#  Copyright (c) Motadata 2022.  All rights reserved.

import argparse
import json
import re
import socket
import time
from base64 import b64encode

from motadatasdk.constants import Constant
from motadatasdk.logger import Logger
from motadatasdk.util import Util


class TopologyPlugin:

    def __init__(self):

        super().__init__()

        self.errors = []

    def run(self, context):

        result = {Constant.STATUS: Constant.STATUS_FAIL, Constant.RESULT: {}}

        # write your code below...

        # after successful execution of the code, make sure that result contains key 'status' with the value 'succeed'
        result[Constant.STATUS] = Constant.STATUS_SUCCEED

        return result


if __name__ == '__main__':

    try:

        parser = argparse.ArgumentParser()

        parser.add_argument("--context")

        args, leftovers = parser.parse_known_args()

        print(b64encode(
            json.dumps(
                TopologyPlugin().run(
                    Util.load_plugin_context(str(args.context)))).encode()).decode(),
              end=Constant.BLANK_STRING)

    except Exception as exception:

        print(b64encode(json.dumps({Constant.STATUS: Constant.STATUS_FAIL,
                                    Constant.ERRORS: [{
                                        Constant.ERROR: str(exception),
                                        Constant.ERROR_CODE: Constant.ERROR_CODE_BAD_REQUEST,
                                        Constant.MESSAGE: "Failed to load plugin Context"}
                                    ]}).encode()).decode(), end=Constant.BLANK_STRING)
