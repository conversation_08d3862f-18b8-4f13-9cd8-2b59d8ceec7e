#  Copyright (c) Motadata 2022. All rights reserved.

import argparse
import json
from base64 import b64encode
from datetime import datetime

import requests
from motadatasdk.constants import Constant
from motadatasdk.logger import Logger
from motadatasdk.util import Util
from requests.exceptions import SSLError
from requests_ntlm import HttpNtlmAuth


class FortinetFirewallPolicyMetricPlugin:

    LOGIN_URL = "https://{}:{}/logincheck/"

    POLICY_URL = "https://{}:{}/api/v2/monitor/firewall/policy"

    ERROR_MESSAGE_INVALID_URL_PRIVILEGE = "HTTP Error, reason: User does not have privileges to access the URL {} on {}"

    def __init__(self):

        super().__init__()

        self._session = None

        self.errors = []

    def collect(self, context):

        result = {Constant.STATUS: Constant.STATUS_FAIL, Constant.RESULT: {}}

        try:

            if context.get(Constant.OBJECT_VENDOR) is not None and context.get(Constant.OBJECT_VENDOR) == Constant.VENDOR_FORTINET:

                client = self._init(context)

                if client is not None:

                    output = self._execute_post_request(
                        self.LOGIN_URL.format(context.get(Constant.OBJECT_IP), context.get(Constant.PORT)),
                        'username={}&secretkey={}&ajax=1'.format(context.get(Constant.USER_NAME),
                                                                 context.get(Constant.PASSWORD)))

                    if output is not None and output.get(
                            Constant.URL_RESPONSE_CODE) == Constant.URL_RESPONSE_CODE_SUCCESS:

                        cookies = output.get("cookies")

                        output = self._execute_get_request(
                            self.POLICY_URL.format(context.get(Constant.OBJECT_IP), context.get(Constant.PORT)),
                            cookies)

                        if output is not None and output.get(
                                Constant.URL_RESPONSE_CODE) == Constant.URL_RESPONSE_CODE_SUCCESS:

                            output = str(output.get(Constant.URL_RESPONSE_CONTENT))

                            output = json.loads(output)

                            if output.get("results") is not None and len(output.get("results")) > 0:

                                policies = []

                                for policy_obj in output.get("results"):

                                    policy = {"policy": str(policy_obj.get("policyid")),
                                              "policy.active.sessions": policy_obj.get("active_sessions"),
                                              "policy.hit.count": policy_obj.get("hit_count"),
                                              "policy.name": str(policy_obj.get("name")).strip()}

                                    if policy_obj.get("from_zone") is not None and len(policy_obj.get("from_zone")) > 0:
                                        policy["policy.from.zone"] = " ".join(
                                            str(zone).strip() for zone in policy_obj.get("from_zone"))

                                    if policy_obj.get("to_zone") is not None and len(policy_obj.get("to_zone")) > 0:
                                        policy["policy.to.zone"] = " ".join(
                                            str(zone).strip() for zone in policy_obj.get("to_zone"))

                                    if policy_obj.get("last_used") is not None and policy_obj.get("last_used") > 0:
                                        policy["policy.last.used"] = str(
                                            datetime.fromtimestamp(int(policy_obj.get("last_used")))).strip()

                                    policies.append(policy)

                                result[Constant.RESULT]["policy"] = policies

                    elif output is not None and output.get(
                            Constant.URL_RESPONSE_CODE) == Constant.URL_RESPONSE_CODE_UNAUTHORIZED:

                        self.errors.append({
                            Constant.MESSAGE: Constant.ERROR_MESSAGE_INVALID_CREDENTIALS.format(
                                context.get(Constant.OBJECT_IP)), Constant.ERROR: str(
                                output.get(Constant.URL_RESPONSE_CONTENT)),
                            Constant.ERROR_CODE: Constant.ERROR_CODE_INVALID_CREDENTIALS})

                    elif output is not None and output.get(
                            Constant.URL_RESPONSE_CODE) == Constant.URL_RESPONSE_CODE_FORBIDDEN:

                        self.errors.append({
                            Constant.MESSAGE: self.ERROR_MESSAGE_INVALID_URL_PRIVILEGE.format(
                                self.LOGIN_URL, context.get(Constant.OBJECT_IP)),
                            Constant.ERROR: str(
                                output.get(Constant.URL_RESPONSE_CONTENT)),
                            Constant.ERROR_CODE: Constant.ERROR_CODE_UNAUTHORIZED_URL_ACCESS})

                if len(result[Constant.RESULT]) == 0 and len(self.errors) == 0:
                    self.errors.append({Constant.MESSAGE: 'Fortinet Management URL is not accessible',
                                                    Constant.ERROR: 'Fortinet Management URL is not accessible',
                                                    Constant.ERROR_CODE: Constant.ERROR_CODE_NO_RESPONSE})

            else:
                result[Constant.STATUS] = Constant.STATUS_FAIL

                result[Constant.ERROR_CODE] = Constant.ERROR_CODE_BAD_REQUEST

                result[Constant.MESSAGE] = "Invalid Vendor Type"

        except Exception as exception:

            self.errors.append({Constant.MESSAGE: str(exception), Constant.ERROR: str(Logger.get_stack_trace()),  Constant.ERROR_CODE: Constant.ERROR_CODE_INTERNAL_ERROR})


        finally:

            if len(self.errors) > 0:
                result[Constant.ERRORS] = self.errors

            if len(result.get(Constant.RESULT)) > 0:
                result[Constant.STATUS] = Constant.STATUS_SUCCEED

        return result

    def _init(self, context):

        try:

            self._session = self._get_http_client(context)

            if self._session is not None:

                return True

        except Exception as exception:

            self.errors.append(
                {Constant.ERROR: str(Logger.get_stack_trace()),  Constant.MESSAGE: str(exception), Constant.ERROR_CODE: Constant.ERROR_CODE_INTERNAL_ERROR})

        return None

    def _get_http_client(self, context):

        session = requests.session()

        try:

            if context.get(Constant.USER_NAME) and context.get(Constant.PASSWORD):

                session.auth = HttpNtlmAuth(username=context.get(Constant.USER_NAME), password=context.get(Constant.PASSWORD))

        except Exception as exception:

            self.errors.append(
                {Constant.ERROR: str(Logger.get_stack_trace()),  Constant.MESSAGE: str(exception), Constant.ERROR_CODE: Constant.ERROR_CODE_INTERNAL_ERROR})
        return session


    def _execute_post_request(self, url, data):

        result = {}

        try:

            response = self._session.post(url, data=data,verify=False)

            if response is not None:

                result[Constant.URL_RESPONSE_CODE] = response.status_code

                result["cookies"] = response.cookies

            return result

        except SSLError as error:

            self.errors.append(
                {Constant.ERROR: str(error), Constant.MESSAGE: str(error), Constant.ERROR_CODE: Constant.ERROR_CODE_INTERNAL_ERROR})

        except Exception as exception:

            self.errors.append(
                {Constant.ERROR: str(Logger.get_stack_trace()),  Constant.MESSAGE: str(exception), Constant.ERROR_CODE: Constant.ERROR_CODE_INTERNAL_ERROR})


    def _execute_get_request(self, url, cookies):

        result = {}

        try:

            response = self._session.get(url,cookies=cookies)

            if response is not None:

                result[Constant.URL_RESPONSE_CODE] = response.status_code

                result[Constant.URL_RESPONSE_CONTENT] = response.content.decode()

            return result

        except SSLError as error:

            self.errors.append(
                {Constant.ERROR: str(error), Constant.MESSAGE: str(error), Constant.ERROR_CODE: Constant.ERROR_CODE_INTERNAL_ERROR})

        except Exception as exception:

            self.errors.append(
                {Constant.ERROR: str(Logger.get_stack_trace()),  Constant.MESSAGE: str(exception), Constant.ERROR_CODE: Constant.ERROR_CODE_INTERNAL_ERROR})


if __name__ == '__main__':

    parser = argparse.ArgumentParser()

    parser.add_argument("--context")

    args, leftovers = parser.parse_known_args()

    print(b64encode(json.dumps(FortinetFirewallPolicyMetricPlugin().collect(Util.load_plugin_context(str(args.context))).encode()).decode())