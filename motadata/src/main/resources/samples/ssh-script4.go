/*
 * Copyright (c) Motadata 2025. All rights reserved.
 */
package main

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"motadatasdk/utils"
	"os"
)

// "command for ssh": "uname -a"

var loggerObj = NewLogger("Cisco ISR CPU/custom-script", "custom-script")

func main() {

	context, err := utils.LoadPluginContext(os.Args[2:][0])

	if err != nil {

		bytes, _ := json.Marshal(MotadataMap{

			consts.Status: consts.StatusFail,

			consts.Errors: []MotadataStringMap{
				{
					consts.ErrorCode: consts.ErrorCodeInternalError,
					consts.Error:     fmt.Sprintf("%v", err),
					consts.Message:   "Failed to load context",
				}},
		})

		fmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)

	} else {

		result := make(MotadataMap)

		run(result, context)

		bytes, err := json.Marshal(result)

		if err != nil {

			bytes, _ = json.Marshal(MotadataMap{

				consts.Status: consts.StatusFail,

				consts.Errors: []MotadataStringMap{
					{
						consts.ErrorCode: consts.ErrorCodeInternalError,
						consts.Error:     fmt.Sprintf("%v", err),
						consts.Message:   "Invalid Result",
					}},
			})
		}

		fmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)
	}
}

/*
		run:
	    strictly follow below result structure:

		result["status"] = StatusSucceed or StatusFail      (status is necessary field)
		result["result"] = map
		map["metric"] = "value"                             (scalar metrics)
		map["instance.data"] = []instanceMap                (instance metrics)

		instanceMap["instance.data"] = ""                   (should contain instance key in every map)
		instanceMap["instance.data.metric"] = ""
*/

func run(result, context MotadataMap) {

	loggerObj.Debug(context.ToJSON())

	output := context.GetMotadataStringValue(consts.Result).TrimSpace()

	result[consts.Status] = consts.StatusFail

	defer func() {

		if r := recover(); r != nil {

			result[consts.Errors] = []MotadataStringMap{
				{
					consts.ErrorCode: consts.ErrorCodeInternalError,
					consts.Message:   "Invalid Result",
					consts.Error:     fmt.Sprintf("%v", r),
				},
			}
		}
	}()

	if output.IsNotEmpty() {

		metrics := make(MotadataMap)

		metrics["system.information"] = output

		result[consts.Status] = consts.StatusSucceed

		result[consts.Result] = metrics

		loggerObj.Debug(MotadataString(fmt.Sprintf("Result %s : %s", context.GetStringValue(consts.ObjectIP), result.GetMapValue(consts.Result))))

	} else {

		result[consts.Errors] = []MotadataStringMap{
			{
				consts.ErrorCode: consts.ErrorCodeNoItemFound,
				consts.Message:   "No Result found",
			},
		}
	}
}
