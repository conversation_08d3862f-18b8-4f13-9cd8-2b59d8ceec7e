/*
 * Copyright (c) Motadata 2025. All rights reserved.
 */

package main

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"motadatasdk/clients/winrmclient"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"motadatasdk/utils"
	"os"
)

var loggerObj = NewLogger("Plugin/Custom", "custom-script") //todo: change module name as your script name

func main() {

	context, err := utils.LoadPluginContext(os.Args[2:][0])

	if err != nil {

		bytes, _ := json.Marshal(MotadataMap{

			consts.Status: consts.StatusFail,

			consts.Errors: []MotadataStringMap{
				{
					consts.ErrorCode: consts.ErrorCodeInternalError,
					consts.Error:     fmt.Sprintf("%v", err),
					consts.Message:   "Failed to load context",
				}},
		})

		fmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)

	} else {

		result := make(MotadataMap)

		run(result, context)

		bytes, err := json.Marshal(result)

		if err != nil {

			bytes, _ = json.Marshal(MotadataMap{

				consts.Status: consts.StatusFail,

				consts.Errors: []MotadataStringMap{
					{
						consts.ErrorCode: consts.ErrorCodeInternalError,
						consts.Error:     fmt.Sprintf("%v", err),
						consts.Message:   "Invalid Result",
					}},
			})
		}

		fmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)
	}
}

/*
			run:
	    strictly follow below result structure:

		result["status"] = StatusSucceed or StatusFail      (status is necessary field)
		result["result"] = map
		map["metric"] = "value"                             (scaler metrics)
		map["instance.data"] = []instanceMap                (instance metrics)

		instanceMap["instance.data"] = ""                   (should contain instance key in every map)
		instanceMap["instance.data.metric"] = ""
*/
func run(result, context MotadataMap) {

	client := &winrmclient.WinRMClient{}

	client.SetContext(context, false, &loggerObj)

	result[consts.Status] = consts.StatusFail

	defer cleanUp(client, result)

	if client.Init() {

		//Write your logic here...
		//Do use loggerObj for Debugging

	}

	if len(client.GetErrors()) > 0 {

		result[consts.Errors] = append(result.GetStringMapSliceValue(consts.Errors), client.GetErrors()...)

	}
}

func cleanUp(client *winrmclient.WinRMClient, result MotadataMap) {

	if r := recover(); r != nil {

		result[consts.Errors] = []MotadataStringMap{
			{
				consts.ErrorCode: consts.ErrorCodeInternalError,
				consts.Message:   "Invalid Result",
				consts.Error:     fmt.Sprintf("%v", r),
			},
		}
	}

	client.Destroy()
}
