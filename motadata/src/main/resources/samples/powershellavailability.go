/*
 * Copyright (c) Motadata 2025. All rights reserved.
 */

package main

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"motadatasdk/consts"
	"motadatasdk/motadatatypes"
	"motadatasdk/utils"
	"os"
)

//"command for powershell": "Get-WmiObject Win32_Computersystem | select-object Domain; echo \"hostname\";hostname"

func main() {

	context, err := utils.LoadPluginContext(os.Args[2:][0])

	if err != nil {

		bytes, _ := json.Marshal(motadatatypes.MotadataMap{

			consts.Status: consts.StatusFail,

			consts.Errors: []motadatatypes.MotadataStringMap{
				{
					consts.ErrorCode: consts.ErrorCodeInternalError,
					consts.Error:     fmt.Sprintf("%v", err),
					consts.Message:   "Failed to load context",
				}},
		})

		fmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)

	} else {

		result := make(motadatatypes.MotadataMap)

		run(result, context)

		bytes, err := json.Marshal(result)

		if err != nil {

			bytes, _ = json.Marshal(motadatatypes.MotadataMap{

				consts.Status: consts.StatusFail,

				consts.Errors: []motadatatypes.MotadataStringMap{
					{
						consts.ErrorCode: consts.ErrorCodeInternalError,
						consts.Error:     fmt.Sprintf("%v", err),
						consts.Message:   "Invalid Result",
					}},
			})
		}

		fmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)
	}
}

func run(result, context motadatatypes.MotadataMap) {

	output := context.GetMotadataStringValue(consts.Result).TrimSpace()

	result[consts.Status] = consts.StatusFail

	defer func() {

		if r := recover(); r != nil {

			result[consts.Errors] = []motadatatypes.MotadataStringMap{
				{
					consts.ErrorCode: consts.ErrorCodeInternalError,
					consts.Message:   "Invalid Result",
					consts.Error:     fmt.Sprintf("%v", r),
				},
			}
		}
	}()

	if output.IsNotEmpty() {

		result[consts.Status] = consts.StatusSucceed

	} else {

		result[consts.Errors] = []motadatatypes.MotadataStringMap{
			{
				consts.ErrorCode: consts.ErrorCodeNoItemFound,
				consts.Message:   "No Result found",
			},
		}
	}
}
