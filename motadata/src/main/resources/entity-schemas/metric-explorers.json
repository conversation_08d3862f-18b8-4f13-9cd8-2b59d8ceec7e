{"entity": "Metric Explorer", "collection": "metric.explorer", "version": "1.0", "author": "<PERSON><PERSON><PERSON>", "props": [{"name": "metric.explorer.name", "title": "Metric Explorer Name", "type": "string", "rules": ["required", "unique"]}, {"name": "metric.explorer.description", "title": "Metric Explorer Description", "type": "string"}, {"name": "metric.explorer.access.type", "title": "Security", "type": "string", "rules": ["required"], "value": ["private", "public"]}, {"name": "metric.explorer.users", "title": "Users", "type": "list"}, {"name": "metric.explorer.global.view.enabled", "title": "Metric Explorer Global View Enable", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "metric.explorer.object.type", "title": "Metric Explorer Object Type", "type": "string"}, {"name": "metric.explorer.object.id", "title": "Metric Explorer Object id", "type": "numeric"}, {"name": "metric.explorer.context", "title": "Metric Explorer Context", "type": "map", "rules": ["required"]}]}