{"entity": "My Account", "collection": "user", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "user.name", "title": "User Name", "type": "string", "rules": ["required", "unique"]}, {"name": "user.type", "title": "User Type", "type": "string", "default": "System", "private": true}, {"name": "user.first.name", "title": "First Name", "type": "string", "rules": ["required"]}, {"name": "user.last.name", "title": "Last Name", "type": "string", "rules": ["required"]}, {"name": "user.email", "title": "Email", "type": "string", "rules": ["required"]}, {"name": "user.mobile", "title": "Mobile", "type": "numeric", "rules": ["required"]}, {"name": "user.password", "title": "Password", "type": "password", "rules": ["required"]}, {"name": "user.status", "title": "Status", "type": "string", "rules": ["required"]}, {"name": "user.role", "title": "User Role", "type": "numeric", "rules": ["required"]}, {"name": "user.groups", "title": "Group(s)", "type": "list", "rules": ["required"]}]}