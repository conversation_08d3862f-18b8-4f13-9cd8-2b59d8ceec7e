{"entity": "<PERSON><PERSON>", "collection": "metric.plugin", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "metric.plugin.name", "title": "Metric Name", "type": "string", "rules": ["required", "unique"]}, {"name": "metric.plugin.type", "title": "Metric Type", "type": "string", "rules": ["required"]}, {"name": "metric.plugin.entity.type", "title": "Entity Type", "type": "string", "rules": ["required"], "values": ["Monitor", "Group"]}, {"name": "metric.plugin.entities", "title": "Monitor(s)/Group(s)", "type": "list", "rules": ["required"]}, {"name": "metric.plugin.protocol", "title": "Protocol", "type": "string", "rules": ["required"], "values": ["custom", "ssh", "snmp", "powershell", "database", "http"]}, {"name": "metric.plugin.variables", "title": "Variable(s)", "type": "map"}], "entries": [{"type": "script", "directory": "metric", "records": [{"metric.plugin.name": "Cisco ASA Firewall Policy", "metric.plugin.vendor": "Cisco Systems", "metric.plugin.type": "Firewall", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "cisco_asa_firewall_policy.py", "script.language": "python", "script.protocol": "SSH", "port": 22}, "id": 10000000000001}, {"metric.plugin.name": "Cisco ASA Firewall Interface Security", "metric.plugin.vendor": "Cisco Systems", "metric.plugin.type": "Firewall", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "cisco_asa_firewall_interface_security.py", "script.language": "python", "script.protocol": "SSH", "port": 22}, "id": 10000000000002}, {"metric.plugin.name": "Fortinet Firewall Policy", "metric.plugin.vendor": "Fortinet", "metric.plugin.type": "Firewall", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "fortinet_firewall_policy.py", "script.language": "python", "script.protocol": "HTTP/HTTPS", "port": 44333}, "id": 10000000000003}, {"metric.plugin.name": "Juniper Switch", "metric.plugin.vendor": "Juniper Networks", "metric.plugin.type": "Switch", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "juniper_switch_metric.py", "script.language": "python", "script.protocol": "SSH", "port": 22}, "id": 10000000000004}, {"metric.plugin.name": "Juniper Switch STP", "metric.plugin.vendor": "Juniper Networks", "metric.plugin.type": "Switch", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "juniper_switch_stp.py", "script.language": "python", "script.protocol": "SSH", "port": 22}, "id": 10000000000005}, {"metric.plugin.name": "Palo Alto Firewall", "metric.plugin.vendor": "Palo Alto Networks", "metric.plugin.type": "Firewall", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "palo_alto_firewall_metric.py", "script.language": "python", "script.protocol": "SSH", "port": 22}, "id": 10000000000006}, {"metric.plugin.name": "Palo Alto Firewall Policy", "metric.plugin.vendor": "Palo Alto Networks", "metric.plugin.type": "Firewall", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "palo_alto_firewall_policy.py", "script.language": "python", "script.protocol": "HTTP/HTTPS"}, "id": 10000000000007}, {"metric.plugin.name": "Palo Alto Remote VPN", "metric.plugin.vendor": "Palo Alto Networks", "metric.plugin.type": "Firewall", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "palo_alto_firewall_remote_vpn.py", "script.language": "python", "script.protocol": "HTTP/HTTPS"}, "id": 10000000000008}, {"metric.plugin.name": "Palo Alto Site VPN", "metric.plugin.vendor": "Palo Alto Networks", "metric.plugin.type": "Firewall", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "palo_alto_firewall_site_vpn.py", "script.language": "python", "script.protocol": "SSH", "port": 22}, "id": 10000000000009}], "version": "1.1"}, {"type": "script", "directory": "metric", "records": [{"metric.plugin.name": "Custom Temperature Sensor", "metric.plugin.type": "SNMP Device", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "temperaturesensor.go", "script.language": "go", "port": 161}, "id": 10000000000010}, {"metric.plugin.name": "Custom Border Gateway Protocol(BGP)", "metric.plugin.type": "SNMP Device", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "bgp.go", "script.language": "go", "port": 161}, "id": 10000000000011}, {"metric.plugin.name": "Custom Open Shortest Path First Protocol(OSPF)", "metric.plugin.type": "SNMP Device", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "ospf.go", "script.language": "go", "port": 161}, "id": **************}, {"metric.plugin.name": "Custom Intermediate System to Intermediate System Protocol(IS-IS)", "metric.plugin.type": "SNMP Device", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "isis.go", "script.language": "go", "port": 161}, "id": **************}, {"metric.plugin.name": "Custom Remote VPN Connection", "metric.plugin.type": "SNMP Device", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "remotevpnconnection.go", "script.language": "go", "port": 161}, "id": **************}, {"metric.plugin.name": "Custom Routing Protocol", "metric.plugin.type": "SNMP Device", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "routing.go", "script.language": "go", "port": 161}, "id": **************}, {"metric.plugin.name": "Custom Site VPN", "metric.plugin.type": "SNMP Device", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "sitevpn.go", "script.language": "go", "port": 161}, "id": **************}, {"metric.plugin.name": "Custom Spanning Tree Protocol(STP)", "metric.plugin.type": "SNMP Device", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "stp.go", "script.language": "go", "port": 161}, "id": **************}, {"metric.plugin.name": "Custom Switch Port Mapper(SPM)", "metric.plugin.type": "SNMP Device", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "stp.go", "script.language": "go", "port": 161}, "id": **************}, {"metric.plugin.name": "Custom Virtual LAN(VLAN)", "metric.plugin.type": "SNMP Device", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "vlan.go", "script.language": "go", "port": 161}, "id": **************}, {"metric.plugin.name": "Custom Virtual Routing & Forwarding(VRF)", "metric.plugin.type": "SNMP Device", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "vrf.go", "script.language": "go", "port": 161}, "id": **************}, {"metric.plugin.name": "Custom Cisco Hardware Sensor", "metric.plugin.type": "SNMP Device", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "ciscohardwaresensor.go", "script.language": "go", "port": 161}, "id": **************}], "version": "1.2"}, {"type": "script", "directory": "metric", "records": [{"metric.plugin.name": "Custom Cisco Hardware Sensor(SSH)", "metric.plugin.type": "SNMP Device", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "ciscohardwaresensorssh.go", "script.language": "go", "port": 22}, "id": 10000000000022}], "version": "1.3"}, {"type": "script", "directory": "metric", "records": [{"metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "temperaturesensor.go", "script.language": "go", "port": 161}, "id": 10000000000010}, {"metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "bgp.go", "script.language": "go", "port": 161}, "id": 10000000000011}, {"metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "ospf.go", "script.language": "go", "port": 161}, "id": **************}, {"metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "isis.go", "script.language": "go", "port": 161}, "id": **************}, {"metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "remotevpnconnection.go", "script.language": "go", "port": 161}, "id": **************}, {"metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "routing.go", "script.language": "go", "port": 161}, "id": **************}, {"metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "sitevpn.go", "script.language": "go", "port": 161}, "id": **************}, {"metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "stp.go", "script.language": "go", "port": 161}, "id": **************}, {"metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "stp.go", "script.language": "go", "port": 161}, "id": **************}, {"metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "vlan.go", "script.language": "go", "port": 161}, "id": **************}, {"metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "vrf.go", "script.language": "go", "port": 161}, "id": **************}, {"metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "ciscohardwaresensor.go", "script.language": "go", "port": 161}, "id": **************}, {"metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "ciscohardwaresensorssh.go", "script.language": "go", "port": 22}, "id": 10000000000022}, {"metric.plugin.name": "Custom IPSec Tunnel(SSH)", "metric.plugin.type": "Firewall", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "ipsectunnelssh.go", "script.language": "go", "port": 22}, "id": 10000000000023}, {"metric.plugin.name": "Custom Cisco Hardware Sensor(Sh Platform)", "metric.plugin.type": "SNMP Device", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "ciscohardwaresensorshplatform.go", "script.language": "go", "port": 22}, "id": 10000000000024}, {"metric.plugin.name": "Custom MikroTik Gateway(SSH)", "metric.plugin.type": "SNMP Device", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"metric.plugin": "custom", "script": "mikrotikwangatewayssh.go", "script.language": "go", "port": 22}, "id": 10000000000025}], "version": "1.4"}]}