{"entity": "SNMP Trap Forwarder", "collection": "snmp.trap.forwarder", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "snmp.trap.forwarder.name", "title": "Trap Forwarder Name", "type": "string", "rules": ["required", "unique"]}, {"name": "snmp.trap.forwarder.traps", "title": "Trap Profile(s)", "type": "list", "rules": ["required"]}, {"name": "snmp.trap.forwarder.destination", "title": "Forwarder IP/Host", "type": "string", "rules": ["required"]}, {"name": "snmp.trap.forwarder.port", "title": "Forwarder Port", "type": "numeric", "rules": ["required"]}]}