{"entity": "Log Collector", "collection": "log.collector", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "log.collector.name", "title": "Profile Name", "type": "string", "rules": ["required", "unique"]}, {"name": "log.collector.description", "title": "Description", "type": "string"}, {"name": "log.collector.type", "title": "Collection Type", "type": "string", "rules": ["required"]}, {"name": "log.collector.log.parser", "title": "Log Parser", "type": "list"}, {"name": "log.collector.runbook", "title": "Runbook", "type": "numeric", "rules": ["required"]}, {"name": "log.collector.interval", "title": "Collection interval", "type": "numeric", "rules": ["required"]}, {"name": "log.collector.timeout", "title": "Timeout (sec)", "type": "numeric"}]}