{"entity": "Custom Monitoring Field", "collection": "custom.monitoring.field", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "custom.monitoring.field.name", "title": "Field Name", "type": "string", "rules": ["required", "unique"]}], "entries": [{"type": "inline", "records": [{"custom.monitoring.field.name": "Location", "id": 10000000000001}, {"custom.monitoring.field.name": "Floor", "id": 10000000000002}, {"custom.monitoring.field.name": "Serial Number", "id": 10000000000003}, {"custom.monitoring.field.name": "Owner", "id": 10000000000004}, {"custom.monitoring.field.name": "Environment", "id": 10000000000005}, {"custom.monitoring.field.name": "Field 6", "id": 10000000000006}, {"custom.monitoring.field.name": "Field 7", "id": 10000000000007}, {"custom.monitoring.field.name": "Field 8", "id": 10000000000008}, {"custom.monitoring.field.name": "Field 9", "id": 10000000000009}, {"custom.monitoring.field.name": "Field 10", "id": 10000000000010}], "version": "1.0"}]}