{"entity": "Tag Rule", "collection": "tag.rule", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "tag.rule.name", "title": "Rule Name", "type": "string", "rules": ["required", "unique"]}, {"name": "tag.rule.description", "title": "Rule Description", "type": "string"}, {"name": "tag.rule.type", "title": "Rule Type", "type": "string", "rules": ["required"]}, {"name": "tag.rule.operation", "title": "Rule Operation", "type": "string"}, {"name": "tag.rule.tags", "title": "Rule Tags", "type": "list"}, {"name": "tag.rule.context", "title": "Rule Context", "type": "map"}], "entries": [{"type": "inline", "records": [], "version": "1.0"}]}