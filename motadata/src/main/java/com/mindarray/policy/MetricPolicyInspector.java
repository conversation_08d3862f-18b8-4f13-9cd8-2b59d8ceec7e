 /*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

 /*
	Change Logs:
	Date			Author			    Notes
	24-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
	4-Apr-2025		Sankalp     		MOTADATA-5565: poll result will be sent in context to be used as macros in policy message
*/

 package com.mindarray.policy;

 import com.mindarray.Bootstrap;
 import com.mindarray.GlobalConstants;
 import com.mindarray.aiops.AIOpsConstants;
 import com.mindarray.api.AIOpsObject;
 import com.mindarray.api.APIConstants;
 import com.mindarray.api.Metric;
 import com.mindarray.api.Tag;
 import com.mindarray.datastore.DatastoreConstants;
 import com.mindarray.eventbus.EventBusConstants;
 import com.mindarray.eventbus.EventEngine;
 import com.mindarray.nms.NMSConstants;
 import com.mindarray.store.*;
 import com.mindarray.util.*;
 import io.vertx.core.AbstractVerticle;
 import io.vertx.core.Promise;
 import io.vertx.core.buffer.Buffer;
 import io.vertx.core.eventbus.DeliveryOptions;
 import io.vertx.core.json.JsonArray;
 import io.vertx.core.json.JsonObject;

 import java.util.*;
 import java.util.concurrent.atomic.AtomicInteger;

 import static com.mindarray.GlobalConstants.*;
 import static com.mindarray.aiops.AIOpsConstants.*;
 import static com.mindarray.api.APIConstants.ENTITY_ID;
 import static com.mindarray.api.MetricPolicy.*;
 import static com.mindarray.api.User.USER_NAME;
 import static com.mindarray.eventbus.EventBusConstants.*;
 import static com.mindarray.nms.NMSConstants.METRIC_INSTANCES;
 import static com.mindarray.policy.PolicyEngineConstants.*;

 /**
  * MetricPolicyInspector is responsible for evaluating metrics against defined policies.
  * <p>
  * This class:
  * 1. Loads and manages metric policies from the configuration store
  * 2. Assigns policies to objects, groups, and tags
  * 3. Inspects incoming metrics against applicable policies
  * 4. Triggers actions when policy conditions are met
  * 5. Handles policy flaps (state changes) and manages policy lifecycle
  * 6. Coordinates with the correlation engine for availability policies
  * <p>
  * The inspector runs as a Vert.x verticle and communicates with other components
  * through the event bus.
  */
 public class MetricPolicyInspector extends AbstractVerticle
 {
     private static final Logger LOGGER = new Logger(MetricPolicyInspector.class, GlobalConstants.MOTADATA_POLICY, "Metric Policy Inspector");

     private static final DeliveryOptions DELIVERY_OPTIONS = new DeliveryOptions().setSendTimeout(400000L);
     private static final int INTERVAL_SECONDS = MotadataConfigUtil.getPolicyCleanupTimerSeconds();
     private static final boolean CORRELATION_ENABLED = MotadataConfigUtil.correlationEnabled();
     private static final int SETUP_TIMER_SECONDS = MotadataConfigUtil.getPolicySetupTimerSeconds();
     private final Map<Long, JsonObject> policies = new HashMap<>();
     private final Map<String, JsonObject> policyTimerContexts = new HashMap<>();
     private final Map<String, JsonObject> policyTriggerContexts = new HashMap<>();
     private final Map<Long, Set<Long>> suppressedPolicies = new HashMap<>();
     private final Map<String, JsonObject> triggeredPolicies = new HashMap<>();
     private final Map<Long, Map<String, List<Long>>> policiesByObject = new HashMap<>();
     private final Map<Long, Map<String, List<Long>>> policiesByGroup = new HashMap<>();
     private final Map<Long, Map<String, List<Long>>> policiesByTag = new HashMap<>();
     private final JsonObject columns = new JsonObject();
     private final Set<String> qualifiedPolicyTypes = Set.of(PolicyType.AVAILABILITY.getName(), PolicyType.STATIC.getName());
     private final StringBuilder builder = new StringBuilder(0);
     private EventEngine eventEngine;
     private boolean policyTimerEnabled = false;
     private Set<String> mappers;

     @Override
     public void start(Promise<Void> promise) throws Exception
     {
         mappers = new HashSet<>();

         vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_COLUMN_MAPPER_QUERY, EMPTY_VALUE, reply ->
         {
             columns.mergeIn(reply.result().body().getJsonObject(DatastoreConstants.METRIC_COLUMNS));

             var items = MetricPolicyConfigStore.getStore().getItems();

             try
             {
                 for (var index = 0; index < items.size(); index++)
                 {
                     var policy = items.getJsonObject(index);

                     if (qualifiedPolicyTypes.contains(policy.getString(POLICY_TYPE)) && (!policy.containsKey(POLICY_ARCHIVED) || policy.getString(POLICY_ARCHIVED).equalsIgnoreCase(NO)))
                     {
                         var policyContext = policy.getJsonObject(POLICY_CONTEXT);

                         if (policyContext.containsKey(ENTITY_TYPE) && policyContext.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG))
                         {
                             policy.put(POLICY_CONTEXT, policyContext.put(ENTITIES, TagConfigStore.getStore().getIdsByItems(policyContext.getJsonArray(ENTITIES))));
                         }

                         policies.put(policy.getLong(ID), policy);
                     }
                 }

                 assign();
             }

             catch (Exception exception)
             {
                 LOGGER.error(exception);
             }
         });

         vertx.eventBus().<JsonObject>localConsumer(EVENT_COLUMN_MAPPER_UPDATE, message -> updateColumnMapper(message.body()));

         var setupTimer = new AtomicInteger(SETUP_TIMER_SECONDS);

         vertx.setPeriodic(INTERVAL_SECONDS * 1000L, timer ->
         {
             try
             {
                 if (policyTimerEnabled)//now once policy timer enabled after that will be running assign policy to objects..
                 {
                     setupTimer.set(setupTimer.get() - 10);

                     if (setupTimer.get() <= 0)
                     {
                         assign();

                         policyTimerEnabled = false;

                         setupTimer.set(SETUP_TIMER_SECONDS);
                     }
                 }

                 policyTimerContexts.forEach((key, value) ->
                 {
                     if (value.containsKey(POLICY_AUTO_CLEAR_TIMER_SECONDS))
                     {
                         value.put(POLICY_AUTO_CLEAR_TIMER_SECONDS, value.getInteger(POLICY_AUTO_CLEAR_TIMER_SECONDS) - INTERVAL_SECONDS);

                         if (value.getInteger(POLICY_AUTO_CLEAR_TIMER_SECONDS) <= 0)
                         {
                             PolicyEngineConstants.processPolicyFlap(value.put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()).put(SEVERITY, Severity.CLEAR.name()).put(VALUE, "Auto Clear"), policies.get(value.getLong(ID)), true, triggeredPolicies, policyTimerContexts, LOGGER, mappers, builder);
                         }
                     }

                     if (value.containsKey(POLICY_ACTIONS) && !value.getJsonObject(POLICY_ACTIONS).isEmpty()
                             && value.getJsonObject(POLICY_ACTIONS).containsKey(PolicyTriggerActionType.RENOTIFICATION.getName())
                             && !value.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.RENOTIFICATION.getName()).isEmpty())
                     {
                         var context = value.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.RENOTIFICATION.getName());

                         for (var severity : Severity.values())
                         {
                             if (context.containsKey(severity.name()))
                             {
                                 context.getJsonObject(severity.name()).put(PolicyEngineConstants.TIMER_SECONDS, context.getJsonObject(severity.name()).getInteger(PolicyEngineConstants.TIMER_SECONDS) - INTERVAL_SECONDS);
                             }
                         }
                     }

                     if (value.containsKey(POLICY_UNPROVISION_OBJECT_TIMER_SECONDS))
                     {
                         value.put(POLICY_UNPROVISION_OBJECT_TIMER_SECONDS, value.getInteger(POLICY_UNPROVISION_OBJECT_TIMER_SECONDS) - INTERVAL_SECONDS);

                         if (value.getInteger(POLICY_UNPROVISION_OBJECT_TIMER_SECONDS) <= 0 && triggeredPolicies.get(key) != null)
                         {
                             var context = triggeredPolicies.get(key);

                             // unprovision instance
                             if (key.contains(INSTANCE_SEPARATOR))
                             {
                                 LOGGER.info(String.format("unprovisioning instance %s of monitor %s", context.getString(INSTANCE), context.getString(AIOpsObject.OBJECT_NAME)));

                                 Bootstrap.vertx().eventBus().send(EVENT_METRIC_INSTANCE_UNPROVISION, new JsonObject().put(ID, context.getLong("metric.id")).put(METRIC_INSTANCES, new JsonArray().add(context.getString(INSTANCE))));
                             }
                             else
                             {
                                 // unprovision monitor

                                 LOGGER.info(String.format("unprovisioning monitor %s", context.getString(AIOpsObject.OBJECT_NAME)));

                                 Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_OBJECT_UNPROVISION, ObjectConfigStore.getStore().getItem(CommonUtil.getLong(key.split(SEPARATOR_WITH_ESCAPE)[0]))
                                         .put(REMOTE_ADDRESS, SYSTEM_REMOTE_ADDRESS).put(USER_NAME, DEFAULT_USER));
                             }
                         }
                     }
                 });
             }
             catch (Exception exception)
             {
                 LOGGER.error(exception);
             }
         });

         vertx.eventBus().<JsonObject>localConsumer(EVENT_METRIC_POLICY_CLEAR, message ->
         {
             try
             {
                 var event = message.body();

                 if (CommonUtil.traceEnabled())
                 {
                     LOGGER.trace(String.format("policy clear request received : %s ", event.encode()));
                 }

                 if (message.body() != null && !event.isEmpty() && policies.containsKey(event.getLong(POLICY_ID)))
                 {
                     var key = event.containsKey(INSTANCE) ? event.getLong(ENTITY_ID) + SEPARATOR + policies.get(event.getLong(POLICY_ID)).getString(POLICY_TYPE) + SEPARATOR + event.getString(METRIC) + SEPARATOR + event.getValue(INSTANCE) : event.getLong(ENTITY_ID) + SEPARATOR + policies.get(event.getLong(POLICY_ID)).getString(POLICY_TYPE) + SEPARATOR + event.getString(METRIC);

                     if (triggeredPolicies.containsKey(key))
                     {
                         PolicyEngineConstants.processPolicyFlap(new JsonObject().mergeIn(triggeredPolicies.get(key)).put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()).put(SEVERITY, Severity.CLEAR.name()).put(VALUE, "Manual Clear"), policies.get(event.getLong(POLICY_ID)), false, triggeredPolicies, policyTimerContexts, LOGGER, mappers, builder);
                     }
                 }
             }
             catch (Exception exception)
             {
                 LOGGER.error(exception);
             }
         });

         vertx.eventBus().<JsonObject>localConsumer(EVENT_METRIC_POLICY_SUPPRESS, message ->
         {
             try
             {
                 var event = message.body();

                 message.reply(message.body() != null && suppressedPolicies.containsKey(event.getLong(ENTITY_ID)) && suppressedPolicies.get(event.getLong(ENTITY_ID)).contains(event.getLong(POLICY_ID)));
             }
             catch (Exception exception)
             {
                 LOGGER.error(exception);
             }
         });

         vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
         {
             try
             {
                 var event = message.body();

                 switch (ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)))
                 {
                     case ADD_POLICY, UPDATE_POLICY ->
                     {
                         var policy = MetricPolicyConfigStore.getStore().getItem(event.getLong(ID));

                         if (policy != null && qualifiedPolicyTypes.contains(policy.getString(POLICY_TYPE)))
                         {
                             var policyContext = policy.getJsonObject(POLICY_CONTEXT);

                             if (policyContext.containsKey(ENTITY_TYPE) && policyContext.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG))
                             {
                                 policy.put(POLICY_CONTEXT, policyContext.put(ENTITIES, TagConfigStore.getStore().getIdsByItems(policyContext.getJsonArray(ENTITIES))));
                             }

                             policies.put(event.getLong(ID), policy);

                             policyTimerContexts.values().removeIf(item -> item != null && !item.isEmpty() && item.containsKey(ID) && item.getLong(ID).equals(event.getLong(ID)));

                             assign();
                         }
                     }

                     case DELETE_POLICY ->
                     {
                         policies.remove(event.getLong(ID));

                         policiesByObject.values().forEach(items ->
                         {
                             for (var objectPolicies : items.values())
                             {
                                 objectPolicies.remove(event.getLong(ID));
                             }
                         });

                         policiesByGroup.values().forEach(items ->
                         {
                             for (var groupPolicies : items.values())
                             {
                                 groupPolicies.remove(event.getLong(ID));
                             }
                         });

                         policiesByTag.values().forEach(items ->
                         {
                             for (var tagPolicies : items.values())
                             {
                                 tagPolicies.remove(event.getLong(ID));
                             }
                         });

                         suppressedPolicies.values().removeIf(item -> item.contains(event.getLong(ID)));

                         triggeredPolicies.values().removeIf(item -> item.getLong(ID).equals(event.getLong(ID)));

                         policyTriggerContexts.values().removeIf(item -> item.getLong(ID).equals(event.getLong(ID)));

                         policyTimerContexts.values().removeIf(item -> item != null && !item.isEmpty() && item.containsKey(ID) && item.getLong(ID).equals(event.getLong(ID)));

                         MetricPolicyCacheStore.getStore().cleanup(event.getLong(ID));

                         sort();
                     }

                     case DELETE_OBJECT ->
                     {
                         if (!event.containsKey(METRIC_INSTANCES) || event.getValue(METRIC_INSTANCES) == null)
                         {
                             policiesByObject.remove(event.getLong(ID));

                             suppressedPolicies.remove(event.getLong(ID));

                             triggeredPolicies.entrySet().removeIf(entrySet -> CommonUtil.getLong(entrySet.getKey().split(SEPARATOR_WITH_ESCAPE)[0].trim()) == event.getLong(ID));

                             policyTimerContexts.entrySet().removeIf(entrySet -> CommonUtil.getLong(entrySet.getKey().split(SEPARATOR_WITH_ESCAPE)[0].trim()) == event.getLong(ID));

                             policyTriggerContexts.entrySet().removeIf(entrySet -> CommonUtil.getLong(entrySet.getKey().split(SEPARATOR_WITH_ESCAPE)[0].trim()) == event.getLong(ID));

                             MetricPolicyCacheStore.getStore().deleteItem(event.getLong(ID));
                         }
                         else
                         {
                             // delete instances post operations
                             var id = event.getLong(ID);

                             var instances = event.getJsonArray(METRIC_INSTANCES);

                             for (var index = 0; index < instances.size(); index++)
                             {
                                 var instance = instances.getString(index);

                                 var policyKey = triggeredPolicies.keySet().stream().filter(key -> key.split(SEPARATOR_WITH_ESCAPE).length == 4 && key.startsWith(CommonUtil.getString(id)) && key.split(SEPARATOR_WITH_ESCAPE)[3].trim().equalsIgnoreCase(instance)).findFirst().orElse(null);

                                 if (CommonUtil.isNotNullOrEmpty(policyKey))
                                 {
                                     triggeredPolicies.remove(policyKey);

                                     policyTimerContexts.remove(policyKey);

                                     policyTriggerContexts.remove(policyKey);

                                     MetricPolicyCacheStore.getStore().deleteItem(id, instance, policyKey);
                                 }
                             }
                         }
                     }

                     case ADD_METRIC -> enablePolicyAssignTimer();

                     case SUPPRESS_POLICY ->
                     {
                         if (event.containsKey(ENTITY_ID) && policies.containsKey(event.getLong(POLICY_ID)))
                         {
                             var objectId = event.getLong(ENTITY_ID);

                             if (!suppressedPolicies.containsKey(objectId))
                             {
                                 suppressedPolicies.put(objectId, new HashSet<>());
                             }

                             suppressedPolicies.get(objectId).add(event.getLong(POLICY_ID));
                         }

                     }

                     case UNSUPPRESS_POLICY ->
                     {
                         if (event.containsKey(ENTITY_ID) && suppressedPolicies.containsKey(event.getLong(ENTITY_ID)))
                         {
                             var objectId = event.getLong(ENTITY_ID);

                             suppressedPolicies.get(objectId).remove(event.getLong(POLICY_ID));

                             if (suppressedPolicies.get(objectId).isEmpty())
                             {
                                 suppressedPolicies.remove(objectId);
                             }
                         }
                     }

                     default ->
                     {
                     }

                 }
             }
             catch (Exception exception)
             {
                 LOGGER.error(exception);
             }

         }).exceptionHandler(LOGGER::error);

         vertx.eventBus().<JsonObject>localConsumer(EVENT_POLICY_ACTION_TRIGGER, message ->
         {

             var event = message.body();

             if (event != null)
             {
                 writeTriggeredActionEvent(null, event, event.getLong(ENTITY_ID), event.getLong(ID), event.put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.RUNBOOK.getName()), LOGGER, mappers, builder);
             }
             else
             {
                 LOGGER.warn("Invalid context received from integration");
             }

         });

         eventEngine = new EventEngine().setEventType(config().getString(EventBusConstants.EVENT_TYPE))
                 .setPersistEventOffset(true).setLogger(LOGGER).setEventHandler(this::inspect).start(vertx, promise);
     }

     private void enablePolicyAssignTimer()
     {
         //new metric added need to re run policy assigner logic to assign latest policy to object after default policy timer seconds policy will be applied..
         policyTimerEnabled = true;
     }

     private void assign(String metric)
     {
         var update = false;

         for (var policy : policies.values())
         {
             if (!policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyEngineConstants.PolicyType.AVAILABILITY.getName()) && policy.getJsonObject(POLICY_CONTEXT).getString(METRIC).equalsIgnoreCase(metric))
             {
                 update = true;
             }
         }

         if (update)
         {
             assign();
         }
     }

     /**
      * Assigns policies to objects, groups, and tags based on policy configurations.
      * This method builds the mapping between policies and the entities they apply to,
      * which is essential for efficient policy evaluation.
      */
     private void assign()
     {
         try
         {
             policiesByObject.clear();

             policiesByGroup.clear();

             policiesByTag.clear();

             policies.forEach((key, value) ->
             {
                 var availabilityPolicy = value.getString(POLICY_TYPE).equalsIgnoreCase(PolicyEngineConstants.PolicyType.AVAILABILITY.getName());

                 var metric = "";

                 if (availabilityPolicy)
                 {
                     metric = (value.getJsonObject(POLICY_CONTEXT).getString(METRIC) != null ? value.getString(POLICY_TYPE) + SEPARATOR + value.getJsonObject(POLICY_CONTEXT).getString(METRIC) : value.getString(POLICY_TYPE) + SEPARATOR + STATUS);
                 }
                 else
                 {
                     metric = value.getString(POLICY_TYPE) + SEPARATOR + value.getJsonObject(POLICY_CONTEXT).getString(METRIC);
                 }

                 JsonArray entities;

                 var policyByObject = true;

                 var policyByTag = true;

                 var policyContext = value.getJsonObject(POLICY_CONTEXT);

                 if (policyContext.getJsonArray(ENTITIES) != null && !policyContext.getJsonArray(ENTITIES).isEmpty())//if policy contains filter of group or monitor so just assigning that filtered entities or groups
                 {
                     entities = policyContext.getJsonArray(ENTITIES);

                     if (policyContext.getString(ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.GROUP.getName()))
                     {
                         policyByObject = false;

                         policyByTag = false;
                     }
                     else if (policyContext.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG))
                     {
                         policyByObject = false;
                     }
                 }
                 else
                 {
                     if (value.getString(POLICY_TYPE).equalsIgnoreCase(PolicyEngineConstants.PolicyType.AVAILABILITY.getName()))
                     {
                         entities = ObjectConfigStore.getStore().getItemsByPlugin(new JsonArray().add(ObjectManagerCacheStore.getStore().getPluginIdByMetricPlugin(NMSConstants.MetricPlugin.AVAILABILITY.getName())), true);
                     }

                     else
                     {
                         if (!columns.isEmpty() && columns.containsKey(policyContext.getString(METRIC)))
                         {
                             entities = ObjectConfigStore.getStore().getItemsByPlugin(columns.getJsonObject(policyContext.getString(METRIC)).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS), true);
                         }

                         else entities = new JsonArray();
                     }
                 }

                 assign(value.getLong(ID), metric, entities, policyByObject ? policiesByObject : policyByTag ? policiesByTag : policiesByGroup);
             });

             sort();
         }
         catch (Exception exception)
         {
             LOGGER.error(exception);
         }
     }

     //according to entitytype selected monitor or group will be assigning policies accordingly
     private void assign(long policyId, String metricKey, JsonArray entities, Map<Long, Map<String, List<Long>>> policiesByEntity)
     {
         try
         {
             for (var i = 0; i < entities.size(); i++)
             {
                 var object = entities.getLong(i);

                 if (!policiesByEntity.containsKey(object))
                 {
                     policiesByEntity.put(object, new HashMap<>());

                     policiesByEntity.get(object).put(metricKey, new ArrayList<>());
                 }

                 else if (!policiesByEntity.get(object).containsKey(metricKey))
                 {
                     policiesByEntity.get(object).put(metricKey, new ArrayList<>());
                 }

                 if (!policiesByEntity.get(object).get(metricKey).contains(policyId))
                 {
                     policiesByEntity.get(object).get(metricKey).add(policyId);
                 }
             }
         }

         catch (Exception exception)
         {
             LOGGER.error(exception);
         }
     }

     // will be sorting all policies according to metric with its last created or updated time in reverse order or descending so that only last one needs to be evaluated..
     private void sort()
     {
         try
         {
             for (var values : policiesByObject.values())
             {
                 for (var entry : values.entrySet())
                 {
                     entry.getValue().sort(Comparator.comparing(item -> this.policies.get(CommonUtil.getLong(item)).getLong(POLICY_CREATION_TIME)).reversed());
                 }
             }

             for (var values : policiesByGroup.values())
             {
                 for (var entry : values.entrySet())
                 {
                     entry.getValue().sort(Comparator.comparing(item -> this.policies.get(CommonUtil.getLong(item)).getLong(POLICY_CREATION_TIME)).reversed());
                 }
             }

             for (var values : policiesByTag.values())
             {
                 for (var entry : values.entrySet())
                 {
                     entry.getValue().sort(Comparator.comparing(item -> this.policies.get(CommonUtil.getLong(item)).getLong(POLICY_CREATION_TIME)).reversed());
                 }
             }
         }
         catch (Exception exception)
         {
             LOGGER.error(exception);
         }
     }

     /**
      * Inspects incoming metric events against applicable policies.
      * This is the core method that evaluates metrics and triggers policy actions when conditions are met.
      *
      * @param event The metric event to inspect, containing metric values and metadata
      */
     private void inspect(JsonObject event)
     {
         try
         {

             if (event.containsKey(CORRELATION_PROBE))
             {
                 PolicyEngineConstants.processPolicyFlap(event, policies.get(event.getLong(ID)), false, triggeredPolicies, policyTimerContexts, LOGGER, mappers, builder);
             }

             else if (event.getJsonObject(RESULT) != null && !event.getJsonObject(RESULT).isEmpty())
             {
                 var result = event.getJsonObject(RESULT);

                 var contexts = new HashMap<String, List<JsonObject>>(1);

                 var context = new JsonObject().put(AIOpsObject.OBJECT_ID, event.getInteger(AIOpsObject.OBJECT_ID)).put(Metric.METRIC_TYPE, event.getString(Metric.METRIC_TYPE)).put(EVENT_TIMESTAMP, event.containsKey(EventBusConstants.EVENT_TIMESTAMP) ? event.getLong(EventBusConstants.EVENT_TIMESTAMP) : DateTimeUtil.currentSeconds()).put(AIOpsObject.OBJECT_CATEGORY, event.containsKey(Metric.METRIC_CATEGORY) ? event.getString(Metric.METRIC_CATEGORY) : event.getString(AIOpsObject.OBJECT_CATEGORY)).put(GlobalConstants.PLUGIN_ID, event.getInteger(GlobalConstants.PLUGIN_ID)).put(ENTITY_ID, event.getLong(Metric.METRIC_OBJECT)).put("metric.id", event.getLong(ID));

                 var item = ObjectConfigStore.getStore().getItem(event.getLong(Metric.METRIC_OBJECT));

                 if (item != null)
                 {
                     qualify(event.getJsonArray(AIOpsObject.OBJECT_GROUPS, new JsonArray()), event.getJsonArray(TAGS), event.getLong(Metric.METRIC_OBJECT), policies, policiesByObject, policiesByGroup, policiesByTag).forEach((key, value) ->
                     {
                         try
                         {
                             if (!value.isEmpty())
                             {
                                 var metric = key.split(SEPARATOR_WITH_ESCAPE)[1];

                                 var policyId = value.stream().findFirst().get();

                                 var policy = policies.get(policyId);

                                 if (policy.getString(POLICY_STATE).equalsIgnoreCase(YES) && (!suppressedPolicies.containsKey(event.getLong(Metric.METRIC_OBJECT)) || (suppressedPolicies.containsKey(event.getLong(Metric.METRIC_OBJECT)) && !suppressedPolicies.get(event.getLong(Metric.METRIC_OBJECT)).contains(policyId))))
                                 {
                                     var policyContext = policy.getJsonObject(POLICY_CONTEXT);

                                     context.put(GlobalConstants.METRIC, metric).put(ID, policyId).put(POLICY_TYPE, policy.getString(POLICY_TYPE));

                                     //for instance level alerts we will be keeping track using instance.type key
                                     if (metric.contains(INSTANCE_SEPARATOR) || CommonUtil.isNotNullOrEmpty(policyContext.getString(PolicyEngineConstants.INSTANCE_TYPE)))
                                     {
                                         var instance = metric.contains(INSTANCE_SEPARATOR) ? metric.split(INSTANCE_SEPARATOR)[0] : policyContext.getString(PolicyEngineConstants.INSTANCE_TYPE);

                                         //if result contains that particular instance result then only check condition
                                         if (result.containsKey(instance) && result.getJsonArray(instance) != null && !result.getJsonArray(instance).isEmpty())
                                         {
                                             context.put(INSTANCE, instance);

                                             inspectInstanceMetric(policyContext, result, context.put(POLICY_SEVERITY, policyContext.getJsonObject(POLICY_SEVERITY)), contexts, policy.getString(POLICY_TYPE));
                                         }
                                     }
                                     else
                                     {
                                         context.remove(INSTANCE);

                                         if (result.containsKey(metric))
                                         {
                                             inspectScalarMetric(result, metric, policyContext.getJsonObject(POLICY_SEVERITY), context, contexts, policy.getString(POLICY_TYPE));
                                         }
                                     }
                                 }
                             }
                         }
                         catch (Exception exception)
                         {
                             LOGGER.error(exception);
                         }
                     });
                 }

                 if (!contexts.isEmpty())
                 {
                     removeDuplicateObjects(contexts);
                 }
             }
         }
         catch (Exception exception)
         {
             LOGGER.error(exception);
         }
     }

     /**
      * Inspects scalar metrics against policy conditions.
      * This method evaluates single-value metrics (non-instance metrics) against policy thresholds
      * and triggers appropriate actions when conditions are met.
      *
      * @param result     The result object containing metric values
      * @param metric     The name of the metric being evaluated
      * @param condition  The policy condition to evaluate against
      * @param context    The context containing metadata about the metric and policy
      * @param contexts   Map to store contexts for triggered policies
      * @param policyType The type of policy being evaluated
      */
     private void inspectScalarMetric(JsonObject result, String metric, JsonObject condition, JsonObject context, Map<String, List<JsonObject>> contexts, String policyType)
     {
         var instance = context.getString(INSTANCE, null);

         var metricValue = CommonUtil.getString(result.getValue(metric));

         if (columns.containsKey(metric))
         {
             var categories = columns.getJsonObject(metric).getJsonArray(DatastoreConstants.MAPPER_DATA_CATEGORIES);

             if (categories.contains(CommonUtil.getInteger(DatastoreConstants.DataCategory.FLOAT.getName())))
             {
                 metricValue = CommonUtil.getString(CommonUtil.getDouble(metricValue));
             }
             else if (categories.contains(CommonUtil.getInteger(DatastoreConstants.DataCategory.NUMERIC.getName()))) //if it has decimal number than convert it into integer
             {
                 metricValue = CommonUtil.getString(Math.round(CommonUtil.getDouble(metricValue)));
             }
         }

         result.put(metric, metricValue);

         var output = evaluateCondition(result.getValue(metric), condition, policyType);

         if (output.isEmpty() && !context.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.AVAILABILITY.getName()))
         {
             output = new JsonObject().put(SEVERITY, Severity.CLEAR.name());
         }

         if (!output.isEmpty())
         {
             output.mergeIn(context).remove(POLICY_SEVERITY);

             contexts.computeIfAbsent(metric + SEPARATOR + context.getString(POLICY_TYPE), value -> new ArrayList<>(1)).add(output.put(INSTANCE, instance != null ? CommonUtil.getString(result.getValue(instance)) : null).put(VALUE, result.getValue(metric)).put(INSTANCE_METRIC_CONTEXT, result));
         }
     }

     /**
      * Inspects instance metrics against policy conditions.
      * This method evaluates multi-instance metrics (like interfaces, disks, etc.) against policy thresholds
      * and triggers appropriate actions when conditions are met. It handles filtering of instances
      * based on policy criteria and tag-based policies.
      *
      * @param policyContext The policy context containing filters and conditions
      * @param result        The result object containing instance metric values
      * @param context       The context containing metadata about the metric and policy
      * @param contexts      Map to store contexts for triggered policies
      * @param policyType    The type of policy being evaluated
      */
     private void inspectInstanceMetric(JsonObject policyContext, JsonObject result, JsonObject context, Map<String, List<JsonObject>> contexts, String policyType)
     {
         try
         {
             var entries = result.getJsonArray(context.getString(INSTANCE));

             var filter = policyContext.getJsonObject(FILTERS).getJsonObject(DATA_FILTER, null);

             if (entries != null && !entries.isEmpty())
             {
                 var policyByTag = policyContext.containsKey(ENTITY_TYPE) && policyContext.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG);

                 var entities = policyByTag ? policyContext.getJsonArray(ENTITIES) : null;

                 if (filter != null && !filter.isEmpty())
                 {
                     var conditionGroup = filter.getJsonArray(CONDITION_GROUPS).getJsonObject(0);

                     var operator = conditionGroup.getString(OPERATOR);

                     var conditions = conditionGroup.getJsonArray(CONDITIONS);

                     for (var i = 0; i < entries.size(); i++)
                     {
                         var satisfied = false;

                         var entry = entries.getJsonObject(i);

                         for (var j = 0; j < conditions.size(); j++)
                         {
                             var condition = conditions.getJsonObject(j);

                             var operand = condition.getString(OPERAND).contains(CARET_SEPARATOR) ? condition.getString(OPERAND).split(CARET_SEPARATOR_WITH_ESCAPE)[0] : condition.getString(OPERAND);

                             if (entry.containsKey(operand))
                             {
                                 satisfied = PolicyEngineConstants.evaluateCondition(conditionGroup.getString(FILTER).equalsIgnoreCase("include"), condition.getString(OPERATOR), condition.getValue(VALUE), entry.getValue(operand));

                                 if ((satisfied && operator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName())) || (!satisfied && operator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.AND.getName())))
                                 {
                                     break;
                                 }
                             }
                         }

                         if (satisfied && entry.containsKey(context.getString(GlobalConstants.METRIC)))
                         {
                             var tags = TagCacheStore.getStore().getTags(context.getInteger(AIOpsObject.OBJECT_ID) + KEY_SEPARATOR + context.getString(INSTANCE) + KEY_SEPARATOR + (context.getString(INSTANCE).equalsIgnoreCase(NMSConstants.INTERFACE) ? entry.getString("interface~index") : entry.getString(context.getString(INSTANCE))));

                             if (!policyByTag || (tags != null && entities != null && entities.stream().map(CommonUtil::getLong).anyMatch(tags::contains)))
                             {
                                 inspectScalarMetric(entry, context.getString(GlobalConstants.METRIC), context.getJsonObject(POLICY_SEVERITY), context, contexts, policyType);
                             }
                         }
                     }
                 }

                 else
                 {
                     for (var i = 0; i < entries.size(); i++)
                     {
                         var entry = entries.getJsonObject(i);

                         var tags = TagCacheStore.getStore().getTags(context.getInteger(AIOpsObject.OBJECT_ID) + KEY_SEPARATOR + context.getString(INSTANCE) + KEY_SEPARATOR + (context.getString(INSTANCE).equalsIgnoreCase(NMSConstants.INTERFACE) ? entry.getString("interface~index") : entry.getString(context.getString(INSTANCE))));

                         if (entry.containsKey(context.getString(GlobalConstants.METRIC)) && (!policyByTag || (tags != null && entities != null && entities.stream().map(CommonUtil::getLong).anyMatch(tags::contains))))
                         {

                             inspectScalarMetric(entry, context.getString(GlobalConstants.METRIC), context.getJsonObject(POLICY_SEVERITY), context, contexts, policyType);

                         }
                     }
                 }
             }
         }

         catch (Exception exception)
         {
             LOGGER.error(exception);
         }
     }

     /**
      * Evaluates metric values against policy conditions to determine if a policy should be triggered.
      * Handles different policy types (availability, static) and severity levels.
      *
      * @param value       The metric value to evaluate
      * @param conditions  The policy conditions to evaluate against
      * @param policyType  The type of policy (availability, static)
      * @return A JsonObject containing the evaluation result, including severity and threshold if conditions are met
      */
     private JsonObject evaluateCondition(Object value, JsonObject conditions, String policyType)
     {
         var result = new JsonObject();

         //for availability policies
         if (policyType.equalsIgnoreCase(PolicyType.AVAILABILITY.getName()))
         {
             if (PolicyEngineConstants.evaluateCondition(Boolean.TRUE, Operator.EQUAL.getName(), STATUS_DOWN, value))
             {
                 result.put(SEVERITY, Severity.DOWN.name());

                 result.put(POLICY_THRESHOLD, STATUS_DOWN);
             }

             else if (PolicyEngineConstants.evaluateCondition(Boolean.TRUE, Operator.EQUAL.getName(), STATUS_UP, value))
             {
                 result.put(SEVERITY, Severity.CLEAR.name());

                 result.put(POLICY_THRESHOLD, STATUS_UP);
             }

             else if (PolicyEngineConstants.evaluateCondition(Boolean.TRUE, Operator.EQUAL.getName(), STATUS_UNREACHABLE, value))
             {
                 result.put(SEVERITY, Severity.UNREACHABLE.name());

                 result.put(POLICY_THRESHOLD, STATUS_UNREACHABLE);
             }
         }
         else
         {

             if (conditions != null)
             {
                 for (var index = 0; index < POLICY_SEVERITIES.size(); index++)
                 {
                     var severity = POLICY_SEVERITIES.getString(index);

                     var condition = conditions.getJsonObject(severity, null);

                     if (condition != null && PolicyEngineConstants.evaluateCondition(Boolean.TRUE, condition.getString(POLICY_CONDITION), condition.getValue(POLICY_THRESHOLD), value))
                     {
                         result.put(SEVERITY, Severity.valueOf(severity));

                         result.put(POLICY_THRESHOLD, CommonUtil.getString(condition.getValue(POLICY_THRESHOLD)));

                         break;
                     }
                 }
             }
         }

         return result;
     }

     //if any policy condition is violated or satisfied as per policy configuration than kindly check for occurrence count or trigger time and trigger policy accordingly
     private void updateContext(JsonObject context)
     {
         try
         {

             var policy = policies.get(context.getLong(ID));

             var policyKey = context.getLong(ENTITY_ID) + SEPARATOR + (context.getValue(INSTANCE, null) != null ? context.getString(POLICY_TYPE) + SEPARATOR + context.getString(GlobalConstants.METRIC) + SEPARATOR + context.getValue(INSTANCE) : context.getString(POLICY_TYPE) + SEPARATOR + context.getString(GlobalConstants.METRIC));

             context.put(POLICY_KEY, policyKey);

             if (policy.getJsonObject(POLICY_CONTEXT).getInteger(POLICY_TRIGGER_OCCURRENCES) == null || policy.getJsonObject(POLICY_CONTEXT).getInteger(POLICY_TRIGGER_OCCURRENCES) == 1)
             {
                 if (policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.AVAILABILITY.getName()))
                 {
                     runAvailabilityCorrelation(context, policy);
                 }

                 else
                 {
                     PolicyEngineConstants.processPolicyFlap(context, policy, false, triggeredPolicies, policyTimerContexts, LOGGER, mappers, builder);
                 }
             }
             else
             {
                 if (context.getString(SEVERITY).equalsIgnoreCase(Severity.CLEAR.name()))//if policy gets clear and occurrence count > 1 so removing flap data and clearing the alert
                 {
                     if (policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.AVAILABILITY.getName()))
                     {
                         runAvailabilityCorrelation(context, policy);
                     }

                     else
                     {
                         PolicyEngineConstants.processPolicyFlap(context, policy, false, triggeredPolicies, policyTimerContexts, LOGGER, mappers, builder);
                     }

                     policyTriggerContexts.remove(policyKey);

                 }
                 else
                 {
                     if (policyTriggerContexts.containsKey(policyKey))
                     {
                         var policyTriggerContext = policyTriggerContexts.get(policyKey);

                         if (policyTriggerContext.getString(SEVERITY).equalsIgnoreCase(context.getString(SEVERITY)) && (context.getLong(EVENT_TIMESTAMP) - policyTriggerContext.getLong(POLICY_TRIGGER_TIME)) < policy.getJsonObject(POLICY_CONTEXT).getInteger(POLICY_TRIGGER_TIME))
                         {
                             policyTriggerContext.put(POLICY_TRIGGER_OCCURRENCES, policyTriggerContext.getInteger(POLICY_TRIGGER_OCCURRENCES) + 1);

                             if (policyTriggerContext.getInteger(POLICY_TRIGGER_OCCURRENCES).equals(policy.getJsonObject(POLICY_CONTEXT).getInteger(POLICY_TRIGGER_OCCURRENCES)))
                             {
                                 policyTriggerContexts.remove(policyKey);

                                 if (policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.AVAILABILITY.getName()))
                                 {
                                     runAvailabilityCorrelation(context, policy);
                                 }

                                 else
                                 {
                                     PolicyEngineConstants.processPolicyFlap(context, policy, false, triggeredPolicies, policyTimerContexts, LOGGER, mappers, builder);
                                 }
                             }
                         }

                         else
                         {
                             policyTriggerContexts.remove(policyKey);

                             policyTriggerContexts.computeIfAbsent(policyKey, value -> new JsonObject()).put(POLICY_TRIGGER_TIME, context.getLong(EVENT_TIMESTAMP)).put(POLICY_TRIGGER_OCCURRENCES, 1).put(SEVERITY, context.getString(SEVERITY));
                         }
                     }
                     else
                     {
                         policyTriggerContexts.computeIfAbsent(policyKey, value -> new JsonObject()).put(POLICY_TRIGGER_TIME, context.getLong(EVENT_TIMESTAMP)).put(POLICY_TRIGGER_OCCURRENCES, 1).put(SEVERITY, context.getString(SEVERITY));
                     }
                 }
             }
         }

         catch (Exception exception)
         {

             LOGGER.error(exception);
         }
     }

     private void runAvailabilityCorrelation(JsonObject context, JsonObject policy)
     {
         var severity = context.getString(SEVERITY);

         try
         {
             if (MotadataConfigUtil.devMode() || CORRELATION_ENABLED)
             {
                 if (severity.equalsIgnoreCase(Severity.UNREACHABLE.name()))//as severity unreachable so coming from correlation itself so directly processing it..
                 {
                     PolicyEngineConstants.processPolicyFlap(context, policy, false, triggeredPolicies, policyTimerContexts, LOGGER, mappers, builder);
                 }

                 else if (context.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVER.getName()) || context.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.NETWORK.getName()) || context.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.VIRTUALIZATION.getName()) || context.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.HCI.getName()))
                 {
                     String probeType;

                     var valid = false;

                     if (triggeredPolicies.containsKey(context.getString(POLICY_KEY)))
                     {
                         valid = triggeredPolicies.get(context.getString(POLICY_KEY)).getValue(VALUE).equals(context.getValue(VALUE));
                     }

                     // to avoid duplicate request to run correlation
                     // check if previous policy and current triggered policy have same value then don't send request to run correlation

                     if (!valid)
                     {
                         if (context.getString(INSTANCE) != null)
                         {
                             probeType = severity.equalsIgnoreCase(Severity.DOWN.name()) ? AIOpsConstants.AvailabilityProbeType.INSTANCE_DOWN.getName() : AIOpsConstants.AvailabilityProbeType.INSTANCE_UP.getName();
                         }
                         else
                         {
                             probeType = severity.equalsIgnoreCase(Severity.DOWN.name()) ? AIOpsConstants.AvailabilityProbeType.OBJECT_DOWN.getName() : AIOpsConstants.AvailabilityProbeType.OBJECT_UP.getName();
                         }

                         var object = ObjectConfigStore.getStore().getItem(context.getLong(ENTITY_ID));

                         if (CommonUtil.traceEnabled())
                         {
                             LOGGER.trace(String.format("request send to run correlation for %s probe type %s", object.getString(AIOpsObject.OBJECT_NAME), probeType));
                         }


                         vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_AVAILABILITY_CORRELATION, new JsonObject().put(AIOpsObject.OBJECT_NAME, object.getString(AIOpsObject.OBJECT_NAME)).put(AIOpsObject.OBJECT_TYPE, object.getString(AIOpsObject.OBJECT_TYPE)).put(AIOpsObject.OBJECT_GROUPS, object.getJsonArray(AIOpsObject.OBJECT_GROUPS)).put(AIOpsConstants.CORRELATION_INSTANCE, context.getString(INSTANCE) != null ? context.getString(INSTANCE) : EMPTY_VALUE).put(EventBusConstants.EVENT_REPLY, YES).put(Metric.METRIC_OBJECT, context.getLong(ENTITY_ID)).put(AVAILABILITY_PROBE_TYPE, probeType).put(EVENT_TIMESTAMP, context.getLong(EVENT_TIMESTAMP)).put(AIOpsObject.OBJECT_ID, object.getInteger(AIOpsObject.OBJECT_ID)).put(AIOpsObject.OBJECT_CATEGORY, object.getString(AIOpsObject.OBJECT_CATEGORY))
                                 , DELIVERY_OPTIONS, reply ->
                                 {
                                     if (reply.succeeded())
                                     {
                                         try
                                         {
                                             var event = reply.result().body();

                                             //if status abort from correlation so duplicate request received so received status abort so ignoring it..
                                             if ((!event.containsKey(STATUS) || !event.getString(STATUS).equalsIgnoreCase(STATUS_ABORT)))
                                             {
                                                 var builder = new StringBuilder();

                                                 var unreachableObjects = new JsonArray();

                                                 if (probeType.equalsIgnoreCase(AIOpsConstants.AvailabilityProbeType.OBJECT_DOWN.getName()) && event.containsKey(CORRELATED_DOWN_OBJECTS))
                                                 {
                                                     LOGGER.info(String.format("correlation result probe type %s for %s ", event.getString(AVAILABILITY_PROBE_TYPE), ObjectConfigStore.getStore().getObjectNames(event.getJsonArray(CORRELATED_DOWN_OBJECTS))));

                                                     if (processUnreachableObjects(unreachableObjects, builder, event, context))
                                                     {
                                                         LOGGER.info(String.format("correlated keys are : %s for %s ", builder, ObjectConfigStore.getStore().getObjectNames(event.getJsonArray(CORRELATED_DOWN_OBJECTS))));

                                                         if (event.containsKey(AIOpsConstants.CORRELATED_DOWN_OBJECTS) && !event.getJsonArray(AIOpsConstants.CORRELATED_DOWN_OBJECTS).isEmpty())
                                                         {
                                                             context.put(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS, !builder.isEmpty() ? builder.deleteCharAt(builder.length() - 1).toString() : EMPTY_VALUE);

                                                             context.put(AIOpsConstants.CORRELATION_OBJECTS, event.getJsonArray(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS).size());

                                                             var downContext = new JsonObject().put(SEVERITY, Severity.DOWN.name()).put(METRIC, STATUS).put(POLICY_THRESHOLD, STATUS_DOWN).put(VALUE, STATUS_DOWN);

                                                             var downPolicyKey = EMPTY_VALUE;

                                                             var policyId = 0L;

                                                             for (var downObject : event.getJsonArray(AIOpsConstants.CORRELATED_DOWN_OBJECTS))
                                                             {
                                                                 var item = ObjectConfigStore.getStore().getItem(CommonUtil.getLong(downObject));

                                                                 policyId = qualifyPolicyByMetric(item.getJsonArray(AIOpsObject.OBJECT_GROUPS), item.getLong(ID), item.getJsonArray(AIOpsObject.OBJECT_TAGS));

                                                                 if (policyId > 0)
                                                                 {
                                                                     downPolicyKey = downObject + SEPARATOR + PolicyType.AVAILABILITY.getName() + SEPARATOR + STATUS;

                                                                     downContext.put(CORRELATED_UNREACHABLE_OBJECTS, unreachableObjects).put(POLICY_KEY, downPolicyKey)
                                                                             .put(Metric.METRIC_TYPE, context.getString(Metric.METRIC_TYPE)).put(PLUGIN_ID, context.getInteger(PLUGIN_ID))
                                                                             .put(POLICY_TYPE, context.getString(POLICY_TYPE)).put(CORRELATION_PROBE, YES)
                                                                             .put(EVENT_TIMESTAMP, context.getLong(EVENT_TIMESTAMP)).put(ID, policyId)
                                                                             .put(AIOpsObject.OBJECT_CATEGORY, item.getString(AIOpsObject.OBJECT_CATEGORY)).put(ENTITY_ID, CommonUtil.getLong(downObject))
                                                                             .put(AIOpsObject.OBJECT_NAME, item.getString(AIOpsObject.OBJECT_NAME)).put(AIOpsObject.OBJECT_IP, item.getString(AIOpsObject.OBJECT_IP))
                                                                             .put(AIOpsObject.OBJECT_TYPE, item.getString(AIOpsObject.OBJECT_TYPE)).put(AIOpsObject.OBJECT_ID, item.getInteger(AIOpsObject.OBJECT_ID))
                                                                             .put(AIOpsConstants.CORRELATION_OBJECTS, context.getInteger(AIOpsConstants.CORRELATION_OBJECTS));

                                                                     LOGGER.info(String.format("down object %s for correlated alert", item.getString(AIOpsObject.OBJECT_NAME)));

                                                                     if (context.getLong(ENTITY_ID).equals(downObject))
                                                                     {
                                                                         PolicyEngineConstants.processPolicyFlap(downContext, policy, false, triggeredPolicies, policyTimerContexts, LOGGER, mappers, builder);
                                                                     }
                                                                     else
                                                                     {
                                                                         send(downContext);
                                                                     }
                                                                 }
                                                             }

                                                             writeCorrelationMap(ObjectConfigStore.getStore().getItem(event.getJsonArray(AIOpsConstants.CORRELATED_DOWN_OBJECTS).getLong(0)), event, policyId);
                                                         }
                                                     }
                                                     else
                                                     {
                                                         if (event.containsKey(AIOpsConstants.CORRELATED_DOWN_OBJECTS) && !event.getJsonArray(AIOpsConstants.CORRELATED_DOWN_OBJECTS).isEmpty())
                                                         {
                                                             event.getJsonArray(AIOpsConstants.CORRELATED_DOWN_OBJECTS).forEach(downObject -> PolicyEngineConstants.processPolicyFlap(context, policy, false, triggeredPolicies, policyTimerContexts, LOGGER, mappers, builder));
                                                         }
                                                     }
                                                 }
                                                 else
                                                 {
                                                     LOGGER.info(String.format("correlation result probe type %s for %s ", event.getString(AVAILABILITY_PROBE_TYPE), object.getString(AIOpsObject.OBJECT_NAME)));

                                                     if (probeType.equalsIgnoreCase(AIOpsConstants.AvailabilityProbeType.INSTANCE_DOWN.getName()) && processUnreachableObjects(unreachableObjects, builder, event, context))
                                                     {
                                                         LOGGER.info(String.format("correlated keys are : %s for %s ", builder, object.getString(AIOpsObject.OBJECT_NAME)));

                                                         send(context.put(CORRELATED_UNREACHABLE_OBJECTS, unreachableObjects).put(AIOpsObject.OBJECT_ID, ObjectConfigStore.getStore().getItem(context.getLong(ENTITY_ID)).getInteger(AIOpsObject.OBJECT_ID)).put(CORRELATION_PROBE, YES));

                                                         writeCorrelationMap(ObjectConfigStore.getStore().getItem(context.getLong(ENTITY_ID)), event, context.getLong(ID));
                                                     }
                                                     else
                                                     {
                                                         PolicyEngineConstants.processPolicyFlap(context, policy, false, triggeredPolicies, policyTimerContexts, LOGGER, mappers, builder);
                                                     }
                                                 }
                                             }

                                             // MOTADATA-1166
                                             else if (!triggeredPolicies.containsKey(context.getString(POLICY_KEY)) || (probeType.equalsIgnoreCase(AvailabilityProbeType.OBJECT_UP.getName()) || probeType.equalsIgnoreCase(AvailabilityProbeType.INSTANCE_UP.getName())))//in case when object was unreachable due to down object now that down object is up so from correlation will be getting abort status so when probe type is up will be treating it as normal
                                             {
                                                 PolicyEngineConstants.processPolicyFlap(context, policy, false, triggeredPolicies, policyTimerContexts, LOGGER, mappers, builder);
                                             }
                                         }
                                         catch (Exception exception)
                                         {
                                             LOGGER.error(exception);
                                         }
                                     }
                                     else
                                     {
                                         LOGGER.error(reply.cause());
                                     }
                                 });
                     }
                     else
                     {
                         PolicyEngineConstants.processPolicyFlap(context, policy, false, triggeredPolicies, policyTimerContexts, LOGGER, mappers, builder);

                         //as it is already correlated just checking if any unreachable objects there so then updating its unreachable objects duration to keep its policy duration up to date
                         var item = MetricPolicyCacheStore.getStore().getItem(context.getString(POLICY_KEY).hashCode());

                         if (item.containsKey(CORRELATED_UNREACHABLE_OBJECTS))
                         {
                             for (var key : item.getJsonArray(CORRELATED_UNREACHABLE_OBJECTS))
                             {
                                 if (triggeredPolicies.containsKey(CommonUtil.getString(key)))
                                 {
                                     PolicyEngineConstants.updatePolicyTriggerDuration(triggeredPolicies.get(CommonUtil.getString(key)).put(EVENT_TIMESTAMP, context.getLong(EVENT_TIMESTAMP)));
                                 }

                                 else
                                 {

                                     var policyContext = MetricPolicyCacheStore.getStore().getItem(key.hashCode());

                                     PolicyEngineConstants.updatePolicyTriggerDuration(policyContext.put(POLICY_TYPE, policies.get(policyContext.getLong(ID)).getString(POLICY_TYPE)).put(POLICY_KEY, key).put(EVENT_TIMESTAMP, context.getLong(EVENT_TIMESTAMP)));
                                 }
                             }
                         }
                     }
                 }
                 else
                 {
                     PolicyEngineConstants.processPolicyFlap(context, policy, false, triggeredPolicies, policyTimerContexts, LOGGER, mappers, builder);
                 }
             }

             else
             {
                 PolicyEngineConstants.processPolicyFlap(context, policy, false, triggeredPolicies, policyTimerContexts, LOGGER, mappers, builder);
             }
         }
         catch (Exception exception)
         {
             LOGGER.error(exception);
         }
     }

     private long qualifyPolicyByMetric(JsonArray groups, long objectId, JsonArray tags)
     {
         var qualifyPolicies = new ArrayList<Long>();

         try
         {

             if (policiesByObject.containsKey(objectId) && policiesByObject.get(objectId).containsKey(PolicyType.AVAILABILITY.getName() + SEPARATOR + STATUS))
             {
                 qualifyPolicies.addAll(policiesByObject.get(objectId).get(PolicyEngineConstants.PolicyType.AVAILABILITY.getName() + SEPARATOR + STATUS));
             }

             for (var index = 0; index < groups.size(); index++)
             {
                 var group = groups.getLong(index);

                 if (policiesByGroup.containsKey(group) && policiesByGroup.get(group).containsKey(PolicyEngineConstants.PolicyType.AVAILABILITY.getName() + SEPARATOR + STATUS))
                 {
                     qualifyPolicies.addAll(policiesByGroup.get(group).get(PolicyEngineConstants.PolicyType.AVAILABILITY.getName() + SEPARATOR + STATUS));
                 }
             }

             if (tags != null)
             {
                 for (var index = 0; index < tags.size(); index++)
                 {
                     var tagId = tags.getLong(index);

                     if (policiesByTag.containsKey(tagId) && policiesByTag.get(tagId).containsKey(PolicyEngineConstants.PolicyType.AVAILABILITY.getName() + SEPARATOR + STATUS))
                     {
                         qualifyPolicies.addAll(policiesByTag.get(tagId).get(PolicyEngineConstants.PolicyType.AVAILABILITY.getName() + SEPARATOR + STATUS));
                     }
                 }
             }

             if (!qualifyPolicies.isEmpty())
             {
                 qualifyPolicies.sort(Comparator.comparing(item -> this.policies.get(CommonUtil.getLong(item)).getLong(PolicyEngineConstants.POLICY_CREATION_TIME)).reversed());
             }

         }
         catch (Exception exception)
         {
             LOGGER.error(exception);
         }

         return qualifyPolicies.isEmpty() ? 0 : qualifyPolicies.getFirst();
     }

     /**
      * Removes duplicate objects from the triggered policy contexts.
      * In case of instance-level metrics, all unique instances will be considered.
      * For scalar metrics, only the first object is processed to avoid duplicate notifications.
      *
      * @param contexts Map of contexts for triggered policies, organized by metric and policy type
      */
     private void removeDuplicateObjects(Map<String, List<JsonObject>> contexts)
     {
         contexts.forEach((key, objects) ->
         {
             try
             {
                 if (objects.getFirst().getString(INSTANCE) != null)
                 {
                     objects.forEach(this::updateContext);
                 }
                 else
                 {
                     objects.getFirst().remove(INSTANCE);

                     updateContext(objects.getFirst());
                 }
             }
             catch (Exception exception)
             {
                 LOGGER.error(exception);
             }
         });
     }

     /**
      * Updates the column mapper with new metric information.
      * This method processes column mapper update events, adding new metrics to the
      * column mapper and triggering policy assignment for new metrics.
      *
      * @param event The event containing column mapper update information
      */
     private void updateColumnMapper(JsonObject event)
     {
         try
         {
             if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(ChangeNotificationType.UPDATE_METRIC_COLUMN.name()))
             {
                 var tokens = event.getString(DatastoreConstants.MAPPER).split(COLUMN_SEPARATOR, -1);

                 if (!columns.containsKey(tokens[2]))
                 {
                     columns.put(tokens[2], new JsonObject());
                 }

                 var mapper = columns.getJsonObject(tokens[2]);

                 var plugins = mapper.getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);

                 if (plugins == null)
                 {
                     plugins = new JsonArray(new ArrayList<>(1));
                 }

                 var categories = mapper.getJsonArray(DatastoreConstants.MAPPER_DATA_CATEGORIES);

                 if (categories == null)
                 {
                     categories = new JsonArray(new ArrayList<>(1));
                 }

                 if (!categories.contains(CommonUtil.getInteger(tokens[0])))
                 {
                     mapper.put(DatastoreConstants.MAPPER_DATA_CATEGORIES, categories.add(CommonUtil.getInteger(tokens[0])));
                 }

                 mapper.put(DatastoreConstants.MAPPER_INSTANCE, tokens[3]);

                 if (!plugins.contains(CommonUtil.getInteger(tokens[1])))
                 {
                     mapper.put(DatastoreConstants.MAPPER_PLUGIN_IDS, plugins.add(CommonUtil.getInteger(tokens[1])));

                     assign(tokens[2]);
                 }
             }
         }
         catch (Exception exception)
         {
             LOGGER.error(exception);
         }
     }

     @Override
     public void stop(Promise<Void> promise) throws Exception
     {
         eventEngine.stop(vertx, promise);
     }

     /**
      * Processes unreachable objects during availability correlation.
      * This method handles objects that are unreachable due to upstream failures,
      * updating their correlation status and suppressing unnecessary alerts.
      *
      * @param unreachableObjects The array of unreachable objects
      * @param builder            StringBuilder for logging messages
      * @param correlationResult  The correlation result object to update
      * @param context            The context containing policy and object information
      * @return True if objects were correlated, false otherwise
      */
     private boolean processUnreachableObjects(JsonArray unreachableObjects, StringBuilder builder, JsonObject correlationResult, JsonObject context)
     {
         var correlated = false;

         try
         {
             if (correlationResult.containsKey(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS) && !correlationResult.getJsonArray(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS).isEmpty())
             {
                 correlated = true;

                 var correlationContext = new JsonObject().put(SEVERITY, Severity.UNREACHABLE.name()).put(METRIC, STATUS).put(POLICY_THRESHOLD, STATUS_UNREACHABLE).put(VALUE, STATUS_UNREACHABLE);

                 var count = 0;

                 for (var unreachableObject : correlationResult.getJsonArray(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS))
                 {
                     var item = ObjectConfigStore.getStore().getItem(CommonUtil.getLong(unreachableObject));

                     var policyId = qualifyPolicyByMetric(item.getJsonArray(AIOpsObject.OBJECT_GROUPS), item.getLong(ID), item.getJsonArray(TAGS));

                     if (policyId > 0)
                     {
                         if (count <= MAX_CORRELATED_OBJECT_NAMES)                                         // for readability purpose in UI we are showing 10 object name at a time.
                         {
                             builder.append(item.getString(AIOpsObject.OBJECT_NAME)).append(COMMA_SEPARATOR);
                         }

                         var object = unreachableObject + SEPARATOR + PolicyType.AVAILABILITY.getName() + SEPARATOR + STATUS;

                         unreachableObjects.add(object);

                         correlationContext.put(POLICY_KEY, object).put(EVENT_TIMESTAMP, context.getLong(EVENT_TIMESTAMP))
                                 .put(PLUGIN_ID, context.getInteger(PLUGIN_ID)).put(Metric.METRIC_TYPE, context.getString(Metric.METRIC_TYPE))
                                 .put(ID, policyId).put(POLICY_TYPE, context.getString(POLICY_TYPE))
                                 .put(CORRELATION_PROBE, NO)
                                 .put(ENTITY_ID, CommonUtil.getLong(unreachableObject)).put(AIOpsObject.OBJECT_CATEGORY, item.getString(AIOpsObject.OBJECT_CATEGORY))
                                 .put(AIOpsObject.OBJECT_TYPE, item.getString(AIOpsObject.OBJECT_TYPE)).put(AIOpsObject.OBJECT_NAME, item.getString(AIOpsObject.OBJECT_NAME))
                                 .put(AIOpsObject.OBJECT_IP, item.getString(AIOpsObject.OBJECT_IP)).put(AIOpsObject.OBJECT_ID, item.getInteger(AIOpsObject.OBJECT_ID));

                         send(correlationContext);

                         count++;
                     }
                 }

                 if (count > MAX_CORRELATED_OBJECT_NAMES)
                 {
                     builder.deleteCharAt(builder.length() - 1).append(correlationResult.getJsonArray(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS).size() - count).append(" more,");
                 }
             }
         }
         catch (Exception exception)
         {
             LOGGER.error(exception);
         }

         return correlated;
     }

     private void send(JsonObject event)
     {
         // we have multiple instances of policy inspection on basis of routing using object id so there are chances that this particular event's object id
         // could be inspected by other instance so will be sending this message to route it properly to its original inspected instance

         vertx.eventBus().send(EVENT_METRIC_POLICY, event);
     }

     private void writeCorrelationMap(JsonObject item, JsonObject correlationResult, long policyId)
     {
         vertx.eventBus().<JsonObject>request(EVENT_DEPENDENCY_QUERY,
                 new JsonObject().put(AIOpsConstants.ENTITY_ID, CommonUtil.getLong(item.getLong(ID))).put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.valueOfName(item.getString(AIOpsObject.OBJECT_CATEGORY)))
                         .put(AIOpsObject.OBJECT_TYPE, item.getString(AIOpsObject.OBJECT_TYPE))
                         .put(EVENT_REPLY, YES).put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                         .put(AIOpsConstants.RECURSIVE_DEPENDENCIES, YES)
                         .put(EVENT_TYPE, EventBusConstants.EVENT_AVAILABILITY_CORRELATION)
                         .put(AIOpsConstants.DEPENDENCY_FORMAT, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName()),
                 DELIVERY_OPTIONS,
                 reply ->
                 {
                     try
                     {
                         if (reply.succeeded())
                         {
                             LOGGER.info(String.format("writing correlation map for %s ", item.getString(AIOpsObject.OBJECT_NAME)));

                             if (MotadataConfigUtil.devMode())
                             {
                                 vertx.eventBus().send("event.write.correlation.map", new JsonObject().put(ID, item.getLong(ID)).put(POLICY_ID, policyId).put(INSTANCE, correlationResult.getString(CORRELATION_INSTANCE, EMPTY_VALUE)));
                             }

                             vertx.fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + CACHE_DIR + PATH_SEPARATOR + item.getLong(ID) + DASH_SEPARATOR + policyId + GlobalConstants.DASH_SEPARATOR + correlationResult.getString(CORRELATION_INSTANCE) + DASH_SEPARATOR + CORRELATION_MAP,
                                     Buffer.buffer(CodecUtil.compress(new JsonObject().put(RESULT, new JsonObject().put(CORRELATION_OBJECTS, correlationResult.getJsonArray(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS).size()).put(CORRELATION_MAP, reply.result().body().getJsonObject(RESULT).encode())).encode().getBytes())));
                         }
                         else
                         {
                             LOGGER.error(reply.cause());
                         }
                     }

                     catch (Exception exception)
                     {
                         LOGGER.error(exception);
                     }
                 });
     }

 }
