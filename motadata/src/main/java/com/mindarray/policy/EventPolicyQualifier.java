/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  25-Mar-2025     Smit Prajapati      MOTADATA-5435: Flow back-pressure mechanism.
 */
package com.mindarray.policy;

import com.mindarray.GlobalConstants;
import com.mindarray.api.APIConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.store.EventPolicyConfigStore;
import com.mindarray.store.EventSourceConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.EventPolicy.POLICY_RESULT_BY;
import static com.mindarray.api.EventPolicy.POLICY_SCHEDULED;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.policy.PolicyEngineConstants.*;

/**
 * EventPolicyQualifier is responsible for qualifying events against defined policies.
 * <p>
 * This class:
 * 1. Loads and manages event policies from the configuration store
 * 2. Maps policies to event sources, source types, and groups
 * 3. Qualifies incoming events against applicable policies
 * 4. Forwards qualified events to the EventPolicyInspector for further processing
 * 5. Handles policy suppression and filtering
 * <p>
 * The qualifier runs as a Vert.x verticle and acts as a first-level filter
 * to determine which policies should be evaluated for each event.
 */
public class EventPolicyQualifier extends AbstractVerticle
{

    private static final Logger LOGGER = new Logger(EventPolicyQualifier.class, GlobalConstants.MOTADATA_POLICY, "Event Policy Qualifier");
    private final Map<Long, JsonObject> policies = new HashMap<>();
    private final Map<Long, JsonArray> filters = new HashMap<>();//will be used to filterout datapoints of grouping so easy to filterout
    private final JsonObject eventColumns = new JsonObject();
    private final Map<String, Map<String, List<Long>>> policiesBySource = new HashMap<>();
    private final Map<String, Map<String, List<Long>>> policiesBySourceType = new HashMap<>();
    private final Map<Long, Map<String, List<Long>>> policiesByGroup = new HashMap<>();
    private final Set<Long> suppressedPolicies = new HashSet<>();
    private final Set<String> numericColumns = new HashSet<>();
    private EventEngine eventEngine;

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_EVENT_COLUMN_MAPPER_QUERY, EMPTY_VALUE, reply ->
        {
            eventColumns.mergeIn(reply.result().body().getJsonObject(DatastoreConstants.EVENT_COLUMNS));

            try
            {
                var items = EventPolicyConfigStore.getStore().getItems();

                loadNumericColumns();

                for (var index = 0; index < items.size(); index++)
                {
                    var policy = items.getJsonObject(index);

                    if ((!policy.containsKey(POLICY_ARCHIVED) || policy.getString(POLICY_ARCHIVED).equalsIgnoreCase(NO)) && (!policy.containsKey(POLICY_SCHEDULED) || policy.getString(POLICY_SCHEDULED).equalsIgnoreCase(NO)))
                    {
                        policies.put(policy.getLong(ID), policy);

                        var context = policy.getJsonObject(POLICY_CONTEXT);

                        if (context.containsKey(POLICY_RESULT_BY))
                        {
                            filters.put(policy.getLong(ID), new JsonArray().addAll(context.getJsonArray(POLICY_RESULT_BY).add(context.getString(VisualizationConstants.DATA_POINT))));
                        }
                        else
                        {
                            filters.put(policy.getLong(ID), new JsonArray().add(context.getString(VisualizationConstants.DATA_POINT)));
                        }
                    }
                }

                qualify();
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE, message ->
        {
            try
            {
                var event = message.body();

                var tokens = event.getString(DatastoreConstants.MAPPER).split(GlobalConstants.COLUMN_SEPARATOR, -1);

                if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(EventBusConstants.ChangeNotificationType.UPDATE_EVENT_COLUMN.name()))
                {
                    update(eventColumns, tokens, false);

                    if (CommonUtil.getInteger(tokens[0]) == CommonUtil.getInteger(DatastoreConstants.DataCategory.NUMERIC.getName()))
                    {
                        numericColumns.add(tokens[2].trim());
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        eventEngine = new EventEngine().setEventType(config().getString(EventBusConstants.EVENT_TYPE))
                .setPersistEventOffset(true).setLogger(LOGGER).setEventHandler(this::qualify).start(vertx, promise);

        vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
        {
            var event = message.body();

            var notificationType = EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE));

            switch (notificationType)
            {

                case ADD_POLICY, UPDATE_POLICY ->
                {
                    var policy = EventPolicyConfigStore.getStore().getItem(event.getLong(ID));

                    if (!policy.containsKey(POLICY_SCHEDULED) || policy.getString(POLICY_SCHEDULED).equalsIgnoreCase(NO))
                    {
                        policies.put(policy.getLong(ID), policy);
                    }
                    var context = policy.getJsonObject(POLICY_CONTEXT);

                    if (context.containsKey(POLICY_RESULT_BY))
                    {
                        filters.put(policy.getLong(ID), new JsonArray().addAll(context.getJsonArray(POLICY_RESULT_BY).add(context.getString("data.point"))));
                    }
                    else
                    {
                        filters.put(policy.getLong(ID), new JsonArray().add(context.getString("data.point")));
                    }

                    qualify();
                }

                case DELETE_POLICY ->
                {
                    policies.remove(event.getLong(ID));

                    filters.remove(event.getLong(ID));

                    qualify();
                }

                case SUPPRESS_POLICY ->
                {
                    if (message.body() != null && policies.containsKey(event.getLong(POLICY_ID)))
                    {
                        suppressedPolicies.add(event.getLong(POLICY_ID));
                    }
                }

                case UNSUPPRESS_POLICY ->
                {
                    if (message.body() != null)
                    {
                        suppressedPolicies.remove(event.getLong(POLICY_ID));
                    }
                }
                default ->
                {
                    // do nothing
                }
            }

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EVENT_EVENT_POLICY_SUPPRESS, message -> message.reply(message.body() != null && suppressedPolicies.contains(message.body().getLong(POLICY_ID))));
    }

    private void loadNumericColumns()
    {
        for (var entry : eventColumns.getMap().entrySet())
        {
            var item = (JsonObject) entry.getValue();

            if (item.getJsonArray(DatastoreConstants.MAPPER_DATA_CATEGORIES).contains(CommonUtil.getInteger(DatastoreConstants.DataCategory.NUMERIC.getName())))
            {
                numericColumns.add(entry.getKey());
            }
        }
    }

    /**
     * Maps policies to event sources, source types, and groups.
     * This method builds the mapping between policies and the entities they apply to,
     * which is essential for efficient policy qualification.
     */
    private void qualify()
    {
        policiesBySource.clear();

        policiesByGroup.clear();

        policiesBySourceType.clear();

        try
        {
            policies.forEach((key, value) ->
            {
                var policyContext = value.getJsonObject(POLICY_CONTEXT);

                var dataPoint = policyContext.getString(VisualizationConstants.DATA_POINT);

                JsonArray entities;

                if (policyContext.getJsonArray(ENTITIES) != null && !policyContext.getJsonArray(ENTITIES).isEmpty())//if policy contains filter of group or monitor so just assigning that filtered entities or groups
                {
                    entities = policyContext.getJsonArray(ENTITIES);

                    if (policyContext.getString(ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.GROUP.getName()))
                    {
                        assignById(value.getLong(ID), dataPoint, entities, policiesByGroup);
                    }
                    else if (policyContext.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_SOURCE_TYPE.getName()))
                    {
                        assignBySource(value.getLong(ID), dataPoint, entities, policiesBySourceType);
                    }
                    else
                    {
                        assignBySource(value.getLong(ID), dataPoint, entities, policiesBySource);
                    }
                }
                else
                {
                    if (dataPoint.equalsIgnoreCase(MESSAGE) || dataPoint.equalsIgnoreCase(LogEngineConstants.EVENT_CATEGORY) || dataPoint.equalsIgnoreCase(LogEngineConstants.EVENT_SOURCE_TYPE) || dataPoint.equalsIgnoreCase(EVENT_SOURCE))
                    {
                        entities = EventSourceConfigStore.getStore().flatItems(EVENT_SOURCE);
                    }
                    else if (!eventColumns.isEmpty() && eventColumns.containsKey(dataPoint))
                    {
                        entities = EventSourceConfigStore.getStore().flatItemsByMultiValueFields(EVENT_TYPE, value.getString(POLICY_TYPE).toLowerCase(), EVENT_SOURCE, PLUGIN_ID, eventColumns.getJsonObject(dataPoint).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS));
                    }
                    else entities = new JsonArray();

                    assignBySource(value.getLong(ID), dataPoint, entities, policiesBySource);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

    }

    //according to entitytype selected monitor or group will be assigning policies accordingly
    private void assignBySource(long policyId, String key, JsonArray entities, Map<String, Map<String, List<Long>>> policiesByEntity)
    {
        try
        {
            entities.forEach(entity ->
            {
                var object = CommonUtil.getString(entity);

                if (!policiesByEntity.containsKey(object))
                {
                    policiesByEntity.put(object, new HashMap<>());

                    policiesByEntity.get(object).put(key, new ArrayList<>());
                }

                else if (!policiesByEntity.get(object).containsKey(key))
                {
                    policiesByEntity.get(object).put(key, new ArrayList<>());
                }

                if (!policiesByEntity.get(object).get(key).contains(policyId))
                {
                    policiesByEntity.get(object).get(key).add(policyId);
                }
            });
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void assignById(long policyId, String key, JsonArray entities, Map<Long, Map<String, List<Long>>> policiesByEntity)
    {
        try
        {
            entities.forEach(entity ->
            {
                var object = CommonUtil.getLong(entity);

                if (!policiesByEntity.containsKey(object))
                {
                    policiesByEntity.put(object, new HashMap<>());

                    policiesByEntity.get(object).put(key, new ArrayList<>());
                }

                else if (!policiesByEntity.get(object).containsKey(key))
                {
                    policiesByEntity.get(object).put(key, new ArrayList<>());
                }

                if (!policiesByEntity.get(object).get(key).contains(policyId))
                {
                    policiesByEntity.get(object).get(key).add(policyId);
                }
            });
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Finds applicable policies for an event based on its source, source type, and groups.
     * This method aggregates policies from different mapping sources to create a complete
     * set of policies that should be evaluated for the event.
     *
     * @param groups          The groups associated with the event source
     * @param source          The event source
     * @param eventSourceType The event source type
     * @return A map of data points to sets of policy IDs that should be evaluated
     */
    private Map<String, Set<Long>> assign(JsonArray groups, String source, String eventSourceType)
    {
        var qualifiedPolicies = new HashMap<String, Set<Long>>();

        try
        {
            policiesBySource.getOrDefault(source, Collections.emptyMap()).forEach((key, policyIds) ->
            {
                if (!policyIds.isEmpty())
                {
                    qualifiedPolicies.computeIfAbsent(key, value -> new LinkedHashSet<>()).addAll(policyIds);
                }
            });


            if (CommonUtil.isNotNullOrEmpty(eventSourceType))
            {
                policiesBySourceType.getOrDefault(eventSourceType, Collections.emptyMap()).forEach((key, policyIds) ->
                {
                    if (!policyIds.isEmpty())
                    {
                        qualifiedPolicies.computeIfAbsent(key, value -> new LinkedHashSet<>()).addAll(policyIds);
                    }
                });
            }

            for (var index = 0; index < groups.size(); index++)
            {
                var group = groups.getLong(index);

                policiesByGroup.getOrDefault(group, Collections.emptyMap()).forEach((key, policyIds) ->
                {
                    if (!policyIds.isEmpty())
                    {
                        qualifiedPolicies.computeIfAbsent(key, value -> new LinkedHashSet<>()).addAll(policyIds);
                    }
                });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return qualifiedPolicies;
    }

    /**
     * Qualifies an event against applicable policies.
     * This method:
     * 1. Determines the event type (log, flow, trap)
     * 2. Finds applicable policies based on event source and groups
     * 3. Evaluates the event against policy conditions
     * 4. Forwards qualified events to the EventPolicyInspector
     *
     * @param event The event to qualify, containing event data and metadata
     */
    private void qualify(JsonObject event)
    {
        try
        {
            var eventType = EVENT_LOG;

            var item = EventSourceConfigStore.getStore().getItemByHost(event.getString(EVENT_SOURCE), false);

            if (event.containsKey(PLUGIN_ID) && event.getInteger(PLUGIN_ID) != null)
            {
                if (event.getInteger(PLUGIN_ID) == DatastoreConstants.PluginId.FLOW_EVENT.getName())
                {
                    eventType = EVENT_FLOW;
                }
                else if (event.getInteger(PLUGIN_ID) == DatastoreConstants.PluginId.TRAP_EVENT.getName())
                {
                    eventType = EVENT_TRAP;
                }
            }

            if (item != null)
            {
                for (var entry : assign(item.getJsonArray(LogEngineConstants.SOURCE_GROUPS), event.getString(EVENT_SOURCE), event.getString(LogEngineConstants.EVENT_SOURCE_TYPE)).entrySet())
                {
                    for (var value : entry.getValue())
                    {
                        var policy = policies.get(value);

                        if (policy.getString(POLICY_TYPE).equalsIgnoreCase(eventType) && policy.getString(POLICY_STATE).equalsIgnoreCase(YES) && !suppressedPolicies.contains(value))
                        {
                            var context = policy.getJsonObject(POLICY_CONTEXT);

                            if (event.containsKey(entry.getKey()) && event.getValue(entry.getKey()) != null)
                            {
                                var valid = !numericColumns.contains(entry.getKey()) || CommonUtil.getLong(event.getValue(entry.getKey())) > 0;

                                if (valid && context.containsKey(POLICY_RESULT_BY) && !context.getJsonArray(POLICY_RESULT_BY).isEmpty())
                                {
                                    for (var i = 0; i < context.getJsonArray(POLICY_RESULT_BY).size(); i++)
                                    {
                                        var counter = context.getJsonArray(POLICY_RESULT_BY).getString(i);

                                        if (!event.containsKey(counter) && !CommonUtil.isNotNullOrEmpty(CommonUtil.getString(event.getValue(counter))))
                                        {
                                            valid = false;
                                        }
                                    }
                                }

                                if (valid)
                                {
                                    var filter = context.getJsonObject(FILTERS).getJsonObject(DATA_FILTER, null);

                                    var groupConditionSatisfied = true;

                                    if (filter != null && !filter.isEmpty())
                                    {
                                        if (EVENT_TRAP.equalsIgnoreCase(eventType))
                                        {
                                            groupConditionSatisfied = PolicyEngineConstants.filterTrap(filter, event);
                                        }
                                        else
                                        {
                                            var groupOperator = filter.getString(OPERATOR);

                                            var conditionGroups = filter.getJsonArray(CONDITION_GROUPS);

                                            for (var index = 0; index < conditionGroups.size(); index++)
                                            {
                                                var conditionGroup = conditionGroups.getJsonObject(index);

                                                var operator = conditionGroup.getString(OPERATOR);

                                                var conditions = conditionGroup.getJsonArray(CONDITIONS);

                                                var satisfied = false;

                                                for (var j = 0; j < conditions.size(); j++)
                                                {
                                                    var condition = conditions.getJsonObject(j);

                                                    var operand = condition.getString(OPERAND).contains(CARET_SEPARATOR) ? condition.getString(OPERAND).split(CARET_SEPARATOR_WITH_ESCAPE)[0] : condition.getString(OPERAND);

                                                    if (event.containsKey(operand))
                                                    {
                                                        satisfied = PolicyEngineConstants.evaluateCondition(conditionGroup.getString(FILTER).equalsIgnoreCase("include"), condition.getString(OPERATOR), condition.getValue(VALUE), event.getValue(operand));

                                                        if ((satisfied && operator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName())) || (!satisfied && operator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.AND.getName())))
                                                        {
                                                            break;
                                                        }
                                                    }
                                                }

                                                if (!satisfied && groupOperator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.AND.getName()))
                                                {
                                                    //if main condition is "AND" and any counter group condition is not true so breaking for loop as in "AND" condition all condition is needed to be true
                                                    groupConditionSatisfied = false;

                                                    break;
                                                }
                                                else if (satisfied && groupOperator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName()))
                                                {
                                                    //if main condition is "OR" and any counter group condition is true so breaking for loop as in "OR" condition only one condition needed to be true
                                                    groupConditionSatisfied = true;

                                                    break;
                                                }
                                                else if (!satisfied && groupOperator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName()))
                                                {
                                                    groupConditionSatisfied = false;
                                                }
                                            }
                                        }

                                        if (groupConditionSatisfied)
                                        {
                                            if (EVENT_TRAP.equalsIgnoreCase(eventType))
                                            {
                                                //for trap, we need to show trap details in Alert Message, hence sending event(whole trap) to inspector
                                                vertx.eventBus().send(EVENT_EVENT_POLICY, event.put(POLICY_ID, value));
                                            }
                                            else
                                            {
                                                var result = new JsonObject();

                                                //will forward required fields only to inspector
                                                for (var key : filters.get(policy.getLong(ID)))
                                                {
                                                    result.put(CommonUtil.getString(key), event.getString(CommonUtil.getString(key)));
                                                }

                                                vertx.eventBus().send(EVENT_EVENT_POLICY, result.put(POLICY_ID, value).put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP)).put(EVENT_COPY_REQUIRED, false));
                                            }

                                            if (MotadataConfigUtil.devMode())
                                            {
                                                vertx.eventBus().publish(EventBusConstants.EVENT_EVENT_POLICY_TEST, new JsonObject().put(POLICY_ID, value).put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP)).put(EVENT_COPY_REQUIRED, false));
                                            }
                                        }
                                    }
                                    else
                                    {

                                        if (EVENT_TRAP.equalsIgnoreCase(eventType))
                                        {
                                            //for trap, we need to show trap details in Alert Message, hence sending event(whole trap) to inspector
                                            vertx.eventBus().send(EVENT_EVENT_POLICY, event.put(POLICY_ID, value));
                                        }
                                        else
                                        {
                                            var result = new JsonObject();

                                            //will forward required fields only to inspector
                                            for (var key : filters.get(policy.getLong(ID)))
                                            {
                                                result.put(CommonUtil.getString(key), event.getString(CommonUtil.getString(key)));
                                            }

                                            vertx.eventBus().send(EVENT_EVENT_POLICY, result.put(POLICY_ID, value).put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP)).put(EVENT_COPY_REQUIRED, false));
                                        }

                                        if (MotadataConfigUtil.devMode())
                                        {
                                            vertx.eventBus().publish(EventBusConstants.EVENT_EVENT_POLICY_TEST, new JsonObject().put(POLICY_ID, value).put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP)).put(EVENT_COPY_REQUIRED, false));
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        eventEngine.stop(vertx, promise);
    }
}
