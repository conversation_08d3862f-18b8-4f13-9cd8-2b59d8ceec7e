/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	26-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
*/
package com.mindarray.eventbus;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.util.*;
import io.vertx.core.*;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;
import org.zeromq.SocketType;
import org.zeromq.ZMQ;

import java.io.File;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.LockSupport;

import static com.mindarray.GlobalConstants.*;

/**
 * EventEngine is a core component for event processing and distribution in the Motadata platform.
 * It provides a flexible and scalable mechanism for handling events across the system with support for:
 * <ul>
 *   <li>Local and remote event processing</li>
 *   <li>Event forwarding to other components</li>
 *   <li>Persistent event storage with offset tracking</li>
 *   <li>Blocking and non-blocking event handling</li>
 *   <li>Back-pressure management to prevent system overload</li>
 *   <li>Event segmentation for improved performance</li>
 * </ul>
 * <p>
 * The EventEngine uses a producer-consumer pattern where events are produced and stored,
 * then consumed by handlers based on system capacity. It integrates with Vert.x event bus
 * for internal communication and ZeroMQ for external communication.
 */
public class EventEngine
{
    private final AtomicBoolean hasMoreEvent = new AtomicBoolean(true);
    private final AtomicInteger queuedEvents = new AtomicInteger(0);
    private final AtomicInteger finishedEvents = new AtomicInteger(0);
    private Logger logger;
    private boolean eventForwarder;
    private boolean remoteEventProcessor;
    private String remoteEventPublisher;
    private int remoteEventPublisherPort;
    private int eventForwarderPort;
    private String eventType;
    private boolean persistEventOffset;
    private boolean blockingEvent;
    private Handler<JsonObject> eventHandler;
    private ZMQ.Socket forwarder;
    private ZMQ.Socket remoteEventSubscriber;
    private CipherUtil cipherUtil;
    private String eventEngineHelper;
    private Map<Long, String> replyAddresses;
    private int eventQueueSize;
    private int eventSegments;

    /**
     * Sets the event type identifier for this EventEngine instance.
     * The event type is used to identify the event channel and must be unique across the system.
     *
     * @param eventType A unique identifier for the event type
     * @return This EventEngine instance for method chaining
     */
    public EventEngine setEventType(String eventType)
    {
        this.eventType = eventType;
        return this;
    }

    /**
     * Configures whether event offsets should be persisted.
     * When enabled, the EventEngine will save its processing state to disk,
     * allowing it to resume from the last processed event after a restart.
     *
     * @param persistEventOffset True to enable event offset persistence, false otherwise
     * @return This EventEngine instance for method chaining
     */
    public EventEngine setPersistEventOffset(boolean persistEventOffset)
    {
        this.persistEventOffset = persistEventOffset;

        return this;
    }

    /**
     * Configures whether events should be processed in a blocking manner.
     * When enabled, the EventEngine will wait for a response before processing the next event.
     * This is useful for synchronous event processing where order matters.
     *
     * @param blockingEvent True to enable blocking event processing, false for non-blocking
     * @return This EventEngine instance for method chaining
     */
    public EventEngine setBlockingEvent(boolean blockingEvent)
    {
        this.blockingEvent = blockingEvent;

        return this;
    }

    /**
     * Sets the handler that will process events.
     * The handler is called for each event and receives the event data as a JsonObject.
     *
     * @param handler The event handler function
     * @return This EventEngine instance for method chaining
     */
    public EventEngine setEventHandler(Handler<JsonObject> handler)
    {
        this.eventHandler = handler;
        return this;
    }

    /**
     * Configures whether this EventEngine should forward events to other components.
     * When enabled, events will be sent to the configured forwarder port.
     *
     * @param eventForwarder True to enable event forwarding, false otherwise
     * @return This EventEngine instance for method chaining
     */
    public EventEngine setEventForwarder(boolean eventForwarder)
    {
        this.eventForwarder = eventForwarder;

        return this;
    }

    /**
     * Sets the port number for the event forwarder.
     * This port is used when event forwarding is enabled to send events to other components.
     *
     * @param eventForwarderPort The port number for event forwarding
     * @return This EventEngine instance for method chaining
     */
    public EventEngine setEventForwarderPort(int eventForwarderPort)
    {
        this.eventForwarderPort = eventForwarderPort;
        return this;
    }

    /**
     * Sets the maximum number of events that can be queued for processing at once.
     * This helps manage back-pressure and prevent memory issues during high event volumes.
     *
     * @param events The maximum number of events to queue
     * @return This EventEngine instance for method chaining
     */
    public EventEngine setEventQueueSize(int events)
    {
        this.eventQueueSize = events;
        return this;
    }

    /**
     * Configures whether this EventEngine should process events from remote sources.
     * When enabled, the EventEngine will listen for events from the configured remote publisher.
     *
     * @param remoteEventProcessor True to enable remote event processing, false otherwise
     * @return This EventEngine instance for method chaining
     */
    public EventEngine setRemoteEventProcessor(boolean remoteEventProcessor)
    {
        this.remoteEventProcessor = remoteEventProcessor;
        return this;
    }

    /**
     * Sets the address of the remote event publisher.
     * This is used when remote event processing is enabled to connect to the remote event source.
     *
     * @param remoteEventPublisher The address of the remote event publisher
     * @return This EventEngine instance for method chaining
     */
    public EventEngine setRemoteEventPublisher(String remoteEventPublisher)
    {
        this.remoteEventPublisher = remoteEventPublisher;
        return this;
    }

    /**
     * Sets the port number for the remote event publisher.
     * This is used when remote event processing is enabled to connect to the remote event source.
     *
     * @param remoteEventPublisherPort The port number of the remote event publisher
     * @return This EventEngine instance for method chaining
     */
    public EventEngine setRemoteEventPublisherPort(int remoteEventPublisherPort)
    {
        this.remoteEventPublisherPort = remoteEventPublisherPort;
        return this;
    }

    /**
     * Sets the number of event segments to use for storage.
     * Event segmentation improves performance by distributing events across multiple files.
     *
     * @param eventSegments The number of event segments to use
     * @return This EventEngine instance for method chaining
     */
    public EventEngine setEventSegments(int eventSegments)
    {
        this.eventSegments = eventSegments;
        return this;
    }

    /**
     * Sets the logger instance for this EventEngine.
     * The logger is used to record operational information and errors.
     *
     * @param logger The logger instance to use
     * @return This EventEngine instance for method chaining
     */
    public EventEngine setLogger(Logger logger)
    {
        this.logger = logger;
        return this;
    }

    /**
     * Starts the EventEngine and initializes all required components.
     * This method validates configuration parameters, sets up event handlers,
     * initializes ZeroMQ sockets if needed, and starts the EventEngineHelper.
     *
     * @param vertx   The Vert.x instance to use for event bus communication
     * @param promise A promise that will be completed when startup is finished or failed if an error occurs
     * @return This EventEngine instance for method chaining
     */
    public EventEngine start(Vertx vertx, Promise<Void> promise)
    {
        // Validate engine startup parameters before proceeding

        try
        {
            cipherUtil = new CipherUtil();

            // Validate required configuration parameters
            if (CommonUtil.isNullOrEmpty(eventType))
            {
                promise.fail("Event Type is missing...");
                return this;
            }

            // Either an event handler or event forwarder is required
            // If event is handled by external event handler, we need a way to queue events and handle subscriptions
            if (eventHandler == null && !eventForwarder)
            {
                promise.fail("Event Handler is missing...");
                return this;
            }

            // Validate event forwarder configuration if enabled
            if (eventForwarder && eventForwarderPort <= 0)
            {
                promise.fail("Event forwarder port is invalid...");
                return this;
            }

            // Validate remote event processor configuration if enabled
            if (remoteEventProcessor)
            {
                if (remoteEventPublisherPort <= 0)
                {
                    promise.fail("Remote Event publisher port is invalid...");
                    return this;
                }

                if (CommonUtil.isNullOrEmpty(remoteEventPublisher))
                {
                    promise.fail("Remote Event publisher is missing...");
                    return this;
                }
            }

            if (eventForwarder)
            {
                forwarder = Bootstrap.zcontext().socket(SocketType.PUSH);

                forwarder.setSndHWM(MotadataConfigUtil.getEventBacklogSize());

                forwarder.setHWM(MotadataConfigUtil.getEventBacklogSize());

                forwarder.setSendTimeOut(0);

                forwarder.setTCPKeepAlive(1);

                forwarder.bind("tcp://*:" + eventForwarderPort);

                LockSupport.parkNanos(Duration.ofMillis(100).toNanos()); // Eliminate slow subscriber problem
            }

            if (remoteEventProcessor)
            {
                remoteEventSubscriber = Bootstrap.zcontext().socket(SocketType.PULL);

                remoteEventSubscriber.setHWM(MotadataConfigUtil.getEventBacklogSize());

                remoteEventSubscriber.setRcvHWM(MotadataConfigUtil.getEventBacklogSize());

                remoteEventSubscriber.connect("tcp://" + remoteEventPublisher + ":" + remoteEventPublisherPort);

                new Thread(() ->
                {
                    while (hasMoreEvent.get())
                    {
                        try
                        {
                            var bytes = cipherUtil.decrypt(remoteEventSubscriber.recv());

                            if (bytes != null)
                            {
                                vertx.eventBus().send(eventType, bytes);
                            }
                        }

                        catch (Exception exception)
                        {
                            logger.error(exception);
                        }
                    }
                }, "remote " + eventType + " subscriber").start();
            }


            final var eventConsumerAddress = eventType + ".consume";

            final var eventProducerAddress = eventType + ".produce";

            // product will be running in event helper vertical as separate worker thread while consume will be running in the event engine thread...
            //so, basically first consumer will ask for work in controller manner to avoid backpressure.
            //producer can also activate consumer by sending 1 event only to kick off produce,consumer cycle...

            if (eventForwarder)
            {
                vertx.eventBus().<byte[]>localConsumer(eventConsumerAddress, message ->
                {
                    try
                    {
                        forwarder.send(cipherUtil.encrypt(message.body()));
                    }
                    catch (Exception exception)
                    {
                        logger.error(exception);
                    }
                    finally
                    {
                        finishedEvents.incrementAndGet(); // for monitoring purpose

                        if (queuedEvents.decrementAndGet() == 0) // ask for more work, no need of timer
                        {
                            // send emit message...

                            vertx.eventBus().send(eventProducerAddress, eventQueueSize);
                        }

                    }
                }).exceptionHandler(logger::error);
            }
            else
            {
                if (blockingEvent)
                {
                    replyAddresses = new HashMap<>();

                    vertx.eventBus().<JsonObject>localConsumer(eventType + EventBusConstants.EVENT_REPLY, message ->
                    {
                        try
                        {
                            var replyAddress = replyAddresses.remove(message.body().getLong(EventBusConstants.EVENT_ID));

                            if (replyAddress != null)
                            {
                                vertx.eventBus().send(replyAddress, message.body());
                            }
                        }
                        catch (Exception exception)
                        {
                            logger.error(exception);
                        }
                        finally
                        {
                            finishedEvents.incrementAndGet(); // for monitoring purpose

                            if (queuedEvents.decrementAndGet() == 0) // ask for more work, no need of timer
                            {
                                // send emit message...

                                vertx.eventBus().send(eventProducerAddress, eventQueueSize);
                            }
                        }
                    });

                    vertx.eventBus().<byte[]>localConsumer(eventConsumerAddress, message ->
                    {
                        try
                        {
                            var event = new JsonObject(Buffer.buffer(message.body()));

                            if (event.containsKey(EventBusConstants.EVENT_REPLY) && event.getString(EventBusConstants.EVENT_REPLY).equalsIgnoreCase(YES))
                            {
                                replyAddresses.put(event.getLong(EventBusConstants.EVENT_ID), CommonUtil.getString(event.remove(EventBusConstants.EVENT_REPLY_ADDRESS)));
                            }

                            eventHandler.handle(event);
                        }
                        catch (Exception exception)
                        {
                            logger.error(exception);
                        }
                    }).exceptionHandler(logger::error);
                }
                else
                {
                    vertx.eventBus().<byte[]>localConsumer(eventConsumerAddress, message ->
                    {
                        try
                        {
                            eventHandler.handle(new JsonObject(Buffer.buffer(message.body())));
                        }
                        catch (Exception exception)
                        {
                            logger.error(exception);
                        }

                        finally
                        {
                            finishedEvents.incrementAndGet(); // for monitoring purpose

                            if (queuedEvents.decrementAndGet() == 0) // ask for more work, no need of timer
                            {
                                // send emit message...

                                vertx.eventBus().send(eventProducerAddress, eventQueueSize);
                            }
                        }
                    }).exceptionHandler(logger::error);
                }
            }

            startEventEngineHelper(promise); //start producer as event writer...
        }

        catch (Exception exception)
        {
            logger.error(exception);

            promise.fail(exception);

            logger.fatal(String.format("failed to start event engine %s...", eventType));
        }

        return this;
    }

    /**
     * Stops the EventEngine and cleans up all resources.
     * This method gracefully shuts down the event processing, closes ZeroMQ sockets,
     * and undeploys the EventEngineHelper verticle.
     *
     * @param vertx   The Vert.x instance used by this EventEngine
     * @param promise A promise that will be completed when shutdown is finished or failed if an error occurs
     */
    public void stop(Vertx vertx, Promise<Void> promise)
    {
        try
        {
            // Signal all components to stop processing events
            hasMoreEvent.set(false);

            // Clean up reply addresses for blocking events
            if (replyAddresses != null)
            {
                replyAddresses.clear();
            }

            // Close ZeroMQ sockets if they were opened
            if (forwarder != null)
            {
                forwarder.close();
            }

            if (remoteEventSubscriber != null)
            {
                remoteEventSubscriber.close();
            }

            // Undeploy the EventEngineHelper verticle if it exists and hasn't been automatically undeployed
            if (CommonUtil.isNotNullOrEmpty(eventEngineHelper) && vertx.deploymentIDs().contains(eventEngineHelper))
            {
                vertx.undeploy(eventEngineHelper, result ->
                {
                    if (result.succeeded())
                    {
                        promise.complete();
                    }
                    else
                    {
                        logger.error(result.cause());
                        promise.fail(result.cause());
                    }
                });
            }
            // No need to undeploy nested verticle, as undeploying parent verticle will first undeploy all its nested verticles
            else
            {
                promise.complete();
            }
        }
        catch (Exception exception)
        {
            logger.error(exception);
            promise.fail(exception);
        }
    }

    /**
     * Overloaded stop method that doesn't use a promise for completion notification.
     * This method performs the same cleanup as the Promise-based stop method but doesn't
     * provide completion notification.
     *
     * @param vertx The Vert.x instance used by this EventEngine
     */
    public void stop(Vertx vertx)
    {
        try
        {
            // Signal all components to stop processing events
            hasMoreEvent.set(false);

            // Clean up reply addresses for blocking events
            if (replyAddresses != null)
            {
                replyAddresses.clear();
            }

            // Close ZeroMQ sockets if they were opened
            if (forwarder != null)
            {
                forwarder.close();
            }

            if (remoteEventSubscriber != null)
            {
                remoteEventSubscriber.close();
            }

            // Undeploy the EventEngineHelper verticle if it exists and hasn't been automatically undeployed
            if (CommonUtil.isNotNullOrEmpty(eventEngineHelper) && vertx.deploymentIDs().contains(eventEngineHelper))
            {
                vertx.undeploy(eventEngineHelper, result ->
                {
                    if (result.failed())
                    {
                        logger.debug(result.cause());
                    }
                });
            }
        }
        catch (Exception exception)
        {
            logger.error(exception);
        }
    }

    /**
     * Starts the EventEngineHelper verticle which handles the actual event processing.
     * This method initializes default values for queue size and segments if not specified,
     * then deploys the helper verticle as a worker.
     *
     * @param promise A promise that will be completed when the helper is started or failed if an error occurs
     * @return A Future that will be completed when the helper is started
     */
    private Future<Void> startEventEngineHelper(Promise<Void> promise)
    {
        try
        {
            // Set default queue size based on event type
            if (blockingEvent)
            {
                if (eventQueueSize == 0)
                {
                    // Blocking events require more resources per event, so use a smaller queue
                    eventQueueSize = 100; // ToDo: based on deployment size
                }
            }
            else
            {
                if (eventQueueSize == 0)
                {
                    // Non-blocking events can be processed more efficiently, so use a larger queue
                    eventQueueSize = 5000; // ToDo: based on deployment size
                }
            }

            // Ensure at least 2 segments for event storage
            if (eventSegments < 1)
            {
                eventSegments = 2;
            }

            Bootstrap.vertx().deployVerticle(new EventEngineHelper().setEventType(eventType)
                    .setBlockingEvent(blockingEvent).setEventForwarder(eventForwarder).setPersist(persistEventOffset)
                    .setEventSegments(eventSegments).setRemoteEventProcessor(remoteEventProcessor)
                    .setLogger(logger), new DeploymentOptions().setThreadingModel(ThreadingModel.WORKER).setWorkerPoolSize(1).setWorkerPoolName("wp." + eventType + ".helper"), result ->
            {
                if (result.succeeded())
                {
                    promise.complete();

                    eventEngineHelper = result.result();
                }
                else
                {
                    promise.fail(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            logger.error(exception);

            promise.fail(exception.getCause());
        }

        return promise.future();
    }

    /**
     * Inner class that handles the actual event processing logic.
     * EventEngineHelper is deployed as a Vert.x worker verticle to handle event
     * production and consumption asynchronously from the main EventEngine.
     * It manages the event store, handles event persistence, and coordinates
     * the flow of events between producers and consumers.
     */
    private class EventEngineHelper extends AbstractVerticle
    {
        private Logger logger;
        private String eventType;
        private boolean persist;
        private boolean blockingEvent;
        private EventStore eventStore;
        private boolean remoteEventProcessor;
        private boolean eventForwarder;
        private long pendingEvents;
        private boolean consumerActive;
        private boolean dirty;
        private int eventSegments;

        /**
         * Configures whether this helper should process events from remote sources.
         *
         * @param remoteEventProcessor True to enable remote event processing
         * @return This EventEngineHelper instance for method chaining
         */
        public EventEngineHelper setRemoteEventProcessor(boolean remoteEventProcessor)
        {
            this.remoteEventProcessor = remoteEventProcessor;
            return this;
        }

        /**
         * Configures whether events should be processed in a blocking manner.
         *
         * @param blockingEvent True for blocking event processing
         * @return This EventEngineHelper instance for method chaining
         */
        public EventEngineHelper setBlockingEvent(boolean blockingEvent)
        {
            this.blockingEvent = blockingEvent;
            return this;
        }

        /**
         * Sets the event type identifier for this helper.
         *
         * @param eventType The event type identifier
         * @return This EventEngineHelper instance for method chaining
         */
        public EventEngineHelper setEventType(String eventType)
        {
            this.eventType = eventType;
            return this;
        }

        /**
         * Configures whether event offsets should be persisted.
         *
         * @param persist True to enable event persistence
         * @return This EventEngineHelper instance for method chaining
         */
        public EventEngineHelper setPersist(boolean persist)
        {
            this.persist = persist;
            return this;
        }

        /**
         * Configures whether this helper should forward events.
         *
         * @param eventForwarder True to enable event forwarding
         * @return This EventEngineHelper instance for method chaining
         */
        public EventEngineHelper setEventForwarder(boolean eventForwarder)
        {
            this.eventForwarder = eventForwarder;
            return this;
        }

        /**
         * Sets the number of event segments to use for storage.
         *
         * @param eventSegments The number of event segments
         * @return This EventEngineHelper instance for method chaining
         */
        public EventEngineHelper setEventSegments(int eventSegments)
        {
            this.eventSegments = eventSegments;
            return this;
        }

        /**
         * Sets the logger instance for this helper.
         *
         * @param logger The logger instance
         * @return This EventEngineHelper instance for method chaining
         */
        public EventEngineHelper setLogger(Logger logger)
        {
            this.logger = logger;
            return this;
        }

        /**
         * Starts the EventEngineHelper and initializes all required components.
         * This method sets up event handlers, initializes the event store,
         * and configures event processing based on the helper's configuration.
         *
         * @param promise A promise that will be completed when startup is finished or failed if an error occurs
         * @throws Exception If an error occurs during startup
         */
        @Override
        public void start(Promise<Void> promise) throws Exception
        {
            try
            {
                // Define the address for event production
                final var eventProducerAddress = eventType + ".produce";

                // Create directory for event storage if it doesn't exist
                var storeDir = new File(CURRENT_DIR + PATH_SEPARATOR + EventBusConstants.EVENT_DIR + PATH_SEPARATOR + EventBusConstants.replace(eventType));

                if (!storeDir.exists())
                {
                    storeDir.mkdirs();
                }

                eventStore = new EventStore(storeDir.getPath()).setStoreName(eventType).setMaxSegments(eventSegments);

                if (this.remoteEventProcessor)
                {
                    vertx.eventBus().<byte[]>localConsumer(eventType, message ->
                    {
                        try
                        {
                            eventStore.put(message.body());

                            pendingEvents++;

                            dirty = true; // avoid unnecessary bookmarks flush

                            if (!consumerActive) //if consume is not active then we have to awake it by sending 1 event
                            {
                                consumerActive = true;

                                vertx.eventBus().send(eventProducerAddress, 1);
                            }
                        }
                        catch (Exception exception)
                        {
                            logger.error(exception);
                        }
                    }).exceptionHandler(logger::error);
                }

                else if (this.blockingEvent)
                {
                    vertx.eventBus().<JsonObject>localConsumer(eventType, message ->
                    {
                        try
                        {
                            var event = message.body();

                            if (!event.containsKey(EventBusConstants.EVENT_ID))
                            {
                                event.put(EventBusConstants.EVENT_ID, CommonUtil.newEventId());
                            }

                            if (event.containsKey(EventBusConstants.EVENT_REPLY) && event.getString(EventBusConstants.EVENT_REPLY).equalsIgnoreCase(YES))
                            {
                                event.put(EventBusConstants.EVENT_REPLY_ADDRESS, message.replyAddress());
                            }

                            eventStore.put(event);

                            pendingEvents++;

                            dirty = true; // avoid unnecessary bookmarks flush

                            if (!consumerActive) //if consume is not active then we have to awake it by sending 1 event
                            {
                                consumerActive = true;

                                vertx.eventBus().send(eventProducerAddress, 1);
                            }
                        }
                        catch (Exception exception)
                        {
                            logger.error(exception);
                        }


                    }).exceptionHandler(logger::error);
                }

                else
                {
                    vertx.eventBus().<JsonObject>localConsumer(eventType, message ->
                    {
                        try
                        {
                            eventStore.put(message.body());

                            pendingEvents++;

                            dirty = true; // avoid unnecessary bookmarks flush

                            if (!consumerActive)  //if consume is not active then we have to awake it by sending 1 event
                            {
                                consumerActive = true;

                                vertx.eventBus().send(eventProducerAddress, 1);
                            }
                        }
                        catch (Exception exception)
                        {
                            logger.error(exception);
                        }
                    }).exceptionHandler(logger::error);
                }

                final var eventConsumerAddress = eventType + ".consume";

                vertx.eventBus().<Integer>localConsumer(eventProducerAddress, message ->  // emmit events either by activating consumer or consumer will ask if queuedevent counter goes 0
                {
                    try
                    {
                        if (pendingEvents > 0)
                        {
                            byte[] bytes;

                            var size = message.body() > pendingEvents ? pendingEvents : message.body(); // if pending event is smaller than capacity, then adjust event emitter counter

                            for (var i = 0; i < size; i++)
                            {
                                try
                                {
                                    bytes = eventStore.get(); // raw bytes or uncompressed bytes as per consumer type...

                                    if (bytes != null)
                                    {
                                        queuedEvents.incrementAndGet();  // increment queuedevents counter for looping and monitoring purpose...

                                        vertx.eventBus().send(eventConsumerAddress, CodecUtil.toBytes(bytes, eventType)); //notify consumer for the event

                                        pendingEvents--;
                                    }
                                    else
                                    {
                                        break;
                                    }
                                }
                                catch (Exception exception)
                                {
                                    logger.error(exception);

                                    logger.warn(String.format("corrupted event received in %s", eventType));
                                }
                            }

                            if (queuedEvents.get() == 0) //todo: confusion: needs to find out why bytes are null
                            {
                                consumerActive = false; // no more events means consumer will become inactive
                            }
                        }
                        else
                        {
                            consumerActive = false; // no more events means consumer will become inactive
                        }
                    }
                    catch (Exception exception)
                    {
                        logger.error(exception);
                    }
                }).exceptionHandler(logger::error);

                vertx.eventBus().<Void>localConsumer(EventBusConstants.EVENT_ENGINE_STATS, message ->
                {
                    if (MotadataConfigUtil.devMode())
                    {
                        vertx.eventBus().publish(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.APP && (Bootstrap.getInstallationMode().equalsIgnoreCase(GlobalConstants.InstallationMode.PRIMARY.name()) || Bootstrap.getInstallationMode().equalsIgnoreCase(GlobalConstants.InstallationMode.STANDALONE.name())) ? EventBusConstants.EVENT_ENGINE_STATS_RESPONSE : EventBusConstants.EVENT_REMOTE,
                                new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ENGINE_STATS).put(EventBusConstants.ENGINE_TYPE, eventType).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                        .put(HealthUtil.HEALTH_STATS, new JsonObject().put(EventBusConstants.PENDING_EVENTS, pendingEvents)
                                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, Bootstrap.bootstrapType().name()).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode())
                                                .put(EventBusConstants.FINISHED_EVENTS, finishedEvents.get())
                                                .put(EventBusConstants.QUEUED_EVENTS, queuedEvents.get())
                                                .put(EventBusConstants.ENGINE_CATEGORY, "default")));
                    }
                    else
                    {
                        vertx.eventBus().send(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.APP && (Bootstrap.getInstallationMode().equalsIgnoreCase(GlobalConstants.InstallationMode.PRIMARY.name()) || Bootstrap.getInstallationMode().equalsIgnoreCase(GlobalConstants.InstallationMode.STANDALONE.name())) ? EventBusConstants.EVENT_ENGINE_STATS_RESPONSE : EventBusConstants.EVENT_REMOTE,  // send eventEngine stats to any remote entity
                                new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ENGINE_STATS).put(EventBusConstants.ENGINE_TYPE, eventType)
                                        .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                        .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, Bootstrap.bootstrapType().name()).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode())
                                        .put(HealthUtil.HEALTH_STATS, new JsonObject().put(EventBusConstants.PENDING_EVENTS, pendingEvents)
                                                .put(EventBusConstants.FINISHED_EVENTS, finishedEvents.get())
                                                .put(EventBusConstants.QUEUED_EVENTS, queuedEvents.get())
                                                .put(EventBusConstants.ENGINE_CATEGORY, "default")));
                    }
                });

                if (persist)
                {
                    pendingEvents = eventStore.load();

                    vertx.setPeriodic(30 * 1000L, timer ->
                    {
                        try
                        {
                            if (dirty)
                            {
                                eventStore.save();

                                dirty = false;
                            }
                        }
                        catch (Exception exception)
                        {
                            logger.warn(String.format("error occurred while saving bookmark %s", eventType));

                            logger.error(exception);
                        }
                    });

                    logger.debug(String.format("event store %s loaded...", eventType));

                    // for the older event , we must activate consumer...
                    //if consume is not active then we have to awake it by sending 1 event
                    if (pendingEvents > 0 && !consumerActive)
                    {
                        consumerActive = true;

                        vertx.eventBus().send(eventProducerAddress, 1);
                    }
                }
                else
                {
                    eventStore.init();

                    logger.debug(String.format("initialize event store %s...", eventType));
                }

                promise.complete();
            }

            catch (Exception exception)
            {
                logger.error(exception);

                logger.fatal(String.format("failed to start event engine helper %s...", eventType));

                promise.fail(exception);

            }
        }

        /**
         * Stops the EventEngineHelper and cleans up all resources.
         * This method saves any pending events if persistence is enabled,
         * closes the event store, and completes the provided promise.
         *
         * @param promise A promise that will be completed when shutdown is finished or failed if an error occurs
         * @throws Exception If an error occurs during shutdown
         */
        @Override
        public void stop(Promise<Void> promise) throws Exception
        {
            try
            {
                // Signal all components to stop processing events
                hasMoreEvent.set(false);

                // Save any pending events if persistence is enabled and there are unsaved changes
                if (persist && dirty)
                {
                    eventStore.save();
                }

                // Close the event store if it was initialized
                if (eventStore != null)
                {
                    eventStore.close();
                }

                promise.complete();
            }
            catch (Exception exception)
            {
                logger.error(exception);
                logger.fatal(String.format("failed to stop event engine helper %s...", eventType));
                promise.fail(exception);
            }
        }
    }
}
