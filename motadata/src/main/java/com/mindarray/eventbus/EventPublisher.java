/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.eventbus;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.store.RemoteEventProcessorCacheStore;
import com.mindarray.store.RemoteEventProcessorConfigStore;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;
import org.zeromq.SocketType;
import org.zeromq.ZMQ;

import java.time.Duration;
import java.util.concurrent.locks.LockSupport;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.Agent.AGENT_UUID;
import static com.mindarray.eventbus.EventBusConstants.*;

public class EventPublisher extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(EventPublisher.class, GlobalConstants.MOTADATA_EVENT_BUS, "Event Publisher");

    private final ZMQ.Socket publisher;

    private final int publisherPort;

    private final String publisherType;

    public EventPublisher(int port, String type)
    {
        publisher = Bootstrap.zcontext().socket(SocketType.PUB);

        publisherPort = port;

        publisherType = type;
    }

    @Override
    public void start(Promise<Void> promise) throws Exception
    {

        publisher.setXpubNoDrop(true);

        publisher.setSndHWM(MotadataConfigUtil.getEventBacklogSize());

        publisher.setHWM(MotadataConfigUtil.getEventBacklogSize());

        publisher.setSendTimeOut(0);

        publisher.bind("tcp://*:" + publisherPort);

        LockSupport.parkNanos(Duration.ofMillis(100).toNanos()); // Eliminate slow subscriber problem

        LOGGER.info(String.format("event publisher: %s bind to port %s", config().getString(EVENT_TYPE), publisherPort));

        var cipherUtil = new CipherUtil();

        vertx.eventBus().<JsonObject>localConsumer(config().getString(EVENT_TYPE), message ->
        {
            try
            {
                var event = message.body();

                if (CommonUtil.isNotNullOrEmpty(event.getString(EVENT_TOPIC)))
                {
                    var uuid = EMPTY_VALUE;

                    switch (event.getString(EVENT_TOPIC))
                    {
                        case MOTADATA_MANAGER_TOPIC ->
                        {
                            if (event.containsKey(AGENT_UUID))
                            {
                                event.put(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.AGENT.name());

                                uuid = event.getString(AGENT_UUID, EMPTY_VALUE);
                            }
                            else if (event.containsKey(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID))
                            {
                                event.put(SYSTEM_BOOTSTRAP_TYPE, event.getString(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.COLLECTOR.name()));

                                uuid = event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, EMPTY_VALUE);
                            }

                            if (!uuid.isEmpty())
                            {
                                send(uuid + DOT_SEPARATOR + MOTADATA_MANAGER_TOPIC, event.encode().getBytes());
                            }

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("sending event %s to motadata manager %s for bootstrap type %s ", event.getString(EVENT_TYPE), uuid, event.getString(SYSTEM_BOOTSTRAP_TYPE)));
                            }
                        }

                        case AGENT_TOPIC ->
                        {
                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("sending event %s with event topic %s to agent %s", event.getString(EVENT_TYPE), event.getString(EVENT_TOPIC), event.getString(AGENT_UUID)));
                            }

                            send(event.getString(AGENT_UUID) + "." + AGENT_TOPIC, cipherUtil.encrypt(CodecUtil.compress(message.body().encode())));
                        }

                        case REMOTE_EVENT_PROCESSOR_TOPIC ->
                        {
                            // If event processor uuid available than publish event to that particular collector else publish to all available collector
                            if (CommonUtil.isNotNullOrEmpty(event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID)))
                            {
                                send(cipherUtil.encrypt(CodecUtil.compress(message.body().encode())), event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID), event.getString(EVENT_TYPE), REMOTE_EVENT_PROCESSOR_TOPIC);
                            }
                            else
                            {

                                RemoteEventProcessorConfigStore.getStore().getItems().stream().filter(item -> !JsonObject.mapFrom(item).getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID).equalsIgnoreCase(Bootstrap.getRegistrationId())
                                                && RemoteEventProcessorCacheStore.getStore().getDuration(JsonObject.mapFrom(item).getLong(ID)) > DUMMY_ID)
                                        .forEach(item -> send(cipherUtil.encrypt(CodecUtil.compress(message.body().encode())), JsonObject.mapFrom(item).getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID), event.getString(EVENT_TYPE), REMOTE_EVENT_PROCESSOR_TOPIC));
                            }
                        }

                        case DATASTORE_BROKER_OPERATION_TOPIC ->
                                send(event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID) + DOT_SEPARATOR + DATASTORE_BROKER_OPERATION_TOPIC, event.getBinary(EVENT_CONTEXT));

                        case DATASTORE_OPERATION_TOPIC ->
                                send(RemoteEventProcessorCacheStore.getStore().getActiveDatastoreProcessor() + DOT_SEPARATOR + DATASTORE_OPERATION_TOPIC, event.getBinary(EVENT_CONTEXT));

                        case DATASTORE_QUERY_TOPIC ->
                        {
                            var processor = RemoteEventProcessorCacheStore.getStore().getActiveDatastoreProcessor();

                            if (CommonUtil.traceEnabled())
                            {
                                var buffer = Buffer.buffer(event.getBinary(EVENT_CONTEXT));

                                if (CommonUtil.traceEnabled())
                                {
                                    LOGGER.trace("sending Datastore Query :: " + new JsonObject(buffer.getString(1, buffer.length())));
                                }
                            }

                            if (MotadataConfigUtil.devMode() && processor.equalsIgnoreCase(EMPTY_VALUE))
                            {
                                send(RemoteEventProcessorConfigStore.getStore().getItem(RemoteEventProcessorConfigStore.getStore().getItemByMode(GlobalConstants.InstallationMode.PRIMARY)).getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID) + DOT_SEPARATOR + DATASTORE_QUERY_TOPIC, event.getBinary(EVENT_CONTEXT));
                            }
                            else
                            {
                                send(processor + DOT_SEPARATOR + DATASTORE_QUERY_TOPIC, event.getBinary(EVENT_CONTEXT));
                            }

                        }

                        case DATASTORE_CONNECTION_ALIVE_TOPIC ->        // this separate topic is for sending heartbeat/keep alive to 'all' datastore
                                send(event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID) + DOT_SEPARATOR + event.getString(REMOTE_EVENT_PROCESSOR_TOPIC), event.getBinary(EVENT_CONTEXT));

                        default ->
                        {
                            if (CommonUtil.debugEnabled())
                                LOGGER.warn(String.format("received unknown event %s", event));
                        }
                    }
                }

                else
                {
                    send(event.getString(EVENT_TYPE) + EVENT_TOPIC_DELIMITER, cipherUtil.encrypt(CodecUtil.compress(message.body().encode())));
                }
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        promise.complete();
    }

    private void send(byte[] bytes, String uuid, String eventType, String topic)
    {
        if (CommonUtil.traceEnabled())
        {
            LOGGER.trace(String.format("sending event %s to collector uuid %s and topic %s", eventType, uuid, topic));
        }

        publisher.send((uuid + "." + topic).getBytes(), ZMQ.SNDMORE);

        publisher.send(bytes);
    }

    private void send(String topic, byte[] bytes)
    {
        if (CommonUtil.traceEnabled())
        {
            LOGGER.trace(String.format("sending event to topic %s", topic));
        }

        publisher.send(topic.getBytes(), ZMQ.SNDMORE);

        publisher.send(bytes);

    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {

        publisher.close();

        promise.complete();

    }
}
