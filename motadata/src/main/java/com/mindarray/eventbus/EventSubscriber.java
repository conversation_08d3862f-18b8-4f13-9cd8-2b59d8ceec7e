/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			Notes
 *  6-Feb-2025		<PERSON><PERSON>		MOTADATA-4878: refactored code relatd to datastore init event
 *
 */

package com.mindarray.eventbus;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.config.ConfigConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.ConfigurationCacheStore;
import com.mindarray.store.RemoteEventProcessorCacheStore;
import com.mindarray.store.RemoteEventProcessorConfigStore;
import com.mindarray.streaming.StreamingEngine;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;
import org.zeromq.SocketType;
import org.zeromq.ZMQ;

import java.util.concurrent.atomic.AtomicBoolean;

import static com.mindarray.GlobalConstants.ID;
import static com.mindarray.GlobalConstants.STATUS;
import static com.mindarray.datastore.DatastoreConstants.OPERATION_TYPE;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.ha.HAConstants.HA_SYNC_OPERATION;

public class EventSubscriber extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(EventSubscriber.class, GlobalConstants.MOTADATA_EVENT_BUS, "Event Subscriber");

    private final ZMQ.Socket subscriber;

    private final int subscriberPort;

    private final String subscriberIP;

    private final String subscriberType;

    private final AtomicBoolean hasMoreEvent = new AtomicBoolean(true);

    private boolean streaming = false;

    public EventSubscriber(int port, String type, String ip)
    {
        subscriber = Bootstrap.zcontext().socket(SocketType.PULL);

        subscriberPort = port;

        subscriberType = type;

        subscriberIP = ip;
    }

    public EventSubscriber(int port, String type)
    {
        subscriber = Bootstrap.zcontext().socket(SocketType.PULL);

        subscriberPort = port;

        subscriberType = type;

        subscriberIP = "*";
    }

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        var cipherUtil = new CipherUtil();

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
        {
            switch (ChangeNotificationType.valueOf(message.body().getString(CHANGE_NOTIFICATION_TYPE)))
            {
                case START_LOG_TAIL -> streaming = true;

                case STOP_LOG_TAIL -> streaming = false;
            }

        }).exceptionHandler(LOGGER::error);

        if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.EVENT_COLLECTOR || Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.FLOW_COLLECTOR)
        {
            vertx.eventBus().<byte[]>localConsumer(EVENT_REMOTE_EVENT_PROCESSOR, message ->
            {
                try
                {
                    if (message.body() != null && message.body().length > 0)
                    {
                        var buffer = Buffer.buffer(message.body());

                        var position = 0;

                        var topic = buffer.getString(2, buffer.getShortLE(0) + 2);

                        position = 2 + buffer.getShortLE(0);

                        if (topic.startsWith(FLOW_TOPIC))
                        {
                            vertx.eventBus().send(EVENT_FLOW, new JsonObject(buffer.getBuffer(position, buffer.length())).put(EVENT_VOLUME_BYTES, buffer.length()));
                        }

                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

            }).exceptionHandler(LOGGER::error);
        }
        else
        {
            vertx.eventBus().<byte[]>localConsumer(EVENT_REMOTE, message ->
            {
                try
                {
                    if (message.body() != null && message.body().length > 0)
                    {
                        var buffer = Buffer.buffer(message.body());

                        var position = 0;

                        var topic = buffer.getString(2, buffer.getShortLE(0) + 2);

                        position = 2 + buffer.getShortLE(0);

                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format("event received for topic : %s ", topic));
                        }

                        if (topic.contains(REMOTE_EVENT_PROCESSOR_TOPIC))
                        {
                            var event = CodecUtil.toJSONObject(cipherUtil.decrypt(buffer.getBytes(position, buffer.length())));

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("event received for event type : %s ", event.getString(EVENT_TYPE)));
                            }

                            switch (event.getString(EVENT_TYPE))
                            {
                                // event processor register / event acknowledge event
                                case EVENT_REGISTRATION ->
                                        vertx.eventBus().send(event.getString(EVENT_TYPE), event.put(EVENT_COPY_REQUIRED, false));

                                case EVENT_ACKNOWLEDGEMENT ->
                                        vertx.eventBus().publish(event.getString(EVENT_TYPE), event.put(EVENT_COPY_REQUIRED, false));

                                case EVENT_ENGINE_STATS ->
                                        vertx.eventBus().publish(EVENT_ENGINE_STATS_RESPONSE, event.put(EVENT_COPY_REQUIRED, false));

                                case EVENT_JVM_STATS ->
                                        vertx.eventBus().publish(EVENT_JVM_STATS_RESPONSE, event.put(EVENT_COPY_REQUIRED, false));

                                //remote event discovery response
                                case EVENT_DISCOVERY ->
                                        vertx.eventBus().send(EVENT_DISCOVERY_RESPONSE, event.put(EVENT_COPY_REQUIRED, false));

                                //remote event metric poll response
                                case EVENT_METRIC_POLL -> vertx.eventBus().publish(EVENT_METRIC_POLL_RESPONSE, event);

                                //remote event rediscovery response
                                case EVENT_REDISCOVER -> vertx.eventBus().send(EVENT_REDISCOVER_RESPONSE, event);

                                //remote event topology response
                                case EVENT_TOPOLOGY ->
                                        vertx.eventBus().send(EVENT_TOPOLOGY_RESPONSE, event.put(EVENT_COPY_REQUIRED, false));

                                //event processor heartbeat event
                                case EVENT_REMOTE_PROCESSOR_HEARTBEAT ->
                                {
                                    if (CommonUtil.isNotNullOrEmpty(event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID))
                                            && RemoteEventProcessorConfigStore.getStore().getItemIdByUUID(event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID), event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE), event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE)) != null)
                                    {
                                        RemoteEventProcessorCacheStore.getStore().updateDuration(RemoteEventProcessorConfigStore.getStore().getItemIdByUUID(event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID), event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE), event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE)), event.getLong(GlobalConstants.DURATION));
                                    }
                                }

                                //remote event plugin engine response
                                case EVENT_PLUGIN_ENGINE ->
                                {
                                    if (event.containsKey(WorkerUtil.WORKER_CONTEXT)) // worker id tracker
                                    {
                                        vertx.eventBus().send(EVENT_STREAMING_WORKER_TRACKER, event.put(EVENT_COPY_REQUIRED, false));
                                    }
                                    else
                                    {
                                        if (event.containsKey(ConfigConstants.CONFIG_OPERATION) && event.getString(ConfigConstants.CONFIG_OPERATION).equalsIgnoreCase(ConfigConstants.ConfigOperation.UPGRADE.getName()))
                                        {
                                            if (event.containsKey(STATUS) && event.containsKey(NMSConstants.STATE))
                                            {
                                                vertx.eventBus().send(EVENT_PLUGIN_ENGINE_RESPONSE, event);
                                            }
                                            else
                                            {
                                                EventBusConstants.publish(EventBusConstants.EVENT_CONFIG_REQUEST_STATE_CHANGE, event); // why this ?? Collector will send state.change event to Master and master needs to publish it to UI.

                                                ConfigurationCacheStore.getStore().updateOperation(event.getLong(ID), ConfigConstants.ConfigOperation.UPGRADE, event.getString(NMSConstants.STATE));
                                            }
                                        }
                                        else
                                        {
                                            vertx.eventBus().send(EVENT_PLUGIN_ENGINE_RESPONSE, event.put(EVENT_COPY_REQUIRED, false));
                                        }
                                    }
                                }

                                //log parsing event send to process worker
                                case EVENT_LOG -> //events from log collector
                                {
                                    if (streaming)
                                    {
                                        vertx.eventBus().send(EVENT_STREAMING_BROADCAST, new JsonObject().put(StreamingEngine.STREAMING_TYPE, StreamingEngine.StreamingType.LOG_TAIL.getName()).put(EVENT_CONTEXT, event).put(EVENT_COPY_REQUIRED, false));
                                    }

                                    if (event.containsKey(EVENT_VOLUME_BYTES) && LicenseUtil.updateUsedLogQuota(event.getInteger(EVENT_VOLUME_BYTES)))
                                    {
                                        vertx.eventBus().send(EVENT_LOG, event.put(EVENT_COPY_REQUIRED, false));
                                    }
                                }


                                case EVENT_HA_DATASTORE_SECONDARY_SYNC ->
                                        vertx.eventBus().send(EVENT_HA_DATASTORE_SECONDARY_SYNC, event);

                                case EVENT_APP -> HAConstants.switchOverIP(event.getValue(HA_SYNC_OPERATION));

                                case EVENT_FLOW ->
                                {
                                    if (event.containsKey(EVENT_VOLUME_BYTES) && LicenseUtil.updateUsedFlowQuota(event.getInteger(EVENT_VOLUME_BYTES)))
                                    {
                                        event.remove(EVENT_TYPE);

                                        vertx.eventBus().send(EVENT_FLOW, event.put("peer_ip_src", event.getString(EVENT_SOURCE)));
                                    }
                                }

                                case EVENT_HA_CONFIG_OBSERVER_SYNC ->
                                        vertx.eventBus().send(EVENT_HA_CONFIG_OBSERVER_SYNC, event);

                                case EVENT_HA_CACHE_OBSERVER_SYNC ->
                                        vertx.eventBus().send(EVENT_HA_CACHE_OBSERVER_SYNC, event);

                                default -> vertx.eventBus().send(CommonUtil.getString(event.remove(EVENT_TYPE)), event);
                            }
                        }
                        else if (topic.contains(DATASTORE_QUERY_RESPONSE_TOPIC)) // report db query response event
                        {
                            vertx.eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, buffer.getBytes(position, buffer.length()));
                        }
                        else if (topic.contains(DATASTORE_OPERATION_TOPIC)) // report db event topic
                        {
                            var context = CodecUtil.toJSONObject(buffer.getBytes(position, buffer.length()));

                            if (context.containsKey(OPERATION_TYPE))
                            {
                                if (context.getInteger(OPERATION_TYPE) == DatastoreConstants.OperationType.DATA_FLUSH.getName().intValue())
                                {
                                    if (RemoteEventProcessorCacheStore.getStore().getActiveDatastoreProcessor().equalsIgnoreCase(context.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID)))
                                    {
                                        vertx.eventBus().publish(EVENT_DATASTORE_FLUSH, context.put(EVENT_COPY_REQUIRED, false));
                                    }
                                }
                                else if (context.getInteger(OPERATION_TYPE) == DatastoreConstants.OperationType.DATASTORE_INIT.getName().intValue())
                                {
                                    vertx.eventBus().send(EVENT_DATASTORE_INIT, context.put(EVENT_COPY_REQUIRED, false));
                                }
                                else if (context.getInteger(OPERATION_TYPE) == DatastoreConstants.OperationType.INDEX_UPDATE.getName().intValue())
                                {
                                    if (RemoteEventProcessorCacheStore.getStore().getActiveDatastoreProcessor().equalsIgnoreCase(context.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID)))
                                    {
                                        vertx.eventBus().publish(EVENT_INDEXABLE_COLUMN_UPDATE, context.put(EVENT_COPY_REQUIRED, false));
                                    }
                                }
                                else if (context.getInteger(OPERATION_TYPE) == DatastoreConstants.OperationType.HEALTH_STATS.getName().intValue())
                                {
                                    var item = RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, context.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID));

                                    if (item != null)
                                    {
                                        Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_STATS_RESPONSE, context.put(EVENT_SOURCE, item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP)));
                                    }

                                }
                            }
                            else if (context.containsKey(EVENT_TYPE) && context.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_REGISTRATION))
                            {
                                vertx.eventBus().send(EVENT_REGISTRATION, context);

                                vertx.eventBus().send(EVENT_DATASTORE_REGISTER, context);
                            }
                            else if (context.containsKey(EVENT_TYPE) && context.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_REMOTE_PROCESSOR_HEARTBEAT))
                            {
                                if (CommonUtil.isNotNullOrEmpty(context.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID))
                                        && RemoteEventProcessorConfigStore.getStore().getItemIdByUUID(context.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID), context.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE), getInstallationMode(context)) != null)
                                {
                                    RemoteEventProcessorCacheStore.getStore().updateDuration(RemoteEventProcessorConfigStore.getStore().getItemIdByUUID(context.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID), context.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE), getInstallationMode(context)), context.getLong(GlobalConstants.DURATION));
                                }
                            }
                        }

                        else if (topic.contains(DATASTORE_QUERY_ACK_TOPIC))
                        {
                            vertx.eventBus().send(EVENT_DATASTORE_ACKNOWLEDGEMENT, buffer.getLongLE(position));
                        }

                        else if (topic.contains(AGENT_TOPIC)) //agent event
                        {
                            vertx.eventBus().publish(EVENT_AGENT, CodecUtil.toJSONObject(cipherUtil.decrypt(buffer.getBytes(position, buffer.length()))).put(EVENT_COPY_REQUIRED, false));
                        }
                        else if (topic.contains(MOTADATA_MANAGER_TOPIC)) //motadata manager event (for collector/agent start/stop or backup/restore/master upgrade)
                        {
                            if (MotadataConfigUtil.devMode())
                            {
                                vertx.eventBus().publish(EVENT_MANAGER_RESPONSE_PROCESSOR, new JsonObject(buffer.getBuffer(position, buffer.length())));
                            }
                            else
                            {
                                vertx.eventBus().send(EVENT_MANAGER_RESPONSE_PROCESSOR, new JsonObject(buffer.getBuffer(position, buffer.length())));
                            }
                        }
                        else if (topic.contains(MOTADATA_OBSERVER_TOPIC))
                        {
                            var event = CodecUtil.toJSONObject(cipherUtil.decrypt(buffer.getBytes(position, buffer.length())));

                            vertx.eventBus().send(event.getString(EVENT_TYPE), event);
                        }
                        else if (topic.startsWith(FLOW_TOPIC))
                        {
                            if (LicenseUtil.updateUsedFlowQuota(buffer.length()))
                            {
                                vertx.eventBus().send(EVENT_FLOW, new JsonObject(buffer.getBuffer(position, buffer.length())).put(EVENT_VOLUME_BYTES, buffer.length()));
                            }
                        }
                        else if (topic.contains(SHUTDOWN_TOPIC))
                        {
                            Bootstrap.stop(true, null, true);
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

            }).exceptionHandler(LOGGER::error);
        }

        subscribe(subscriber, subscriberPort, subscriberType, subscriberIP, (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.FLOW_COLLECTOR || Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.EVENT_COLLECTOR) ? EVENT_REMOTE_EVENT_PROCESSOR : EVENT_REMOTE);

        promise.complete();
    }

    private void subscribe(ZMQ.Socket zmqSubscriber, int port, String filter, String ip, String eventType)
    {
        zmqSubscriber.setHWM(MotadataConfigUtil.getEventBacklogSize());

        zmqSubscriber.setRcvHWM(MotadataConfigUtil.getEventBacklogSize());

        zmqSubscriber.bind("tcp://" + ip + ":" + port);

        LOGGER.info(String.format("%s bind to port %s with queue size : %s ", filter.toLowerCase(), port, MotadataConfigUtil.getEventBacklogSize()));

        new Thread(() ->
        {
            while (hasMoreEvent.get())
            {
                try
                {
                    var bytes = zmqSubscriber.recv();

                    if (bytes != null && bytes.length > 0)
                    {
                        vertx.eventBus().send(eventType, bytes);
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }

            LOGGER.warn(String.format("%s disconnected......", filter));

            LOGGER.info(String.format("stopping motadata %s ....", Bootstrap.bootstrapType().toString().toLowerCase()));

            //Bootstrap.stop(true, null);

        }, filter).start();
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        hasMoreEvent.set(false);

        subscriber.close();

        promise.complete();

    }
}
