/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/**
 * The Network Management System (NMS) package provides core functionality for monitoring and managing network devices and systems.
 * <p>
 * This package contains components that work together to provide comprehensive network monitoring capabilities:
 * <p>
 * <strong>Discovery and Topology:</strong>
 * <ul>
 *   <li>{@link com.mindarray.nms.DiscoveryEngine} - Discovers network devices using various protocols</li>
 *   <li>{@link com.mindarray.nms.RediscoverEngine} - Performs rediscovery operations on existing objects</li>
 *   <li>{@link com.mindarray.nms.TopologyEngine} - Maps network topology and device relationships</li>
 * </ul>
 * <p>
 * <strong>Metric Collection and Processing:</strong>
 * <ul>
 *   <li>{@link com.mindarray.nms.MetricScheduler} - Schedules metric collection based on configured intervals</li>
 *   <li>{@link com.mindarray.nms.MetricPoller} - Executes metric polling operations against monitored devices</li>
 *   <li>{@link com.mindarray.nms.MetricEnricher} - Processes and enriches collected metric data</li>
 *   <li>{@link com.mindarray.nms.ResponseProcessor} - Handles responses from various monitoring operations</li>
 *   <li>{@link com.mindarray.nms.AutoScaler} - Dynamically scales polling resources based on system load</li>
 * </ul>
 * <p>
 * <strong>Status Management:</strong>
 * <ul>
 *   <li>{@link com.mindarray.nms.ObjectStatusCalculator} - Calculates and tracks object status information</li>
 *   <li>{@link com.mindarray.nms.ObjectStatusChangeEventProcessor} - Processes and notifies about status changes</li>
 * </ul>
 * <p>
 * <strong>SNMP Trap Handling:</strong>
 * <ul>
 *   <li>{@link com.mindarray.nms.SNMPTrapListener} - Listens for SNMP traps from network devices</li>
 *   <li>{@link com.mindarray.nms.SNMPTrapProcessor} - Processes and correlates received SNMP traps</li>
 * </ul>
 * <p>
 * <strong>Object Management:</strong>
 * <ul>
 *   <li>{@link com.mindarray.nms.ObjectManager} - Manages the lifecycle of monitored objects and metrics</li>
 * </ul>
 * <p>
 * <strong>Constants and Utilities:</strong>
 * <ul>
 *   <li>{@link com.mindarray.nms.NMSConstants} - Provides constants and utility methods used throughout the NMS package</li>
 * </ul>
 * <p>
 * These components work together to provide a complete network monitoring solution that can discover,
 * monitor, and manage network devices and systems. The package is designed to be scalable, extensible,
 * and capable of handling large network environments with diverse device types.
 */
package com.mindarray.nms;