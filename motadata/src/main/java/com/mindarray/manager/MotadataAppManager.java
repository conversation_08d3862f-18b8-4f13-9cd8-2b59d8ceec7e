/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	26-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
*/

package com.mindarray.manager;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Agent;
import com.mindarray.api.BackupProfile;
import com.mindarray.api.StorageProfile;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.eventbus.RemoteEventForwarder;
import com.mindarray.eventbus.RemoteEventSubscriber;
import com.mindarray.ha.HAManager;
import com.mindarray.nms.NMSConstants;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.file.OpenOptions;
import io.vertx.core.http.*;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.core.net.PemKeyCertOptions;
import io.vertx.ext.bridge.BridgeEventType;
import io.vertx.ext.bridge.PermittedOptions;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.codec.BodyCodec;
import io.vertx.ext.web.handler.FileSystemAccess;
import io.vertx.ext.web.handler.StaticHandler;
import io.vertx.ext.web.handler.sockjs.SockJSBridgeOptions;
import io.vertx.ext.web.handler.sockjs.SockJSHandler;
import io.vertx.ext.web.handler.sockjs.SockJSHandlerOptions;
import io.vertx.ext.web.handler.sockjs.Transport;
import org.apache.commons.io.FileUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.GlobalConstants.BootstrapType.COLLECTOR;
import static com.mindarray.agent.AgentConstants.AGENT;
import static com.mindarray.agent.AgentConstants.AGENT_TYPE;
import static com.mindarray.api.APIConstants.CONTENT_TYPE_TEXT_HTML;
import static com.mindarray.api.Agent.*;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_VERSION;
import static com.mindarray.db.ConfigDBConstants.BACKUP_END_TIME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.util.WorkerUtil.EXIT_CODE_TIMED_OUT;

/**
 * The MotadataAppManager class is responsible for managing the lifecycle of the Motadata application.
 * <p>
 * This class serves as the central control system for the Motadata application, handling operations such as:
 * <ul>
 *   <li>Starting, stopping, and restarting application components</li>
 *   <li>Managing application upgrades and patches</li>
 *   <li>Handling backup and restore operations</li>
 *   <li>Coordinating communication between distributed components</li>
 *   <li>Managing HTTP servers and event bus handlers</li>
 *   <li>Monitoring system health and status</li>
 * </ul>
 * <p>
 * The MotadataAppManager extends Vert.x's AbstractVerticle to leverage the event-driven,
 * non-blocking programming model for handling concurrent operations efficiently. It interacts
 * with various system components through the event bus and maintains the overall state of the
 * application.
 * <p>
 * This class implements a robust error handling mechanism and ensures proper coordination
 * between different components during critical operations like upgrades and restarts.
 * <p>
 * <h2>Architecture and Design</h2>
 * <p>
 * The MotadataAppManager follows a reactive, event-driven architecture:
 * <ul>
 *   <li><strong>Event Processing:</strong> The class processes events received through the Vert.x event bus,
 *       dispatching them to appropriate handler methods based on event type.</li>
 *   <li><strong>Asynchronous Operations:</strong> All operations are performed asynchronously using Vert.x's
 *       Future/Promise pattern to ensure non-blocking behavior.</li>
 *   <li><strong>Component Management:</strong> The class manages the lifecycle of child processes and components,
 *       ensuring they are started, stopped, and monitored appropriately.</li>
 *   <li><strong>HTTP Server:</strong> The class sets up and manages an HTTP server for handling web-based
 *       operations and API endpoints.</li>
 * </ul>
 * <p>
 * <h2>Event Handling</h2>
 * <p>
 * The MotadataAppManager handles various types of events:
 * <ul>
 *   <li><strong>Lifecycle Events:</strong> Start, stop, restart events for application components</li>
 *   <li><strong>State Change Events:</strong> Enable, disable events for components</li>
 *   <li><strong>Upgrade Events:</strong> Events for upgrading application components or the manager itself</li>
 *   <li><strong>Backup/Restore Events:</strong> Events for backing up and restoring system data</li>
 *   <li><strong>Heartbeat Events:</strong> Events for monitoring system health</li>
 *   <li><strong>Switchover Events:</strong> Events for high availability operations</li>
 * </ul>
 * <p>
 * <h2>File Operations</h2>
 * <p>
 * The class performs various file operations for backup, restore, and upgrade:
 * <ul>
 *   <li>Copying and transferring files between directories</li>
 *   <li>Extracting and processing ZIP archives</li>
 *   <li>Reading and writing configuration files</li>
 *   <li>Managing system files and directories</li>
 * </ul>
 * <p>
 * <h2>Process Management</h2>
 * <p>
 * The MotadataAppManager manages external processes:
 * <ul>
 *   <li>Starting and stopping child processes</li>
 *   <li>Monitoring process health and status</li>
 *   <li>Handling process output and errors</li>
 *   <li>Managing process IDs and registration</li>
 * </ul>
 * <p>
 * <h2>Error Handling</h2>
 * <p>
 * The class implements comprehensive error handling:
 * <ul>
 *   <li>All operations return Futures that can be composed and chained</li>
 *   <li>Exceptions are caught, logged, and propagated appropriately</li>
 *   <li>Error messages are standardized and include error codes</li>
 *   <li>Recovery mechanisms are implemented for critical operations</li>
 * </ul>
 * <p>
 * <h2>Usage</h2>
 * <p>
 * The MotadataAppManager is typically used by:
 * <ul>
 *   <li>System bootstrap processes to initialize the application</li>
 *   <li>Administrative interfaces to control application lifecycle</li>
 *   <li>Monitoring systems to track application health</li>
 *   <li>Upgrade and maintenance processes to perform system updates</li>
 * </ul>
 *
 * @see com.mindarray.eventbus.EventEngine
 * @see com.mindarray.ha.HAManager
 * @see io.vertx.core.AbstractVerticle
 */
public class MotadataAppManager extends AbstractVerticle
{
    /**
     * Constant for the patch artifact file name used during upgrades
     */
    public static final String PATCH_ARTIFACT_FILE = "patch.artifact.file";

    /**
     * Constant for the heartbeat state, stored only in cache
     * Possible values: reachable, not-reachable, not-running
     */
    public static final String HEARTBEAT_STATE = "heartbeat.state";

    /** Logger for this class */
    private static final Logger LOGGER = new Logger(MotadataAppManager.class, GlobalConstants.MOTADATA_APP_MANAGER, "Motadata App Manager");

    /** Format string for artifacts directory */
    private static final String ARTIFACTS = "%s-artifacts";

    /** Name of the backup folder */
    private static final String BACKUP_FOLDER = "backup";

    /** URL format for artifact downloads */
    private static final String ARTIFACT_URL = "/download/?id=%s";

    /** Buffer size for file operations (4KB) */
    private static final int BUFFER_SIZE = 4096;

    /**
     * Set of mandatory system files that need to be backed up before upgrading
     * These files are critical for system operation and must be preserved
     */
    private static final Set<String> SYSTEM_FILES = Set.of("cache", "cache-files", "CONFIG-DB-BACKUP-LOCAL", "config-db-backups", "config-management", "customscripts", "datastore", "datastore-backups", "datastore-events", "datastore-logs", "downloads", "err.log", "events", "jdk", "jobs", "keys", "license.lic", "logs", "log-utility", "motadata-manager", "motadata-upgrader", "NetFlow", "nfacctd.conf", "plugin-contexts", "public-key.pem", "resources", "server-cert.pem", "server_clean.sh", "server-key.pem", "sfacctd.conf", "shutdown.sh", "stopwords.dat", "tag.rule", "uploads", "backup");

    /** Constant for enable state */
    private static final String ENABLE = "ENABLE";

    /** Constant for disable state */
    private static final String DISABLE = "DISABLE";

    /** File name that triggers manager upgrade */
    private static final String MANAGER_UPGRADE_FILE = "upgrade.me";

    /** Script name for manager upgrader on Linux systems */
    private static final String LINUX_MANAGER_UPGRADER_SCRIPT = "manager-upgrader.sh";

    /** Script name for manager upgrader on Windows systems */
    private static final String WINDOWS_MANAGER_UPGRADER_SCRIPT = "manager-upgrader.bat";

    /** Maximum allowed size for version files (1GB) */
    private static final long MAX_VERSION_FILE_SIZE_BYTES = 1073741824;

    /** Set to track child processes managed by this class */
    private static final Set<String> CHILD_PROCESSES = new LinkedHashSet<>();

    /** HTTP server instance for handling web requests */
    private static HttpServer httpServer = null;

    /** Event engine for managing event bus communications */
    private EventEngine eventEngine;

    /** Topic to which responses should be sent */
    private String replyTopic;

    /**
     * Initializes the MotadataAppManager verticle.
     * <p>
     * This method sets up the manager by:
     * <ul>
     *   <li>Initializing the event engine for event bus communication</li>
     *   <li>Setting up event handlers for various system operations</li>
     *   <li>Starting the HTTP server for web-based operations</li>
     *   <li>Registering event consumers for different types of events</li>
     * </ul>
     * <p>
     * The method handles events related to application lifecycle management,
     * including start, stop, restart, upgrade, backup, and restore operations.
     *
     * @param startPromise the promise to complete when initialization is done
     * @throws Exception if an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> startPromise) throws Exception
    {
        // Set up the reply topic for event responses
        replyTopic = config().getString(EVENT_TYPE) + EVENT_REPLY;

        // Register a consumer for database backup events
        vertx.eventBus().<JsonObject>localConsumer(EVENT_DATABASE_BACKUP, message ->
        {
            var event = message.body();

            try
            {
                // Log the backup request details
                LOGGER.info(String.format("%s backup request received for target : %s protocol : %s",
                        event.getString(BackupProfile.BACKUP_PROFILE_TYPE),
                        event.getString(AIOpsObject.OBJECT_TARGET),
                        event.getString(StorageProfile.STORAGE_PROFILE_PROTOCOL)));

                // Execute the backup operation in a blocking context to avoid blocking the event loop
                vertx.<JsonObject>executeBlocking(future ->
                {
                    try
                    {
                        // Initialize event with default status and source path
                        // Set different source paths based on backup type (CONFIG_DB or other)
                        event.put(STATUS, STATUS_FAIL)
                                .put(GlobalConstants.SRC_FILE_PATH,
                                        event.getString(BackupProfile.BACKUP_PROFILE_TYPE).equalsIgnoreCase(BackupProfile.BackupProfileType.CONFIG_DB.getName())
                                                ? event.getString(SRC_FILE_PATH)
                                                : ConfigDBConstants.DATASTORE_BACKUP_PATH);

                        // Set timeout based on backup type (shorter for CONFIG_DB, longer for others)
                        event.put(TIMEOUT,
                                event.getString(BackupProfile.BACKUP_PROFILE_TYPE).equalsIgnoreCase(BackupProfile.BackupProfileType.CONFIG_DB.getName())
                                        ? 600  // 10 minutes for config DB
                                        : 3600); // 1 hour for other types

                        // Handle remote storage protocols (non-LOCAL)
                        if (!event.getString(StorageProfile.STORAGE_PROFILE_PROTOCOL).equalsIgnoreCase(StorageProfile.StorageProtocol.LOCAL.getName()))
                        {
                            // Use worker to handle remote storage operations
                            var output = WorkerUtil.spawnWorker(event, CommonUtil.newEventId(), CommonUtil.newEventId(),
                                    PluginEngineConstants.PluginEngine.GO.getName(), true);

                            if (!output.isEmpty())
                            {
                                // Decode and merge the worker output with the event
                                event.mergeIn(new JsonObject(new String(Base64.getDecoder().decode(output.trim()))), true);
                            }
                            else
                            {
                                // Handle empty output as an error
                                event.put(ERRORS, new JsonArray().add(new JsonObject()
                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                        .put(MESSAGE, ErrorMessageConstants.INTERNAL_ERROR)));
                            }
                        }
                        else // Handle LOCAL storage protocol
                        {
                            var srcFile = new File(event.getString(SRC_FILE_PATH));

                            if (srcFile.exists())
                            {
                                if (srcFile.isFile()) // Handle single file backup
                                {
                                    // Extract the filename from the path
                                    var tokens = event.getString(SRC_FILE_PATH).split(PATH_SEPARATOR);
                                    event.put(DEST_FILE_NAME, tokens[tokens.length - 1]);

                                    // Copy the file to the destination
                                    if (CommonUtil.copy(event))
                                    {
                                        LOGGER.info(String.format("file %s transferred successfully", srcFile.getName()));
                                        event.put(STATUS, STATUS_SUCCEED);

                                        // Delete source file if requested
                                        if (event.containsKey(DELETE_SRC_FILE) && event.getString(DELETE_SRC_FILE).equalsIgnoreCase(YES))
                                        {
                                            FileUtils.deleteQuietly(srcFile);
                                        }
                                    }
                                    else
                                    {
                                        // Handle copy failure
                                        event.put(ERRORS, new JsonArray().add(new JsonObject()
                                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                .put(MESSAGE, event.containsKey(MESSAGE) ? event.getString(MESSAGE) : ErrorMessageConstants.INTERNAL_ERROR)));
                                    }
                                }
                                else if (srcFile.isDirectory()) // Handle directory backup
                                {
                                    // Instead of copying the directory, transfer files individually
                                    // This prevents the directory from being overridden each time
                                    var files = srcFile.listFiles();

                                    if (files != null)
                                    {
                                        // Track the number of files to transfer
                                        var srcFiles = files.length;
                                        LOGGER.info(String.format("%s files found to transfer", srcFiles));

                                        // Process each file in the directory
                                        for (var file : files)
                                        {
                                            // Update source path to the current file
                                            event.put(SRC_FILE_PATH, file.getAbsolutePath());

                                            // Extract filename from path
                                            var tokens = event.getString(SRC_FILE_PATH).split(PATH_SEPARATOR);
                                            event.put(DEST_FILE_NAME, tokens[tokens.length - 1]);

                                            // Copy the file
                                            if (CommonUtil.copy(event))
                                            {
                                                // Log successful transfer if trace is enabled
                                                if (CommonUtil.traceEnabled())
                                                {
                                                    LOGGER.trace(String.format("file %s transferred from %s directory ", file.getName(), srcFile.getName()));
                                                }

                                                // Delete source file if requested
                                                if (event.containsKey(DELETE_SRC_FILE) && event.getString(DELETE_SRC_FILE).equalsIgnoreCase(YES))
                                                {
                                                    FileUtils.deleteQuietly(file);
                                                }

                                                // Decrement counter of remaining files
                                                srcFiles--;
                                            }
                                        }

                                        // If all files were transferred successfully, mark operation as successful
                                        if (srcFiles == 0)
                                        {
                                            event.put(STATUS, STATUS_SUCCEED);
                                        }
                                        else
                                        {
                                            // Some files failed to transfer
                                            event.put(ERRORS, new JsonArray().add(new JsonObject()
                                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                    .put(MESSAGE, event.containsKey(MESSAGE) ? event.getString(MESSAGE) : ErrorMessageConstants.INTERNAL_ERROR)));
                                        }
                                    }
                                }
                            }
                            else
                            {
                                // Source file or directory doesn't exist
                                event.put(ERRORS, new JsonArray().add(new JsonObject()
                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                        .put(MESSAGE, event.containsKey(MESSAGE) ? event.getString(MESSAGE) : ErrorMessageConstants.INTERNAL_ERROR)));
                            }
                        }

                        // Log the backup transfer status
                        LOGGER.info(String.format("%s backup transfer status : %s ",
                                event.getString(BackupProfile.BACKUP_PROFILE_TYPE),
                                event.getString(STATUS)));
                    }
                    catch (Exception exception)
                    {
                        // Log and handle any exceptions during backup
                        LOGGER.error(exception);
                        event.put(ERRORS, new JsonArray().add(new JsonObject()
                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                .put(MESSAGE, exception.getMessage())));
                    }

                    // Complete the future with the event and add the backup end time
                    future.complete(event.put(BACKUP_END_TIME, DateTimeUtil.currentMilliSeconds()));

                }, result -> vertx.eventBus().send(EVENT_MOTADATA_MANAGER, event));
            }
            catch (Exception exception)
            {
                // Handle exceptions in the outer try block
                LOGGER.error(exception);
                vertx.eventBus().send(EVENT_REMOTE, event.put(MESSAGE, exception.getMessage()));
            }
        });

        // Initialize and start the event engine for processing system events
        eventEngine = new EventEngine()
                .setEventType(config().getString(EVENT_TYPE))
                .setLogger(LOGGER)
                .setBlockingEvent(true)  // Process events in a blocking context
                .setEventQueueSize(1)    // Process one event at a time
                .setEventHandler(event ->
                {
                    // Extract bootstrap type and event type from the event
                    var bootstrapType = event.getString(SYSTEM_BOOTSTRAP_TYPE);
                    var eventType = event.getString(EVENT_TYPE);

                    // Log the event details
                    LOGGER.info(String.format("event %s for motadata-%s process", event.getString(EVENT_TYPE), bootstrapType));

                    // Log the full event if trace is enabled
                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("event : %s", event.encodePrettily()));
                    }

                    // Add additional properties based on bootstrap type
                    if (bootstrapType.equalsIgnoreCase(BootstrapType.AGENT.name()))
                    {
                        // For agent bootstrap type, add agent-specific properties
                        event.put(AGENT_TYPE, EVENT_AGENT).put(AGENT_UUID, Bootstrap.getRegistrationId());
                    }
                    else if (bootstrapType.equalsIgnoreCase(COLLECTOR.name())
                            || bootstrapType.equalsIgnoreCase(BootstrapType.EVENT_COLLECTOR.name())
                            || bootstrapType.equalsIgnoreCase(BootstrapType.EVENT_PROCESSOR.name())
                            || bootstrapType.equalsIgnoreCase(BootstrapType.FLOW_COLLECTOR.name())
                            || bootstrapType.equalsIgnoreCase(BootstrapType.DATASTORE.name()))
                    {
                        // For collector and processor bootstrap types, add remote processor properties
                        event.put(REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId());
                    }

                    // Route the event to the appropriate handler method based on event type
                    var promise = switch (eventType)
                    {
                        // Lifecycle events
                        case EVENT_AGENT_START, EVENT_REMOTE_PROCESSOR_START -> start(event, bootstrapType);
                        case EVENT_AGENT_STOP, EVENT_REMOTE_PROCESSOR_STOP -> stop(bootstrapType);
                        case EVENT_AGENT_RESTART, EVENT_REMOTE_PROCESSOR_RESTART -> restart(event, bootstrapType);

                        // State change events
                        case EVENT_AGENT_DISABLE -> changeState(event, DISABLE);
                        case EVENT_AGENT_ENABLE -> changeState(event, ENABLE);

                        // High availability events
                        case EVENT_SWITCH_OVER -> switchOver(event);

                        // Monitoring events
                        case EVENT_MOTADATA_MANAGER_HEARTBEAT -> sendAcknowledgement(event);

                        // Deletion events
                        case EVENT_REMOTE_PROCESSOR_DELETE, EVENT_AGENT_DELETE -> deactivate(bootstrapType);

                        // Upgrade events
                        case EVENT_AGENT_UPGRADE, EVENT_REMOTE_PROCESSOR_UPGRADE ->
                                upgrade(event, bootstrapType, new JsonObject(), MotadataConfigUtil.getHTTPServerPort(BootstrapType.APP.name()));

                        // Backup/restore events
                        case EVENT_DATABASE_BACKUP -> backup(event);
                        case EVENT_DATABASE_RESTORE -> restore(event);

                        // System upgrade events
                        case EVENT_MASTER_UPGRADE -> upgrade(event);
                        case EVENT_MANAGER_UPGRADE -> upgradeManager(event);

                        // Handle unknown event types
                        default -> Future.failedFuture("Invalid event type");
                    };

                    // Handle the result of the event processing
                    promise.onComplete(result ->
                    {
                        if (result.succeeded())
                        {
                            // Log success and update event status
                            LOGGER.info(String.format("event type : %s is completed successfully", eventType));
                            event.put(STATUS, STATUS_SUCCEED);
                        }
                        else
                        {
                            // Log failure, update event status and add error message
                            LOGGER.info(String.format("event type : %s is failed. Reason : %s ", eventType, result.cause().getMessage()));
                            event.put(STATUS, STATUS_FAIL);
                            event.put(MESSAGE, result.cause().getMessage());
                        }

                        // Send event to appropriate destinations
                        // Skip for backup and upgrade events as they handle their own event sending
                        if (!eventType.equalsIgnoreCase(EVENT_DATABASE_BACKUP) && !eventType.endsWith("upgrade"))
                        {
                            send(event, false);
                        }

                        // Send event to reply topic
                        vertx.eventBus().send(replyTopic, event);
                    });

                }).start(vertx, startPromise);

        // Determine which child processes need to be started based on bootstrap type
        calculateChildProcesses(MotadataConfigUtil.getSystemBootstrapType());

        // Log and start the child processes
        LOGGER.info(String.format("starting %s processes", CHILD_PROCESSES));
        start(CHILD_PROCESSES.toArray(), Promise.promise(), new AtomicInteger(0));

        // Deploy the HA Manager if running in secondary or passive mode
        if (MotadataConfigUtil.getSystemBootstrapType().equalsIgnoreCase(BootstrapType.APP.name())
                && (MotadataConfigUtil.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name())
                || (MotadataConfigUtil.getHAMode() != null && MotadataConfigUtil.getHAMode().equalsIgnoreCase(HAMode.PASSIVE.name()))))
        {
            // Switch over engine will only be deployed in secondary or in passive mode for HA
            Bootstrap.startEngine(new HAManager(), HAManager.class.getSimpleName(), null);
        }
    }

    /**
     * Switches over configuration settings and restarts relevant verticles.
     * <p>
     * This method performs a configuration change and verticle restart sequence:
     * <ul>
     *   <li>Updates the configuration file with new parameters from the event</li>
     *   <li>Reloads the configuration settings</li>
     *   <li>Undeploys the RemoteEventSubscriber verticle</li>
     *   <li>Undeploys the RemoteEventForwarder verticle</li>
     *   <li>Redeploys both verticles with the new configuration</li>
     * </ul>
     * <p>
     * This method is primarily used during high availability operations or when
     * system configuration changes require component restarts.
     *
     * @param event the JSON object containing configuration changes to apply
     * @return a Future that completes when the switchover operation is finished
     */
    private Future<Void> switchOver(JsonObject event)
    {
        // First change configuration settings, then restart verticles to apply changes
        var promise = Promise.<Void>promise();

        try
        {
            // Locate the configuration file
            var file = new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + MOTADATA_CONFIG_FILE);

            // Read the current configuration
            JsonObject configs = new JsonObject(FileUtils.readFileToString(file, StandardCharsets.UTF_8));

            // Merge the new configuration changes from the event
            // This preserves existing settings while updating only those specified in the event
            configs.mergeIn(event.getJsonObject(EVENT_CONFIG_CHANGE, new JsonObject()));

            // Write the updated configuration back to the file
            // Using encodePrettily() for human-readable JSON formatting
            vertx.fileSystem().writeFileBlocking(file.getPath(), Buffer.buffer(configs.encodePrettily().getBytes()));

            // Reload the configuration settings into memory
            loadConfigs(MOTADATA_CONFIG_FILE, false);

            // Log the configuration changes
            LOGGER.info(String.format("file %s updated with new parameters %s",
                    file.getName(), event.getJsonObject(EVENT_CONFIG_CHANGE)));
            LOGGER.info(String.format("new updated motadata json %s ",
                    MotadataConfigUtil.getConfigs().encode()));

            // Begin the verticle restart sequence
            // Step 1: Undeploy the RemoteEventSubscriber verticle
            Bootstrap.undeployVerticle(RemoteEventSubscriber.class.getSimpleName()).onComplete(result ->
            {
                if (result.succeeded())
                {
                    // Step 2: Undeploy the RemoteEventForwarder verticle
                    // The verticle name includes the port number for uniqueness
                    Bootstrap.undeployVerticle(
                            RemoteEventForwarder.class.getSimpleName() + " " +
                                    MotadataConfigUtil.getMotadataManagerEventPublisherPort()
                    ).onComplete(response ->
                    {
                        if (response.succeeded())
                        {
                            // Step 3: Redeploy the RemoteEventForwarder with the new configuration
                            Bootstrap.startEngine(
                                    new RemoteEventForwarder(MotadataConfigUtil.getMotadataManagerEventPublisherPort()),
                                    RemoteEventForwarder.class.getSimpleName() + " " +
                                            MotadataConfigUtil.getMotadataManagerEventPublisherPort(),
                                    null
                            ).onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    // Step 4: Redeploy the RemoteEventSubscriber with the new configuration
                                    Bootstrap.startEngine(
                                            new RemoteEventSubscriber(MotadataConfigUtil.getMotadataManagerEventSubscriberPort()),
                                            RemoteEventSubscriber.class.getSimpleName(),
                                            null
                                    ).onComplete(asyncResponse ->
                                    {
                                        if (asyncResponse.succeeded())
                                        {
                                            // All verticles redeployed successfully
                                            promise.complete();
                                            LOGGER.info(String.format("%s %s restart successfully",
                                                    RemoteEventSubscriber.class.getSimpleName(),
                                                    RemoteEventForwarder.class.getSimpleName()));
                                        }
                                        else
                                        {
                                            // RemoteEventSubscriber redeployment failed
                                            LOGGER.warn(asyncResponse.cause());
                                            // Note: We don't fail the promise here, which means the operation
                                            // is considered partially successful even if this step fails
                                        }
                                    });
                                }
                                else
                                {
                                    // RemoteEventForwarder redeployment failed
                                    LOGGER.warn(asyncResult.cause());
                                    // Note: We don't fail the promise here, which means the operation
                                    // is considered partially successful even if this step fails
                                }
                            });
                        }
                        else
                        {
                            // RemoteEventForwarder undeployment failed
                            LOGGER.error(response.cause());
                            // Note: We don't fail the promise here, which means the operation
                            // is considered partially successful even if this step fails
                        }
                    });
                }
                else
                {
                    // RemoteEventSubscriber undeployment failed
                    LOGGER.error(result.cause());
                    // Note: We don't fail the promise here, which means the operation
                    // is considered partially successful even if this step fails
                }
            });

        }
        catch (Exception exception)
        {
            // Log any exceptions during the configuration update
            LOGGER.error(exception);
        }

        return promise.future();
    }

    /**
     * Recursively stops a series of system components.
     * <p>
     * This method iterates through an array of component types and stops each one
     * in sequence. It uses recursion to process the array, incrementing the index
     * after each component is successfully stopped.
     * <p>
     * The method completes the provided promise when all components have been
     * processed, or if an error occurs during the stopping process.
     *
     * @param types an array of component types to stop
     * @param promise the promise to complete when all components are stopped
     * @param index the current index in the types array
     */
    private void stop(Object[] types, Promise<Void> promise, AtomicInteger index)
    {
        // stop all infra started by the manager
        try
        {
            if (index.get() >= types.length)
            {
                promise.complete();
            }
            else
            {
                stop(types[index.get()].toString()).onComplete(result ->
                {
                    index.set(index.incrementAndGet());

                    stop(types, promise, index);
                });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Recursively starts a series of system components.
     * <p>
     * This method iterates through an array of component types and starts each one
     * in sequence. It uses recursion to process the array, incrementing the index
     * after each component is successfully started.
     * <p>
     * The method completes the provided promise when all components have been
     * processed, or if an error occurs during the starting process.
     * <p>
     * This method is the counterpart to the {@link #stop(Object[], Promise, AtomicInteger)}
     * method and follows the same recursive pattern.
     *
     * @param types an array of component types to start
     * @param promise the promise to complete when all components are started
     * @param index the current index in the types array
     */
    private void start(Object[] types, Promise<Void> promise, AtomicInteger index)
    {
        // start all infra
        try
        {
            if (index.get() >= types.length)
            {
                promise.complete();
            }
            else
            {
                var bootstrapType = types[index.get()].toString();

                LOGGER.info(String.format("starting infra for %s bootstrap type ", bootstrapType));

                start(new JsonObject(), bootstrapType).onComplete(result ->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            var upgrade = EMPTY_VALUE;

                            var file = new File(CURRENT_DIR + PATH_SEPARATOR + MANAGER_UPGRADE_FILE);

                            if (file.exists())
                            {
                                upgrade = FileUtils.readFileToString(file, StandardCharsets.UTF_8);

                                LOGGER.info(String.format("manager upgrade status : %s", upgrade));

                                FileUtils.deleteQuietly(file);
                            }

                            if (bootstrapType.equalsIgnoreCase(BootstrapType.APP.name()) && CommonUtil.isNotNullOrEmpty(upgrade))
                            {
                                var tokens = upgrade.split(":"); // file will contain "zip file uuid : status of manager update"

                                // sometimes motadata not receives this event hence the remote entity managers are not getting the manager upgrade event
                                vertx.setTimer(TimeUnit.SECONDS.toMillis(30), timer ->
                                {
                                    if (tokens.length > 1 && CommonUtil.getString(tokens[1]).equalsIgnoreCase(STATUS_SUCCEED))
                                    {
                                        if (CommonUtil.traceEnabled())
                                        {
                                            LOGGER.trace(String.format("sending manager upgrade status : %s", tokens[1]));

                                        }

                                        send(new JsonObject().put(EVENT_TYPE, EVENT_MANAGER_UPGRADE).put(STATUS, STATUS_SUCCEED).put(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.APP.name()).put(PATCH_ARTIFACT_FILE, tokens[0]), false);
                                    }
                                    else
                                    {
                                        send(new JsonObject().put(EVENT_TYPE, EVENT_MANAGER_UPGRADE).put(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.APP.name()).put(STATUS, STATUS_FAIL), false);
                                    }
                                });
                            }

                            index.set(index.incrementAndGet());

                            start(types, promise, index);
                        }
                        else
                        {
                            LOGGER.info(String.format("failed to start motadata-%s process, reason: %s", bootstrapType, result.cause().getMessage()));

                            promise.fail(String.format("failed to start motadata-%s process, reason: %s", bootstrapType, result.cause().getMessage()));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Calculates the number of child processes for a specific bootstrap type.
     * <p>
     * This method determines the appropriate child processes to spawn
     * based on the bootstrap type provided. Different bootstrap types may require
     * different child processes for optimal performance.
     * <p>
     * For example, when the bootstrap type is APP and the installation type is 0,
     * a DATASTORE child process is also added.
     *
     * @param bootstrapType the type of bootstrap for which to calculate child processes
     */
    private void calculateChildProcesses(String bootstrapType)
    {
        CHILD_PROCESSES.add(bootstrapType);

        if (bootstrapType.equalsIgnoreCase(BootstrapType.APP.name()) && MotadataConfigUtil.getInstallationType() == 0)
        {
            CHILD_PROCESSES.add(BootstrapType.DATASTORE.name());
        }

        CHILD_PROCESSES.add(BootstrapType.AGENT.name());
    }

    /**
     * Starts a specific system component based on its bootstrap type.
     * <p>
     * This method handles the startup process for different types of system components:
     * <ul>
     *   <li>Resets the registration ID for the component</li>
     *   <li>Loads appropriate configuration files</li>
     *   <li>Checks if the component is enabled and can be started</li>
     *   <li>Handles already running processes by restarting them</li>
     *   <li>Builds and executes the appropriate start command</li>
     *   <li>Sets up registration IDs and process monitoring</li>
     * </ul>
     * <p>
     * Special handling is provided for agent components, which require checking
     * their state and deletion status before starting.
     *
     * @param event the JSON object containing event data related to the start operation
     * @param bootstrapType the type of bootstrap component to start
     * @return a Future that completes when the start operation is finished
     */
    private Future<Void> start(JsonObject event, String bootstrapType)
    {
        var promise = Promise.<Void>promise();

        try
        {
            // Reset any existing registration ID for this bootstrap type
            resetRegistrationId(bootstrapType);

            // Special handling for agent bootstrap type
            if (bootstrapType.equalsIgnoreCase(BootstrapType.AGENT.name()))
            {
                // Load agent-specific configuration file
                loadConfigs(AGENT_FILE, true);

                var agentConfigs = AgentConfigUtil.getAgentConfigs();

                // Check if agent is active (not deleted)
                if (!agentConfigs.isEmpty() && agentConfigs.containsKey(AGENT_DELETION_STATUS) && agentConfigs.getString(AGENT_DELETION_STATUS).equalsIgnoreCase(NO))
                {
                    // Check if agent is enabled
                    if (agentConfigs.containsKey(AGENT_STATE) && agentConfigs.getString(AGENT_STATE).equalsIgnoreCase(DISABLE))
                    {
                        // Cannot start disabled agents
                        promise.fail("failed to start agent, reason: the agent is disabled");
                    }
                }
                else
                {
                    // Cannot start deactivated agents
                    promise.fail("failed to start agent, reason: the agent is deactivated");
                }
            }

            // Only proceed if the promise hasn't been completed (failed) by agent checks
            if (!promise.future().isComplete())
            {
                // Check if a process for this bootstrap type is already running
                updateProcessId(bootstrapType, 1).onComplete(response ->
                {
                    if (response.succeeded())
                    {
                        // Process is already running, need to restart it instead of starting new
                        LOGGER.info(String.format("motadata-%s process is already running..... Hence restarting", bootstrapType));

                        // Restart the existing process
                        restart(event, bootstrapType).onComplete(result ->
                        {
                            if (result.succeeded())
                            {
                                promise.complete();
                            }
                            else
                            {
                                promise.fail(result.cause().getMessage());
                            }
                        });
                    }
                    else
                    {
                        // No existing process found, build command to start a new process
                        var commands = buildProcessStartCommand(bootstrapType);

                        // Execute the process start command
                        executeCommand(commands, false).onComplete(result ->
                        {
                            if (result.succeeded())
                            {
                                // Get the process ID of the newly started process
                                // Multiple attempts may be needed as process startup can take time
                                updateProcessId(bootstrapType, CommonUtil.getProcessDetectionAttempts(bootstrapType)).onComplete(asyncResult ->
                                {
                                    if (asyncResult.succeeded())
                                    {
                                        try
                                        {
                                            LOGGER.info(String.format("motadata-%s process started with pid %s having command %s", bootstrapType, asyncResult.result(), commands));

                                            // Record the start time
                                            event.put("time", System.currentTimeMillis());

                                            // Special handling for agent bootstrap type
                                            if (bootstrapType.equalsIgnoreCase(BootstrapType.AGENT.name()))
                                            {
                                                // Reload agent configs after starting the agent
                                                loadConfigs(AGENT_FILE, true);

                                                // Add agent configurations to the event for further processing
                                                event.put(Agent.AGENT_CONFIGS, AgentConfigUtil.getChildAgentConfigs());
                                            }

                                            // Get and verify the registration ID of the started process
                                            // This ensures the process is properly initialized and registered
                                            setRegistrationId(bootstrapType, CommonUtil.getProcessDetectionAttempts(bootstrapType)).onComplete(asyncResponse ->
                                            {
                                                if (asyncResponse.succeeded())
                                                {
                                                    LOGGER.info(String.format("motadata-%s UUID is %s", bootstrapType, asyncResponse.result()));

                                                    // Verify that the registration ID matches the expected value
                                                    if (asyncResponse.result().equalsIgnoreCase(Bootstrap.getRegistrationId()))
                                                    {
                                                        // Process successfully started and registered
                                                        promise.complete();
                                                    }
                                                    else
                                                    {
                                                        // Registration ID mismatch indicates initialization issues
                                                        promise.fail(String.format("failed to verify uuid %s for motadata-%s", asyncResponse.result(), bootstrapType));
                                                    }
                                                }
                                                else
                                                {
                                                    // Failed to get registration ID
                                                    promise.fail(String.format("failed to verify uuid for motadata-%s reason:%s", bootstrapType, asyncResponse.cause().getMessage()));
                                                }
                                            });
                                        }
                                        catch (Exception exception)
                                        {
                                            promise.fail(exception.getMessage());

                                            LOGGER.error(exception);
                                        }
                                    }
                                    else
                                    {
                                        // Failed to get process ID
                                        promise.fail(String.format("failed to start motadata-%s process", bootstrapType));
                                    }
                                });
                            }
                            else
                            {
                                // Command execution failed
                                promise.fail(String.format("failed to start motadata-%s process", bootstrapType));
                            }
                        });
                    }
                });
            }
        }
        catch (Exception exception)
        {
            promise.fail(exception.getMessage());

            LOGGER.error(exception);
        }

        return promise.future();
    }

    /**
     * Stops a specific system component based on its bootstrap type.
     * <p>
     * This method handles the shutdown process for different types of system components:
     * <ul>
     *   <li>Identifies the process ID of the running component</li>
     *   <li>Builds and executes the appropriate stop command</li>
     *   <li>Verifies that the process has been terminated</li>
     *   <li>Handles cases where the process is already stopped</li>
     * </ul>
     * <p>
     * The method includes a polling mechanism to verify that the process has
     * completely terminated before completing the operation.
     *
     * @param bootstrapType the type of bootstrap component to stop
     * @return a Future that completes when the stop operation is finished
     */
    private Future<Void> stop(String bootstrapType)
    {
        var promise = Promise.<Void>promise();

        try
        {
            updateProcessId(bootstrapType, 1).onComplete(pid ->
            {
                if (pid.succeeded())
                {
                    var commands = buildProcessStopCommand(bootstrapType, false);

                    LOGGER.info(String.format("killing motadata-%s process having command %s", bootstrapType, commands));

                    executeCommand(commands, true).onComplete(result ->
                    {
                        if (result.succeeded() || result.cause().getMessage().toLowerCase().contains("no such process")
                                || result.cause().getMessage().toLowerCase().contains("not found")
                                || result.cause().getMessage().toLowerCase().contains("not exist"))
                        {
                            LOGGER.info(String.format("killed motadata-%s process having name %s", bootstrapType, result.result()));

                            var attempts = new AtomicInteger();

                            vertx.setPeriodic(0, 5000, timer ->
                            {
                                try
                                {
                                    if (attempts.getAndIncrement() < CommonUtil.getProcessDetectionAttempts(bootstrapType))
                                    {
                                        executeCommand(buildProcessDetectionCommand(bootstrapType), true).onComplete(asyncResult ->
                                        {
                                            if (asyncResult.succeeded() && asyncResult.result().isEmpty())
                                            {
                                                if (!promise.future().isComplete())
                                                {
                                                    vertx.cancelTimer(timer);

                                                    promise.complete();
                                                }
                                            }
                                            else if (bootstrapType.equalsIgnoreCase(BootstrapType.AGENT.name()) && attempts.get() > 6) // if agent is not stopped in more than 30 seconds then need to stop it forcefully (MOTADATA-1305)
                                            {
                                                LOGGER.warn("killing agent forcefully!");

                                                executeCommand(buildProcessStopCommand(bootstrapType, true), false);
                                            }
                                        });
                                    }
                                    else
                                    {
                                        vertx.cancelTimer(timer);

                                        promise.fail(String.format("unable to stop motadata-%s process", bootstrapType));
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            });
                        }
                        else
                        {
                            promise.fail(String.format("error occurred while stopping process with output : %s and reason : %s", result.result(), result.cause().getMessage()));
                        }
                    });
                }
                else
                {
                    promise.fail(String.format("motadata-%s process is not running", bootstrapType));
                }

            });
        }
        catch (Exception exception)
        {
            promise.fail(exception.getMessage());

            LOGGER.error(exception);
        }

        return promise.future();
    }

    private Future<Void> restart(JsonObject event, String bootstrapType)
    {
        var promise = Promise.<Void>promise();

        try
        {
            LOGGER.info(String.format("restarting %s", bootstrapType));

            stop(bootstrapType).onComplete(result ->
            {
                if (result.succeeded() || result.cause().getMessage().contains("not running"))
                {
                    start(event, bootstrapType).onComplete(asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            promise.complete();
                        }
                        else
                        {
                            promise.fail(asyncResult.cause().getMessage());
                        }
                    });
                }
                else
                {
                    promise.fail(result.cause().getMessage());
                }
            });
        }
        catch (Exception exception)
        {
            promise.fail(exception.getMessage());

            LOGGER.error(exception);
        }

        return promise.future();
    }

    private Future<Void> upgrade(JsonObject event, String bootstrapType, JsonObject response, int port)
    {
        var promise = Promise.<Void>promise();

        var publish = new AtomicBoolean(); // used to publish events to UI in case of master upgrade.

        try
        {
            var fileName = event.getString(PATCH_ARTIFACT_FILE, EMPTY_VALUE);

            if (bootstrapType.equalsIgnoreCase(BootstrapType.AGENT.name())
                    || bootstrapType.equalsIgnoreCase(COLLECTOR.name())
                    || bootstrapType.equalsIgnoreCase(BootstrapType.EVENT_COLLECTOR.name())
                    || bootstrapType.equalsIgnoreCase(BootstrapType.FLOW_COLLECTOR.name())
                    || bootstrapType.equalsIgnoreCase(BootstrapType.EVENT_PROCESSOR.name())
                    || bootstrapType.equalsIgnoreCase(BootstrapType.DATASTORE.name()))
            {
                response.mergeIn(event);
            }
            else if (bootstrapType.equalsIgnoreCase(BootstrapType.APP.name()))
            {
                response.put(EVENT_TYPE, EVENT_MASTER_UPGRADE);

                publish.set(true);
            }

            if (!fileName.isEmpty())
            {
                LOGGER.info(String.format("upgrading %s started .... with file : %s ", bootstrapType, fileName));

                send(response.put(MESSAGE, "Stopping Motadata service ....").put(STATUS, STATUS_SUCCEED).put(PROGRESS, 0.0), publish.get());

                var asyncPromise = Promise.<Void>promise();

                stop(CHILD_PROCESSES.toArray(), asyncPromise, new AtomicInteger(0));

                asyncPromise.future().onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        send(response.put(MESSAGE, "Taking backup ...").put(PROGRESS, 10.0), publish.get());

                        // taking backup of backup files from "current" directory to "backup/{bootstarpType}-artifacts" directory
                        backup(bootstrapType).onComplete(asyncResponse ->
                        {
                            if (asyncResponse.succeeded())
                            {
                                send(response.put(MESSAGE, "Fetching files ...").put(PROGRESS, 20.0), publish.get());

                                // downloading zip file in "downloads" directory with name "{bootstrapType}-artifacts-{timestamp}"
                                downloadFiles(bootstrapType, fileName).onComplete(asyncResult ->
                                {
                                    if (asyncResult.succeeded())
                                    {
                                        LOGGER.info(String.format("files fetched to %s ...", asyncResult.result()));

                                        var zipFile = asyncResult.result();

                                        var destination = CURRENT_DIR + PATH_SEPARATOR + DOWNLOADS + PATH_SEPARATOR + String.format(ARTIFACTS, bootstrapType.toLowerCase());

                                        FileUtils.deleteQuietly(new File(destination)); //deleting old "downloads/{bootstrapType}-artifacts" directory

                                        new File(destination).mkdirs();

                                        try (var fileInputStream = new BufferedInputStream(new FileInputStream(zipFile));
                                             var zipInputStream = new ZipInputStream(new BufferedInputStream(fileInputStream)))
                                        {
                                            var artifact = new ZipFile(zipFile);

                                            var progress = CommonUtil.getFloat((80 - CommonUtil.getFloat(response.getValue(PROGRESS))) / (float) artifact.size());

                                            artifact.close();

                                            ZipEntry entry;

                                            while ((entry = zipInputStream.getNextEntry()) != null)
                                            {
                                                send(response.put(MESSAGE, "Upgrade in progress ...").put(PROGRESS, CommonUtil.getFloat(response.getValue(PROGRESS)) + progress), publish.get());

                                                var entryName = entry.getName();

                                                if (!entryName.isEmpty())
                                                {
                                                    if (entry.isDirectory())
                                                    {
                                                        if (!new File(destination, entryName).mkdir())
                                                        {
                                                            throw new RuntimeException("failed to create directory");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        var length = 0;

                                                        var bytes = new byte[BUFFER_SIZE];

                                                        BufferedOutputStream outputStream = null;

                                                        try
                                                        {
                                                            var file = new File(destination, entryName);

                                                            var fileOutputStream = new FileOutputStream(file);

                                                            outputStream = new BufferedOutputStream(fileOutputStream, BUFFER_SIZE);

                                                            while ((length = zipInputStream.read(bytes, 0, BUFFER_SIZE)) != NOT_AVAILABLE)
                                                            {
                                                                outputStream.write(bytes, 0, length);
                                                            }

                                                            outputStream.flush();

                                                            if (!PATH_SEPARATOR.equalsIgnoreCase("\\"))
                                                            {
                                                                // change permission to 755
                                                                file.setExecutable(true, false);

                                                                file.setReadable(true, true);

                                                                file.setWritable(true, true);
                                                            }
                                                        }
                                                        finally
                                                        {
                                                            if (outputStream != null)
                                                            {
                                                                outputStream.close();
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            if (!bootstrapType.equalsIgnoreCase(BootstrapType.APP.name()))
                                            {
                                                FileUtils.deleteQuietly(new File(zipFile));
                                            }

                                            // checking and reading content from "os.version" file
                                            var version = new File(CURRENT_DIR + PATH_SEPARATOR + "os.version").exists() ? "-" + FileUtils.readFileToString(new File(CURRENT_DIR + PATH_SEPARATOR + "os.version"), StandardCharsets.UTF_8).trim() : EMPTY_VALUE;

                                            var sourceDirectory = PATH_SEPARATOR.equalsIgnoreCase("\\") ? "windows" + version : "linux" + version;

                                            // transferring everything from "downloads/{bootstrapType}-artifacts/{os-version}" directory to "current" directory
                                            // AND transferring "VERSION" file from "downloads/{bootstrapType}-artifacts" directory to "current" directory
                                            // AND transferring anything present in the "downloads/{bootstrapType}-artifacts/parent" directory to parent directory
                                            // AND merging the content of json files present in the "downloads/{bootstrapType}-artifacts/config" directory to config/{file}.json
                                            transferFiles(destination + PATH_SEPARATOR + sourceDirectory, CURRENT_DIR, event, bootstrapType, new String[]{}).
                                                    compose(handler -> transferFiles(destination, CURRENT_DIR, event, bootstrapType, new String[]{VERSION_FILE}))
                                                    .compose(handler ->
                                                            new File(destination + PATH_SEPARATOR + "parent").exists() ? transferFiles(destination + PATH_SEPARATOR + "parent", new File(CURRENT_DIR).getParent(), event, bootstrapType, new String[]{}) : Future.succeededFuture())
                                                    .compose(handler ->
                                                            new File(destination + PATH_SEPARATOR + "config").exists() ? updateConfigFiles(destination + PATH_SEPARATOR + "config") : Future.succeededFuture())
                                                    .onComplete(futureResult ->
                                                    {
                                                        if (futureResult.succeeded())
                                                        {
                                                            // this will contain the process detection attempts keys only as in upgrade, process detection attempts always different and also important
                                                            var file = new File(destination + PATH_SEPARATOR + "manager.json");

                                                            if (file.exists())
                                                            {
                                                                try
                                                                {
                                                                    LOGGER.info("manager.json exists!");

                                                                    MotadataConfigUtil.loadConfigs(MotadataConfigUtil.getConfigs().mergeIn(new JsonObject(vertx.fileSystem().readFileBlocking(file.getAbsolutePath()))));
                                                                }
                                                                catch (Exception exception)
                                                                {
                                                                    LOGGER.error(exception);
                                                                }
                                                            }

                                                            send(response.put(MESSAGE, "Starting Motadata service ...").put(PROGRESS, 90.0), publish.get());

                                                            var asyncFuture = Promise.<Void>promise();

                                                            start(CHILD_PROCESSES.toArray(), asyncFuture, new AtomicInteger(0));

                                                            asyncFuture.future().onComplete(future ->
                                                            {
                                                                if (future.succeeded())
                                                                {
                                                                    send(response.put(MESSAGE, "Motadata service started successfully! ....").put(PROGRESS, 100.0).put(PORT, port), publish.get());

                                                                    promise.complete();
                                                                }
                                                                else
                                                                {
                                                                    restore(bootstrapType, publish.get(), response, port, future.cause().getMessage()).onComplete(restore -> promise.fail(future.cause().getMessage()));
                                                                }
                                                            });
                                                        }
                                                        else
                                                        {
                                                            restore(bootstrapType, publish.get(), response, port, futureResult.cause().getMessage()).onComplete(restore -> promise.fail(futureResult.cause()));
                                                        }
                                                    });

                                        }
                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);

                                            restore(bootstrapType, publish.get(), response, port, exception.getMessage()).onComplete(handler -> promise.fail(exception.getMessage()));
                                        }
                                    }
                                    else
                                    {
                                        LOGGER.info(String.format("failed to fetch files , reason : %s", asyncResult.cause().getMessage()));

                                        send(response.put(STATUS, STATUS_FAIL).put(MESSAGE, asyncResult.cause().getMessage()), publish.get());

                                        var asyncFuture = Promise.<Void>promise();

                                        start(CHILD_PROCESSES.toArray(), asyncFuture, new AtomicInteger(0));

                                        asyncFuture.future().onComplete(future ->
                                        {
                                            if (future.succeeded())
                                            {
                                                if (publish.get())
                                                {
                                                    send(response.put(PORT, port), true);
                                                }
                                            }
                                            else
                                            {
                                                LOGGER.error(future.cause());

                                                if (publish.get())
                                                {
                                                    send(response.put(MESSAGE, future.cause().getMessage()), true);
                                                }
                                            }

                                            promise.fail(asyncResult.cause().getMessage());
                                        });
                                    }
                                });
                            }
                            else
                            {
                                send(response.put(STATUS, STATUS_FAIL).put(MESSAGE, asyncResponse.cause().getMessage()), publish.get());

                                var asyncFuture = Promise.<Void>promise();

                                start(CHILD_PROCESSES.toArray(), asyncFuture, new AtomicInteger(0));

                                asyncFuture.future().onComplete(future ->
                                {
                                    if (future.succeeded())
                                    {
                                        if (publish.get())
                                        {
                                            send(response.put(PORT, port), true);
                                        }
                                    }
                                    else
                                    {
                                        LOGGER.error(future.cause());

                                        if (publish.get())
                                        {
                                            send(response.put(MESSAGE, future.cause().getMessage()), true);
                                        }
                                    }

                                    promise.fail(asyncResponse.cause().getMessage());
                                });
                            }
                        });
                    }
                    else
                    {
                        send(response.put(STATUS, STATUS_FAIL).put(MESSAGE, result.cause().getMessage()).put(PORT, port), publish.get());

                        promise.fail(result.cause().getMessage());
                    }
                });
            }
            else
            {
                send(response.put(STATUS, STATUS_FAIL).put(MESSAGE, "Invalid file name").put(PORT, port), publish.get());

                promise.fail("Invalid file name");
            }
        }
        catch (Exception exception)
        {
            promise.fail(exception.getMessage());

            LOGGER.error(exception);
        }

        return promise.future();
    }


    private Future<Void> restore(String bootstrapType, boolean publish, JsonObject response, int port, String message)
    {
        var promise = Promise.<Void>promise();

        send(response.put(STATUS, STATUS_FAIL).put(MESSAGE, message), publish);

        restore(bootstrapType).onComplete(result ->
        {
            if (result.succeeded())
            {
                LOGGER.info("Restore Succeeded");

                var asyncPromise = Promise.<Void>promise();

                start(CHILD_PROCESSES.toArray(), asyncPromise, new AtomicInteger(0));

                asyncPromise.future().onComplete(asyncResult ->
                {
                    if (asyncResult.succeeded())
                    {
                        if (publish)
                        {
                            send(response.put(PORT, port), true);
                        }

                        promise.complete();
                    }
                    else
                    {
                        LOGGER.error(asyncResult.cause());

                        if (publish)
                        {
                            send(response.put(MESSAGE, asyncResult.cause().getMessage()), true);
                        }

                        promise.fail(asyncResult.cause().getMessage());
                    }
                });
            }
            else
            {
                LOGGER.error(result.cause());

                if (publish)
                {
                    send(response.put(MESSAGE, result.cause().getMessage()), true);
                }

                promise.fail(result.cause().getMessage());
            }
        });

        return promise.future();
    }

    private Future<String> downloadFiles(String bootstrapType, String fileName)
    {
        var promise = Promise.<String>promise();

        var reference = new AtomicReference<String>();

        try
        {
            if (bootstrapType.equalsIgnoreCase(BootstrapType.APP.name()))
            {
                reference.set(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + fileName);

                promise.complete(reference.get());
            }
            else
            {
                reference.set(CURRENT_DIR + PATH_SEPARATOR + DOWNLOADS + PATH_SEPARATOR + (String.format(ARTIFACTS, bootstrapType.toLowerCase()) + "-" + System.currentTimeMillis()));

                vertx.fileSystem().open(reference.get(), new OpenOptions().setCreate(true), result ->
                {
                    if (result.succeeded())
                    {
                        LOGGER.info(String.format("GET : https://%s:%s%s", CommonUtil.getRemoteEventPublisher(), bootstrapType.equalsIgnoreCase(BootstrapType.AGENT.name()) ? AgentConfigUtil.getAgentHTTPServerPort() : MotadataConfigUtil.getHTTPServerPort(BootstrapType.APP.name()), String.format(ARTIFACT_URL, fileName)));

                        WebClientUtil.getWebClient().request(HttpMethod.GET, new RequestOptions().setURI(String.format(ARTIFACT_URL, fileName)).setHost(CommonUtil.getRemoteEventPublisher()).setPort(bootstrapType.equalsIgnoreCase(BootstrapType.AGENT.name()) ? AgentConfigUtil.getAgentHTTPServerPort() : MotadataConfigUtil.getHTTPServerPort(BootstrapType.APP.name()))).as(BodyCodec.pipe(result.result(), true)).send(asyncResult ->
                        {
                            try
                            {
                                if (asyncResult.succeeded())
                                {
                                    promise.complete(reference.get());
                                }
                                else
                                {
                                    promise.fail(asyncResult.cause());
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);

                                promise.fail(exception.getMessage());
                            }
                        });
                    }
                    else
                    {
                        promise.fail(result.cause());
                    }
                });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception.getMessage());
        }

        return promise.future();
    }

    private Future<Void> changeState(JsonObject event, String type)
    {
        var promise = Promise.<Void>promise();

        try
        {
            LOGGER.info(String.format("changing state to %s", type));

            loadConfigs(AGENT_FILE, true);

            var configs = AgentConfigUtil.getChildAgentConfigs();

            configs.getJsonObject(AGENT).put(AGENT_STATE, type);

            CommonUtil.dumpConfigs(AGENT_FILE, configs);

            event.put(AGENT_CONFIGS, configs);

            promise.complete();
        }
        catch (Exception exception)
        {
            promise.fail(exception.getMessage());

            LOGGER.error(exception);
        }

        return promise.future();
    }

    private Future<Void> sendAcknowledgement(JsonObject event)
    {
        event.put(EVENT_TYPE, EVENT_ACKNOWLEDGEMENT);

        return Future.succeededFuture();
    }

    private void resetRegistrationId(String bootstrapType)
    {
        try
        {
            LOGGER.info(String.format("resetting registration id for %s ", bootstrapType));

            if (bootstrapType.equalsIgnoreCase(GlobalConstants.BootstrapType.AGENT.name()))
            {
                loadConfigs(AGENT_FILE, true);

                var configs = AgentConfigUtil.getChildAgentConfigs();

                configs.getJsonObject(AGENT).put(AGENT_UUID, EMPTY_VALUE);

                CommonUtil.dumpConfigs(AGENT_FILE, configs);
            }
            else if (bootstrapType.equalsIgnoreCase(BootstrapType.APP.name())
                    || bootstrapType.equalsIgnoreCase(COLLECTOR.name())
                    || bootstrapType.equalsIgnoreCase(BootstrapType.EVENT_COLLECTOR.name())
                    || bootstrapType.equalsIgnoreCase(BootstrapType.FLOW_COLLECTOR.name())
                    || bootstrapType.equalsIgnoreCase(BootstrapType.EVENT_PROCESSOR.name()))
            {
                var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + REGISTRATION_FILE);

                if (file.exists())
                {
                    var configs = new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8));

                    configs.put(REMOTE_EVENT_PROCESSOR_UUID, EMPTY_VALUE);

                    CommonUtil.dumpConfigs(REGISTRATION_FILE, configs);
                }
            }
            else if (bootstrapType.equalsIgnoreCase(BootstrapType.DATASTORE.name()))
            {
                var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + "motadata-datastore.json");

                if (file.exists())
                {
                    CommonUtil.dumpConfigs("motadata-datastore.json", new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)).put(REMOTE_EVENT_PROCESSOR_UUID, EMPTY_VALUE));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private Future<String> setRegistrationId(String bootstrapType, int retires)
    {
        var promise = Promise.<String>promise();

        var attempts = new AtomicInteger();

        LOGGER.info(String.format("set registration id for %s ", bootstrapType));

        vertx.setPeriodic(0, 3000, timer ->
        {
            try
            {
                if (attempts.getAndIncrement() < retires)
                {
                    if (bootstrapType.equalsIgnoreCase(GlobalConstants.BootstrapType.AGENT.name()))
                    {
                        loadConfigs(AGENT_FILE, true);

                        var configs = AgentConfigUtil.getAgentConfigs();

                        if (!configs.isEmpty() && configs.containsKey(AGENT_UUID) && !configs.getString(AGENT_UUID).isEmpty())
                        {
                            vertx.cancelTimer(timer);

                            promise.complete(configs.getString(AGENT_UUID));
                        }
                    }
                    else
                    {
                        var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + (bootstrapType.equalsIgnoreCase(BootstrapType.DATASTORE.name()) ? "motadata-datastore.json" : REGISTRATION_FILE));

                        if (file.exists())
                        {
                            var configs = new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8));

                            if (!configs.isEmpty() && configs.containsKey(REMOTE_EVENT_PROCESSOR_UUID) && !configs.getString(REMOTE_EVENT_PROCESSOR_UUID).isEmpty())
                            {
                                vertx.cancelTimer(timer);

                                promise.complete(configs.getString(REMOTE_EVENT_PROCESSOR_UUID));
                            }
                        }
                    }
                }
                else
                {
                    LOGGER.info(String.format("failed to get registration after %s attempt id for %s ", attempts.get(), bootstrapType));

                    vertx.cancelTimer(timer);

                    promise.fail(EMPTY_VALUE);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        return promise.future();
    }

    private Future<String> updateProcessId(String bootstrapType, int retires)
    {
        var promise = Promise.<String>promise();

        var attempts = new AtomicInteger();

        try
        {
            var commands = buildProcessDetectionCommand(bootstrapType);

            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug(String.format("checking motadata-%s process having command %s", bootstrapType, commands));
            }

            vertx.setPeriodic(0, 3000, timer ->
            {
                try
                {
                    if (attempts.getAndIncrement() < retires)
                    {
                        executeCommand(commands, true).onComplete(result ->
                        {
                            try
                            {
                                if (result.succeeded() && !result.result().isEmpty() && !promise.future().isComplete())
                                {
                                    vertx.cancelTimer(timer);

                                    if (PATH_SEPARATOR.equalsIgnoreCase("\\")) // PID     :18720
                                    {
                                        promise.complete(result.result().split(":")[1].trim());
                                    }
                                    else
                                    {
                                        promise.complete(result.result());
                                    }
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }
                        });
                    }
                    else
                    {
                        vertx.cancelTimer(timer);

                        promise.fail(EMPTY_VALUE);
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });
        }
        catch (Exception exception)
        {
            promise.fail(exception.getMessage());

            LOGGER.error(exception);
        }

        return promise.future();
    }

    private Future<Void> deactivate(String bootstrapType)
    {
        var promise = Promise.<Void>promise();

        try
        {
            stop(bootstrapType).onComplete(result ->
            {
                try
                {
                    if (result.succeeded() || result.cause().getMessage().contains("not running"))
                    {
                        LOGGER.info(String.format("deactivating motadata-%s process...", bootstrapType));

                        if (bootstrapType.equalsIgnoreCase(BootstrapType.AGENT.name()))
                        {
                            loadConfigs(AGENT_FILE, true);

                            var configs = AgentConfigUtil.getChildAgentConfigs();

                            configs.put(AGENT, configs.getJsonObject(AGENT)
                                    .put(Agent.AGENT_DELETION_STATUS, YES)
                                    .put(AGENT_STATE, ENABLE)
                                    .put(AGENT_UUID, EMPTY_VALUE));

                            CommonUtil.dumpConfigs(AGENT_FILE, configs);
                        }

                        promise.complete();
                    }
                    else
                    {
                        promise.fail(result.cause().getMessage());
                    }
                }
                catch (Exception exception)
                {
                    promise.fail(exception.getMessage());

                    LOGGER.error(exception);
                }
            });
        }
        catch (Exception exception)
        {
            promise.fail(exception.getMessage());

            LOGGER.error(exception);
        }

        return promise.future();
    }

    private Future<Void> backup(String bootstrapType)
    {
        var promise = Promise.<Void>promise();

        try
        {
            var destination = CURRENT_DIR + PATH_SEPARATOR + BACKUP_FOLDER + PATH_SEPARATOR + String.format(ARTIFACTS, bootstrapType.toLowerCase());

            FileUtils.deleteQuietly(new File(destination)); // delete old backup

            new File(destination).mkdirs();

            var event = new JsonObject();

            event.put(EVENT_TYPE, String.format("%s.backup", bootstrapType.toLowerCase()));

            transferFiles(CURRENT_DIR, destination, event, bootstrapType, getBackupFiles()).onComplete(result ->
            {
                if (result.succeeded())
                {
                    promise.complete();
                }
                else
                {
                    promise.fail(result.cause().getMessage());
                }
            });
        }
        catch (Exception exception)
        {
            promise.fail(exception.getMessage());

            LOGGER.error(exception);
        }

        return promise.future();
    }

    private Future<Void> restore(String bootstrapType)
    {
        var promise = Promise.<Void>promise();

        try
        {
            var event = new JsonObject();

            event.put(EVENT_TYPE, String.format("%s.restore", bootstrapType.toLowerCase()));

            transferFiles(CURRENT_DIR + PATH_SEPARATOR + BACKUP_FOLDER + PATH_SEPARATOR + String.format(ARTIFACTS, bootstrapType.toLowerCase()), CURRENT_DIR, event, bootstrapType, new String[]{}).onComplete(result ->
            {
                if (result.succeeded())
                {
                    promise.complete();
                }
                else
                {
                    promise.fail(result.cause().getMessage());
                }
            });
        }
        catch (Exception exception)
        {
            promise.fail(exception.getMessage());

            LOGGER.error(exception);
        }

        return promise.future();
    }

    private Future<String> executeCommand(List<String> commands, boolean output)
    {
        if (CommonUtil.debugEnabled())
        {
            LOGGER.debug(String.format("executing command %s ", commands));
        }

        var promise = Promise.<String>promise();

        vertx.<String>executeBlocking(future ->
        {
            var processBuilder = new ProcessBuilder(commands);

            var builder = new StringBuilder();

            var errors = new StringBuilder();

            try
            {
                var process = processBuilder.start();

                if (output)
                {
                    var exited = process.waitFor(60, TimeUnit.SECONDS);

                    if (exited)
                    {
                        try (var reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                             var errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream())))
                        {
                            String line;

                            while ((line = reader.readLine()) != null)
                            {
                                builder.append(line).append(NEW_LINE);
                            }

                            while ((line = errorReader.readLine()) != null)
                            {
                                errors.append(line).append(NEW_LINE);
                            }

                            if (errors.isEmpty())
                            {
                                future.complete(builder.toString());
                            }
                            else
                            {
                                future.fail(errors.toString());
                            }
                        }
                    }
                    else
                    {
                        process.destroy();

                        future.fail(String.valueOf(EXIT_CODE_TIMED_OUT));
                    }
                }
                else
                {
                    future.complete(builder.toString()); // empty output in case of showOutput is false
                }
            }
            catch (Exception exception)
            {
                future.fail(exception.getMessage());

                LOGGER.error(exception);
            }
        }, asyncResult ->
        {
            if (asyncResult.succeeded())
            {
                promise.complete(asyncResult.result());
            }
            else
            {
                promise.fail(asyncResult.cause().getMessage());
            }
        });

        return promise.future();
    }

    private Future<Void> backup(JsonObject event)
    {
        vertx.eventBus().send(EVENT_DATABASE_BACKUP, event);

        return Future.succeededFuture();
    }

    // restoring config db
    private Future<Void> restore(JsonObject event)
    {
        var promise = Promise.<Void>promise();

        var status = new AtomicBoolean();

        var port = MotadataConfigUtil.getHTTPServerPort(BootstrapType.APP.name());

        var messageConsumer = new AtomicReference<MessageConsumer<JsonObject>>();

        startHTTPServer(MotadataConfigUtil.getHTTPServerPort(BootstrapType.MANAGER.name())).onComplete(asyncResponse ->
        {
            try
            {
                if (asyncResponse.succeeded())
                {
                    var response = new JsonObject();

                    LOGGER.info("HTTP server started successfully...");

                    send(event.copy().put(NMSConstants.STATE, NMSConstants.STATE_RUNNING).put(PORT, MotadataConfigUtil.getHTTPServerPort(BootstrapType.MANAGER.name())), false);

                    messageConsumer.set(vertx.eventBus().localConsumer("websocket.client.registered", message ->
                    {
                        try
                        {
                            if (!status.get()) // mandatory condition as we can receive multiple database.restore.start event if multiple user is connected from different tabs
                            {
                                status.set(true);

                                response.put(EVENT_TYPE, EVENT_DATABASE_RESTORE).put(STATUS, STATUS_SUCCEED).put(PROGRESS, 0.0).put(MESSAGE, "Validating artifacts");

                                send(response, true);

                                if (validateArtifact(event))
                                {
                                    send(response.put(PROGRESS, 10.0).put(MESSAGE, "Stopping motadata service"), true);

                                    stop(MotadataConfigUtil.getSystemBootstrapType()).onComplete(handlerResult ->
                                    {
                                        try
                                        {
                                            if (handlerResult.succeeded() || handlerResult.cause().getMessage().contains("not running"))
                                            {
                                                send(response.put(PROGRESS, 20.0).put(MESSAGE, "Taking backup of current database"), true);

                                                // backup existing database and removing the config directory
                                                if (backupCurrentDatabase())
                                                {
                                                    send(response.put(PROGRESS, 30.0).put(MESSAGE, "Restore in progress ..."), true);

                                                    // transferring file from zip to "current/config" directory
                                                    if (restoreFiles(event, response))
                                                    {
                                                        response.put(PROGRESS, 100.0).put(MESSAGE, "Restore completed. starting motadata service....");

                                                        send(response, true);

                                                        start(event, MotadataConfigUtil.getSystemBootstrapType()).onComplete(asyncResult ->
                                                        {
                                                            try
                                                            {
                                                                if (asyncResult.succeeded())
                                                                {
                                                                    send(response.put(MESSAGE, "Motadata service started successfully!").put(PORT, port), true);

                                                                    loadConfigs(MOTADATA_CONFIG_FILE, false);

                                                                    MotadataConfigUtil.loadArtifactVersions();

                                                                    promise.complete();
                                                                }
                                                                else
                                                                {
                                                                    send(response.put(STATUS, STATUS_FAIL).put(MESSAGE, asyncResult.cause().getMessage() + ", Recovering old config and starting motadata service"), true);

                                                                    FileUtils.deleteQuietly(new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR));

                                                                    transferFiles(CURRENT_DIR + PATH_SEPARATOR + "config-backup", CURRENT_DIR, event, BootstrapType.APP.name(), new String[]{}).onComplete(asyncHandlerResult ->
                                                                    {
                                                                        if (asyncHandlerResult.succeeded())
                                                                        {
                                                                            start(event, MotadataConfigUtil.getSystemBootstrapType()).onComplete(result ->
                                                                            {
                                                                                if (result.succeeded())
                                                                                {
                                                                                    send(response.put(PORT, port), true);
                                                                                }
                                                                                else
                                                                                {
                                                                                    send(response.put(MESSAGE, result.cause().getMessage()), true);


                                                                                    LOGGER.error(result.cause());
                                                                                }

                                                                                promise.fail(asyncResult.cause().getMessage());
                                                                            });
                                                                        }
                                                                        else
                                                                        {
                                                                            send(response.put(MESSAGE, asyncHandlerResult.cause().getMessage()), true);

                                                                            LOGGER.error(asyncHandlerResult.cause());
                                                                        }
                                                                    });
                                                                }
                                                            }
                                                            catch (Exception exception)
                                                            {
                                                                LOGGER.error(exception);
                                                            }
                                                        });
                                                    }
                                                    else
                                                    {
                                                        send(response.put(STATUS, STATUS_FAIL).put(MESSAGE, "failed to restore confing, Recovering old config and starting motadata service"), true);

                                                        FileUtils.deleteQuietly(new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR));

                                                        transferFiles(CURRENT_DIR + PATH_SEPARATOR + "config-backup", CURRENT_DIR, event, BootstrapType.APP.name(), new String[]{}).onComplete(asyncResult ->
                                                        {
                                                            if (asyncResult.succeeded())
                                                            {
                                                                start(event, MotadataConfigUtil.getSystemBootstrapType()).onComplete(result ->
                                                                {
                                                                    if (result.succeeded())
                                                                    {
                                                                        send(response.put(PORT, port), true);
                                                                    }
                                                                    else
                                                                    {
                                                                        send(response.put(MESSAGE, result.cause().getMessage()), true);

                                                                        LOGGER.error(result.cause());
                                                                    }

                                                                    promise.fail("failed to restore config");
                                                                });
                                                            }
                                                            else
                                                            {
                                                                send(response.put(MESSAGE, asyncResult.cause().getMessage()), true);

                                                                LOGGER.error(asyncResult.cause());
                                                            }
                                                        });
                                                    }
                                                }
                                                else
                                                {
                                                    send(response.put(STATUS, STATUS_FAIL).put(MESSAGE, "failed to backup existing config , starting motadata"), true);

                                                    start(event, MotadataConfigUtil.getSystemBootstrapType()).onComplete(result ->
                                                    {
                                                        if (result.succeeded())
                                                        {
                                                            send(response.put(PORT, port), true);
                                                        }
                                                        else
                                                        {
                                                            send(response.put(MESSAGE, result.cause().getMessage()), true);

                                                            LOGGER.error(result.cause());
                                                        }

                                                        promise.fail("failed to backup existing config");
                                                    });
                                                }
                                            }
                                            else
                                            {
                                                send(response.put(STATUS, STATUS_FAIL).put(MESSAGE, handlerResult.cause().getMessage()).put(PORT, port), true);

                                                promise.fail(handlerResult.cause().getMessage());
                                            }
                                        }
                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);

                                            send(response.put(STATUS, STATUS_FAIL).put(MESSAGE, exception.getMessage()).put(PORT, port), true);

                                            promise.fail(exception.getMessage());
                                        }
                                    });
                                }
                                else
                                {
                                    send(response.put(STATUS, STATUS_FAIL).put(MESSAGE, "failed to verify artifacts").put(PORT, port), true);

                                    promise.fail("failed to verify artifacts");
                                }
                            }
                            else
                            {
                                send(response, true);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            promise.fail(exception.getMessage());
                        }
                    }));

                    // restore will be failed if we don't get the restore.start event within 60 seconds from the UI
                    vertx.setTimer(60_000, timer ->
                    {
                        if (!promise.future().isComplete() && !status.get())
                        {
                            promise.fail("UI failed to connect to server ...");
                        }
                    });
                }
                else
                {
                    LOGGER.info(String.format("failed to start HTTP server , reason : %s", asyncResponse.cause().getMessage()));

                    promise.fail(asyncResponse.cause().getMessage());
                }
            }
            catch (Exception exception)
            {
                promise.fail(exception.getMessage());
            }
            finally
            {
                promise.future().onComplete(result ->
                {
                    status.set(false);

                    if (messageConsumer.get() != null)
                    {
                        messageConsumer.get().unregister();
                    }

                    if (httpServer != null)
                    {
                        // this 5 second wait is required as it closes the httpServer before UI receives the last event.
                        vertx.setTimer(5000, timer ->
                        {
                            httpServer.close();

                            httpServer = null;
                        });
                    }
                });
            }
        });

        return promise.future();
    }

    // upgrading master application
    private Future<Void> upgrade(JsonObject event)
    {
        var promise = Promise.<Void>promise();

        var status = new AtomicBoolean();

        var port = MotadataConfigUtil.getHTTPServerPort(BootstrapType.APP.name());

        var messageConsumer = new AtomicReference<MessageConsumer<JsonObject>>();

        startHTTPServer(MotadataConfigUtil.getHTTPServerPort(BootstrapType.MANAGER.name())).onComplete(asyncResult ->
        {
            try
            {
                if (asyncResult.succeeded())
                {
                    var response = new JsonObject();

                    LOGGER.info("HTTP server started successfully...");

                    send(event.copy().put(NMSConstants.STATE, NMSConstants.STATE_RUNNING).put(PORT, MotadataConfigUtil.getHTTPServerPort(BootstrapType.MANAGER.name())), false);

                    messageConsumer.set(vertx.eventBus().localConsumer("websocket.client.registered", message ->
                    {
                        try
                        {
                            if (!status.get()) // mandatory condition as we can receive multiple database.restore.start event if multiple user is connected from different tabs
                            {
                                status.set(true);

                                upgrade(event, MotadataConfigUtil.getSystemBootstrapType(), response, port).onComplete(upgrade ->
                                {
                                    try
                                    {
                                        if (upgrade.succeeded())
                                        {
                                            promise.complete();

                                            send(new JsonObject().put(ID, event.getLong(ID)).put(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.APP.name()).put(EVENT_TYPE, EVENT_MASTER_UPGRADE).put(STATUS, STATUS_SUCCEED), false);
                                        }
                                        else
                                        {
                                            promise.fail(upgrade.cause().getMessage());

                                            send(new JsonObject().put(ID, event.getLong(ID)).put(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.APP.name()).put(EVENT_TYPE, EVENT_MASTER_UPGRADE).put(STATUS, STATUS_FAIL).put(MESSAGE, upgrade.cause().getMessage()), false);
                                        }

                                        // need to clear the configs as we've temporarily merged the content of manager.json while upgrade
                                        MotadataConfigUtil.getConfigs().clear();

                                        MotadataConfigUtil.loadConfigs(new JsonObject(Files.readString(new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + "motadata.json").toPath(), StandardCharsets.UTF_8)));
                                    }
                                    catch (Exception exception)
                                    {
                                        LOGGER.error(exception);

                                        if (!promise.future().isComplete())
                                        {
                                            promise.fail(exception.getMessage());
                                        }
                                    }
                                });
                            }
                            else
                            {
                                send(response, true);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            promise.fail(exception.getMessage());
                        }
                    }));

                    // upgrade will be failed if we don't get the restore.start event within 60 seconds from the UI
                    vertx.setTimer(60_000, timer ->
                    {
                        if (!promise.future().isComplete() && !status.get())
                        {
                            promise.fail("UI failed to connect to server ...");
                        }
                    });
                }
                else
                {
                    LOGGER.info(String.format("failed to start HTTP server , reason : %s", asyncResult.cause().getMessage()));

                    promise.fail(asyncResult.cause().getMessage());
                }
            }
            catch (Exception exception)
            {
                promise.fail(exception.getMessage());
            }
            finally
            {
                promise.future().onComplete(result ->
                {
                    status.set(false);

                    if (messageConsumer.get() != null)
                    {
                        messageConsumer.get().unregister();
                    }

                    if (httpServer != null)
                    {
                        // this 5 second wait is required as it closes the httpServer before UI receives the last event.
                        vertx.setTimer(5000, timer ->
                        {
                            httpServer.close();

                            httpServer = null;
                        });
                    }
                });
            }
        });

        return promise.future();
    }

    private Future<Void> upgradeManager(JsonObject event)
    {
        var promise = Promise.<Void>promise();

        var fileName = event.getString(PATCH_ARTIFACT_FILE, EMPTY_VALUE);

        try
        {
            if (!fileName.isEmpty())
            {
                // downloading zip file in "downloads" directory with name "manager-artifacts-{timestamp}"
                downloadFiles(MotadataConfigUtil.getSystemBootstrapType(), fileName).onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        LOGGER.info(String.format("files fetched to %s ...", result.result()));

                        var zipFile = result.result();

                        var destination = CURRENT_DIR + PATH_SEPARATOR + DOWNLOADS + PATH_SEPARATOR + String.format(ARTIFACTS, BootstrapType.MANAGER.name().toLowerCase());

                        FileUtils.deleteQuietly(new File(destination)); // delete old artifacts

                        new File(destination).mkdirs();

                        try (var fileInputStream = new BufferedInputStream(new FileInputStream(zipFile));
                             var zipInputStream = new ZipInputStream(new BufferedInputStream(fileInputStream)))
                        {
                            ZipEntry entry;

                            while ((entry = zipInputStream.getNextEntry()) != null)
                            {
                                var entryName = entry.getName();

                                if (!entryName.isEmpty())
                                {
                                    if (entry.isDirectory())
                                    {
                                        if (!new File(destination, entryName).mkdir())
                                        {
                                            throw new RuntimeException("failed to create directory");
                                        }
                                    }
                                    else
                                    {
                                        var length = 0;

                                        var bytes = new byte[BUFFER_SIZE];

                                        BufferedOutputStream outputStream = null;

                                        try
                                        {
                                            var file = new File(destination, entryName);

                                            var fileOutputStream = new FileOutputStream(file);

                                            outputStream = new BufferedOutputStream(fileOutputStream, BUFFER_SIZE);

                                            while ((length = zipInputStream.read(bytes, 0, BUFFER_SIZE)) != NOT_AVAILABLE)
                                            {
                                                outputStream.write(bytes, 0, length);
                                            }

                                            outputStream.flush();

                                            if (!PATH_SEPARATOR.equalsIgnoreCase("\\"))
                                            {
                                                // change permission to 755
                                                file.setExecutable(true, false);

                                                file.setReadable(true, true);

                                                file.setWritable(true, true);
                                            }
                                        }
                                        finally
                                        {
                                            if (outputStream != null)
                                            {
                                                outputStream.close();
                                            }
                                        }
                                    }
                                }
                            }

                            // check if "upgrade.me" file present in the "downloads/manager-artifacts" directory
                            if (new File(destination + PATH_SEPARATOR + MANAGER_UPGRADE_FILE).exists())
                            {
                                FileUtils.writeStringToFile(new File(destination + PATH_SEPARATOR + MANAGER_UPGRADE_FILE), fileName, StandardCharsets.UTF_8);

                                // transferring "upgrade.me" file from "downloads/manager-artifacts" directory to current directory
                                transferFiles(destination, CURRENT_DIR, event, BootstrapType.MANAGER.name(), new String[]{MANAGER_UPGRADE_FILE, LINUX_MANAGER_UPGRADER_SCRIPT, WINDOWS_MANAGER_UPGRADER_SCRIPT}).onComplete(asyncResult ->
                                {
                                    if (asyncResult.succeeded())
                                    {
                                        promise.complete();
                                    }
                                    else
                                    {
                                        promise.fail(asyncResult.cause());
                                    }
                                });
                            }
                            else
                            {
                                send(event.put(STATUS, STATUS_FAIL).put(MESSAGE, "upgarde.me file not found"), false);

                                promise.fail("upgarde.me file not found");
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            send(event.put(STATUS, STATUS_FAIL).put(MESSAGE, exception.getMessage()), false);

                            promise.fail(exception.getMessage());
                        }
                    }
                    else
                    {
                        send(event.put(STATUS, STATUS_FAIL).put(MESSAGE, result.cause().getMessage()), false);

                        promise.fail(result.cause().getMessage());
                    }
                });
            }
            else
            {
                send(event.put(STATUS, STATUS_FAIL).put(MESSAGE, "Invalid file name"), false);

                promise.fail("Invalid file name");
            }
        }
        catch (Exception exception)
        {
            promise.fail(exception.getMessage());
        }

        return promise.future();
    }

    private Future<Void> startHTTPServer(int port)
    {
        var promise = Promise.<Void>promise();

        httpServer = vertx.createHttpServer(new HttpServerOptions()
                .setUseAlpn(true) //http2
                .setSsl(MotadataConfigUtil.httpsEnabled())
                .setCompressionSupported(true)
                .setPemKeyCertOptions(new PemKeyCertOptions()
                        .setCertPath(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "server-cert.pem")
                        .setKeyPath(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "server-key.pem")));

        var router = Router.router(vertx);

        router.route("/eventbus/*").subRouter(eventBusHandler());

        router.route().handler(StaticHandler.create("dist/index.html"));

        router.route("/*").method(HttpMethod.GET).handler(StaticHandler.create(FileSystemAccess.ROOT, CURRENT_DIR + "/dist"));

        router.route("/*").method(HttpMethod.GET).handler(routingContext -> routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_TEXT_HTML)
                .sendFile(CURRENT_DIR + "/dist/index.html"));

        httpServer.requestHandler(router)
                .exceptionHandler(LOGGER::error)
                .listen(port, result ->
                {
                    if (result.succeeded())
                    {
                        promise.complete();
                    }
                    else
                    {
                        promise.fail(result.cause());
                    }
                });

        return promise.future();
    }

    private Router eventBusHandler()
    {
        return SockJSHandler.create(vertx, new SockJSHandlerOptions().setHeartbeatInterval(10_000).addDisabledTransport(Transport.XHR.name()).addDisabledTransport(Transport.JSON_P.name()).addDisabledTransport(Transport.EVENT_SOURCE.name())
                .addDisabledTransport(Transport.HTML_FILE.name())).bridge(new SockJSBridgeOptions()
                .addOutboundPermitted(new PermittedOptions().setAddress(EventBusConstants.EVENT_UI))
                .addOutboundPermitted(new PermittedOptions().setAddressRegex(EventBusConstants.EVENT_USER_REGEX)), event ->
        {
            try
            {
                if (event.type() != null && event.type() == BridgeEventType.REGISTER)
                {
                    if (CommonUtil.debugEnabled())
                    {
                        LOGGER.debug(String.format("client registered : %s", event.socket().remoteAddress()));
                    }

                    vertx.eventBus().publish("websocket.client.registered", new JsonObject());
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
            finally
            {
                event.complete(true);
            }
        });
    }

    private boolean restoreFiles(JsonObject event, JsonObject response)
    {
        var finished = false;

        var destination = CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR;

        if (new File(destination).exists())
        {
            try (var fileInputStream = new BufferedInputStream(new FileInputStream(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + event.getString(ConfigDBConstants.BACKUP_FILE)));
                 var zipInputStream = new ZipInputStream(new BufferedInputStream(fileInputStream)))
            {
                var artifact = new ZipFile(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + event.getString(ConfigDBConstants.BACKUP_FILE));

                var progress = CommonUtil.getFloat((90 - CommonUtil.getFloat(response.getValue(PROGRESS))) / (float) artifact.size());

                artifact.close();

                ZipEntry entry;

                while ((entry = zipInputStream.getNextEntry()) != null)
                {
                    send(response.put(PROGRESS, CommonUtil.getFloat(response.getValue(PROGRESS)) + progress), true);

                    var entryName = entry.getName();

                    if (!entryName.isEmpty() && !entryName.equalsIgnoreCase(VERSION_FILE))
                    {
                        LOGGER.debug(String.format("restoring %s file from %s to %s", entryName, CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + event.getString(ConfigDBConstants.BACKUP_FILE), destination));

                        if (entry.isDirectory())
                        {
                            if (!new File(destination, entryName).mkdir())
                            {
                                throw new RuntimeException("failed to create directory");
                            }
                        }
                        else
                        {
                            var length = 0;

                            var bytes = new byte[BUFFER_SIZE];

                            BufferedOutputStream outputStream = null;

                            try
                            {
                                var file = new File(destination, entryName);

                                var fileOutputStream = new FileOutputStream(file);

                                outputStream = new BufferedOutputStream(fileOutputStream, BUFFER_SIZE);

                                while ((length = zipInputStream.read(bytes, 0, BUFFER_SIZE)) != NOT_AVAILABLE)
                                {
                                    outputStream.write(bytes, 0, length);
                                }

                                outputStream.flush();
                            }
                            finally
                            {
                                if (outputStream != null)
                                {
                                    outputStream.close();
                                }
                            }
                        }
                    }
                }

                finished = true;
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }

        return finished;
    }

    private boolean backupCurrentDatabase()
    {
        var finished = false;

        try
        {
            var backupDirectory = new File(CURRENT_DIR + PATH_SEPARATOR + "config-backup");

            FileUtils.deleteQuietly(backupDirectory);

            if (backupDirectory.mkdirs())
            {
                FileUtils.copyDirectoryToDirectory(new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR), backupDirectory);

                finished = true;
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return finished;
    }

    private boolean validateArtifact(JsonObject event)
    {
        var version = EMPTY_VALUE;

        try (var file = new ZipFile(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + event.getString(ConfigDBConstants.BACKUP_FILE)))
        {
            var entry = file.getEntry(VERSION_FILE);

            if (entry != null && entry.getSize() < MAX_VERSION_FILE_SIZE_BYTES)
            {
                var reader = new BufferedReader(new InputStreamReader(file.getInputStream(entry)));

                var line = reader.readLine();

                if (CommonUtil.isNotNullOrEmpty(line))
                {
                    version = CommonUtil.getString(line.trim().split(NEW_LINE)[0].trim());
                }

                reader.close();
            }

            var versions = version.split("\\.");

            var systemVersions = event.getString(VERSION).trim().split("\\.");

            // major and minor version must be the same and patch version must be equal or lower
            return versions.length == 3 && CommonUtil.getInteger(versions[0]) == CommonUtil.getInteger(systemVersions[0])
                    && CommonUtil.getInteger(versions[1]) == CommonUtil.getInteger(systemVersions[1])
                    && CommonUtil.getInteger(versions[2]) <= CommonUtil.getInteger(systemVersions[2]);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return false;
    }

    private Future<Void> transferFiles(String source, String destination, JsonObject event, String bootSequence, String[] fileNames)
    {
        var promise = Promise.<Void>promise();

        var sourceDirectory = new File(source);

        if (sourceDirectory.isDirectory())
        {
            var files = fileNames != null && fileNames.length > 0 ? sourceDirectory.listFiles((path, name) -> Arrays.stream(fileNames).anyMatch(name::equalsIgnoreCase)) : sourceDirectory.listFiles();

            transferFiles(source, destination, event, bootSequence, files).onComplete(result ->
            {
                if (result.succeeded())
                {
                    promise.complete();
                }
                else
                {
                    promise.fail(result.cause());
                }
            });
        }
        else
        {
            promise.fail(String.format("%s is not directory", sourceDirectory));
        }

        return promise.future();
    }

    private Future<Void> updateConfigFiles(String source)
    {
        var promise = Promise.<Void>promise();

        try
        {
            var sourceDirectory = new File(source);

            if (sourceDirectory.isDirectory())
            {
                var files = sourceDirectory.listFiles();

                if (files != null)
                {
                    for (var file : files)
                    {
                        vertx.fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + file.getName(),
                                Buffer.buffer(new JsonObject(vertx.fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + file.getName()))
                                        .mergeIn(new JsonObject(vertx.fileSystem().readFileBlocking(file.getAbsolutePath())), true)
                                        .encodePrettily()));

                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format("file %s updated successfully", file.getName()));
                        }
                    }
                }

                promise.complete();
            }
            else
            {
                promise.fail(String.format("%s is not directory", sourceDirectory));
            }
        }
        catch (Exception exception)
        {
            promise.fail(exception.getMessage());

            LOGGER.error(exception);
        }

        return promise.future();
    }

    private Future<Void> transferFiles(String source, String destination, JsonObject event, String bootSequence, File[] files)
    {
        var promise = Promise.<Void>promise();

        Bootstrap.vertx().<Void>executeBlocking(future ->
        {
            try
            {
                LOGGER.info(String.format("transferring files from %s to %s for event %s", source, destination, event.getString(EVENT_TYPE)));

                var sourceDirectory = new File(source);

                if (sourceDirectory.isDirectory())
                {
                    var sourceFiles = files != null && files.length > 0 ? files : sourceDirectory.listFiles();

                    if (sourceFiles != null)
                    {
                        for (var file : sourceFiles)
                        {
                            var fileName = file.getName();

                            if (!PATH_SEPARATOR.equalsIgnoreCase("\\"))
                            {
                                // change permission to 755
                                file.setExecutable(true, false);

                                file.setReadable(true, true);

                                file.setWritable(true, true);
                            }

                            if (fileName.equalsIgnoreCase(VERSION_FILE))
                            {
                                var version = EMPTY_VALUE;

                                if (bootSequence.equalsIgnoreCase(BootstrapType.AGENT.name()))
                                {
                                    version = AGENT_VERSION;
                                }
                                else if (bootSequence.equalsIgnoreCase(BootstrapType.COLLECTOR.name()))
                                {
                                    version = REMOTE_EVENT_PROCESSOR_VERSION;
                                }

                                if (!version.isEmpty())
                                {
                                    event.put(version, Files.readString(file.toPath(), StandardCharsets.UTF_8));
                                }
                            }

                            if (CommonUtil.traceEnabled())
                            {

                                LOGGER.trace(String.format("file %s transferring ", fileName));
                            }

                            if (file.isDirectory())
                            {
                                FileUtils.copyDirectory(file, new File(destination + PATH_SEPARATOR + fileName));
                            }
                            else
                            {
                                FileUtils.copyFile(file, new File(destination + PATH_SEPARATOR + fileName));
                            }
                        }
                    }

                    future.complete();
                }
                else
                {
                    future.fail(String.format("%s is not directory", sourceDirectory));
                }
            }
            catch (Exception exception)
            {
                future.fail(exception.getMessage());

                LOGGER.error(exception);
            }
        }, result ->
        {
            if (result.succeeded())
            {
                promise.complete();
            }
            else
            {
                promise.fail(result.cause().getMessage());
            }
        });

        return promise.future();
    }

    private void loadConfigs(String fileName, boolean agent) throws Exception
    {
        var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + fileName);

        if (file.exists())
        {
            if (agent)
            {
                AgentConfigUtil.loadAgentConfigs(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));
            }
            else
            {
                MotadataConfigUtil.loadConfigs(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));
            }
        }
    }

    private File[] getBackupFiles()
    {
        // before upgrading will take set of mandatory file's backup.
        return new File(CURRENT_DIR).listFiles((path, name) -> !SYSTEM_FILES.contains(name));
    }

    // this method will return start command based on bootSequence (motadata,collector,agent,datastore)
    private List<String> buildProcessStartCommand(String bootSequence)
    {
        var commands = new ArrayList<String>();

        if (PATH_SEPARATOR.equalsIgnoreCase("\\"))
        {
            commands.add("cmd.exe");

            commands.add("/c");

            if (bootSequence.equalsIgnoreCase(GlobalConstants.BootstrapType.AGENT.name()))
            {
                commands.add(String.format("motadata-agent.exe %s", bootSequence));
            }
            else
            {
                commands.add(String.format("motadata.exe %s", bootSequence));
            }
        }
        else
        {
            commands.add("/bin/sh");

            commands.add("-c");

            if (bootSequence.equalsIgnoreCase(GlobalConstants.BootstrapType.AGENT.name()))
            {
                if (OS_AIX)
                {
                    LOGGER.info("staring AIX agent");

                    commands.add("./motadata-agent.sh");
                }
                else
                {
                    commands.add(String.format("./motadata-agent %s", bootSequence));
                }
            }
            else if (bootSequence.equalsIgnoreCase(BootstrapType.DATASTORE.name()))
            {
                commands.add("./motadatadb DATASTORE");
            }
            else
            {
                commands.add(String.format("./motadata %s", bootSequence));
            }
        }

        return commands;
    }

    // this method  will return stop command based on bootSequence
    private List<String> buildProcessStopCommand(String bootSequence, boolean forcefully)
    {
        var commands = new ArrayList<String>();

        if (PATH_SEPARATOR.equalsIgnoreCase("\\"))
        {
            commands.add("cmd.exe");

            commands.add("/c");

            if (bootSequence.equalsIgnoreCase(GlobalConstants.BootstrapType.AGENT.name()))
            {
                commands.add("taskkill /f /im motadata-agent.exe /im motadata-metric-agent.exe /im motadata-log-agent.exe /im motadata-eventlog-agent.exe /im motadata-packet-agent.exe");
            }
            else
            {
                commands.add("taskkill /f /im motadata.exe");
            }
        }
        else
        {
            commands.add("/bin/sh");

            commands.add("-c");

            if (bootSequence.equalsIgnoreCase(GlobalConstants.BootstrapType.AGENT.name()))
            {
                if (OS_AIX)
                {
                    if (forcefully)
                    {
                        commands.add(String.format("ps -ef | grep -E \"[m]otadata-metric-agent|[m]otadata-log-agent|[m]otadata-packet-agent|[c]om.mindarray.Bootstrap %s\" | awk -F\" \" 'system(\"kill -9 \"$2\"\")' ", bootSequence));
                    }
                    else
                    {
                        commands.add(String.format("ps -ef | grep -E \"[m]otadata-metric-agent|[m]otadata-log-agent|[m]otadata-packet-agent|[c]om.mindarray.Bootstrap %s\" | awk -F\" \" 'system(\"kill \"$2\"\")' ", bootSequence));
                    }
                }
                else
                {
                    if (forcefully)
                    {
                        commands.add(String.format("ps -ef | grep \"[m]otadata-agent %s\\|[m]otadata-metric-agent\\|[m]otadata-log-agent\\|[m]otadata-packet-agent\" | grep -v motadata-manager | awk -F\" \" 'system(\"kill -9 \"$2\"\")' ", bootSequence));
                    }
                    else
                    {
                        commands.add(String.format("ps -ef | grep \"[m]otadata-agent %s\\|[m]otadata-metric-agent\\|[m]otadata-log-agent\\|[m]otadata-packet-agent\" | grep -v motadata-manager | awk -F\" \" 'system(\"kill  \"$2\"\")' ", bootSequence));
                    }
                }
            }
            else if (bootSequence.equalsIgnoreCase(BootstrapType.DATASTORE.name()))
            {
                commands.add("ps -ef | grep \"[m]otadatadb DATASTORE$\" | grep -v motadata-manager | awk -F\" \" 'system(\"kill  \"$2\"\")' ");
            }
            else
            {
                commands.add(String.format("ps -ef | grep \"[m]otadata %s$\" | grep -v motadata-manager | awk -F\" \" 'system(\"kill  \"$2\"\")' && ps -ef | grep \"[n]facctd\\|[s]facctd\" | grep -v motadata-manager | awk -F\" \" 'system(\"kill -9 \"$2\"\")' ", bootSequence));
            }
        }
        return commands;
    }

    // this method will return command to detect process based on bootSequence
    private List<String> buildProcessDetectionCommand(String bootSequence)
    {
        var commands = new ArrayList<String>();

        if (PATH_SEPARATOR.equalsIgnoreCase("\\"))
        {
            commands.add("cmd.exe");

            commands.add("/c");

            if (bootSequence.equalsIgnoreCase(GlobalConstants.BootstrapType.AGENT.name()))
            {
                commands.add("tasklist /v /fi \"imagename EQ motadata-agent.exe\" /FO LIST | FIND \"PID:\"");
            }
            else
            {
                commands.add("tasklist /v /fi \"imagename EQ motadata.exe\" /FO LIST | FIND \"PID:\"");
            }
        }
        else
        {
            commands.add("/bin/sh");

            commands.add("-c");

            if (bootSequence.equalsIgnoreCase(GlobalConstants.BootstrapType.AGENT.name()))
            {
                if (OS_AIX)
                {
                    commands.add(String.format("ps -ef | grep \"[c]om.mindarray.Bootstrap %s$\" | awk '{print $2}'", bootSequence));
                }
                else
                {
                    commands.add(String.format("ps -aux | grep -v motadata-manager | grep \"[m]otadata-agent %s$\" | awk '{print $2}'", bootSequence));
                }
            }
            else if (bootSequence.equalsIgnoreCase(BootstrapType.DATASTORE.name()))
            {
                commands.add("ps -aux | grep -v motadata-manager  | grep \"[m]otadatadb DATASTORE$\" | awk '{print $2}'");
            }
            else
            {
                commands.add(String.format("ps -aux | grep -v motadata-manager  | grep \"[m]otadata %s$\" | awk '{print $2}'", bootSequence));
            }
        }
        return commands;
    }

    private void send(JsonObject event, boolean publish)
    {
        if (publish)
        {
            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("sending event to UI : %s", event.encodePrettily()));
            }

            publish(event.getString(EVENT_TYPE), event);
        }
        else
        {
            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("sending event to motadata : %s", event.encodePrettily()));

            }

            vertx.eventBus().send(EVENT_MOTADATA_MANAGER, event);
        }
    }

    @Override
    public void stop(Promise<Void> promise)
    {
        eventEngine.stop(vertx);

        LOGGER.info(CHILD_PROCESSES);

        stop(CHILD_PROCESSES.toArray(), promise, new AtomicInteger(0));
    }

}
