/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*   Change Logs:
 *   Date               Author            Notes
 *   17-Mar-2025        Chandresh         MOTADATA-5379: Assign ping and trace route runbook by default to all monitors except cloud
 *   20-Feb-2025		Pruthviraj		  MOTADATA-4904: added netroute runbook
 *   11-APR-2025        Sankalp           MOTADATA-5180: Added Ansible category in Runbook
 * */


package com.mindarray.runbook;

import com.mindarray.GlobalConstants;
import com.mindarray.api.StorageProfile;
import com.mindarray.nms.NMSConstants;
import com.mindarray.util.CommonUtil;
import io.vertx.core.json.JsonArray;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Abstract base class for the Runbook system.
 * <p>
 * The Runbook system provides automation capabilities for executing predefined
 * operational procedures across different environments and technologies. This class
 * defines core constants, enumerations, and utility methods used throughout the
 * Runbook package.
 * <p>
 * Runbooks can be of different types (SSH, PowerShell, HTTP, etc.) and are categorized
 * based on their purpose (Diagnostics, Troubleshooting, etc.).
 */
public abstract class Runbook
{
    /**
     * Default runbooks that are automatically assigned to compatible objects.
     * Currently includes PING and TRACE_ROUTE runbooks.
     */
    public static final JsonArray DEFAULT_RUNBOOKS = new JsonArray().add(Runbook.RunbookPluginId.PING.getName()).add(Runbook.RunbookPluginId.TRACE_ROUTE.getName());

    /**
     * Returns a list of object types compatible with the specified runbook plugin type.
     * <p>
     * This method determines which object types (e.g., Windows, Linux, Network devices)
     * can be targeted by a specific type of runbook plugin.
     *
     * @param runbookPluginType The type of runbook plugin to get compatible object types for
     * @return A JsonArray containing the names of compatible object types, or null if none are defined
     */
    public static JsonArray getObjectTypesByRunbookPluginType(Runbook.RunbookPluginType runbookPluginType)
    {
        return switch (runbookPluginType)
        {
            case POWERSHELL_SCRIPT ->
                    new JsonArray().add(NMSConstants.Type.WINDOWS.getName()).add(NMSConstants.Type.WINDOWS_CLUSTER.getName()).add(NMSConstants.Type.HYPER_V.getName()).add(NMSConstants.Type.HYPER_V_CLUSTER.getName());

            // for SSH scripts, added all the supported types including Linux/Unix-based systems, network devices and other hardware
            case SSH_SCRIPT ->
                    new JsonArray().add(NMSConstants.Type.LINUX.getName()).add(NMSConstants.Type.SOLARIS.getName()).add(NMSConstants.Type.HP_UX.getName()).add(NMSConstants.Type.IBM_AIX.getName())
                            .add(NMSConstants.Type.ROUTER.getName()).add(NMSConstants.Type.SWITCH.getName()).add(NMSConstants.Type.FIREWALL.getName()).add(NMSConstants.Type.SNMP_DEVICE.getName())
                            .add(NMSConstants.Type.PRINTER.getName()).add(NMSConstants.Type.LOAD_BALANCER.getName()).add(NMSConstants.Type.UPS.getName()).add(NMSConstants.Type.WIRELESS_CONTROLLER.getName())
                            .add(NMSConstants.Type.HARDWARE_SENSOR.getName()).add(NMSConstants.Type.EMAIL_GATEWAY.getName());

            case DATABASE_SCRIPT ->
                    new JsonArray().add(NMSConstants.Type.WINDOWS.getName()).add(NMSConstants.Type.LINUX.getName())
                            .add(NMSConstants.Type.SOLARIS.getName()).add(NMSConstants.Type.HP_UX.getName()).add(NMSConstants.Type.IBM_AIX.getName());

            case SNMP ->
                    new JsonArray(NMSConstants.NETWORK_DEVICES.stream().map(CommonUtil::getString).collect(Collectors.toList())).add(NMSConstants.Type.ARUBA_WIRELESS.getName()).add(NMSConstants.Type.CISCO_WIRELESS.getName()).add(NMSConstants.Type.IBM_TAPE_LIBRARY.getName());

            default -> null;
        };
    }

    /**
     * Returns the runbook ID associated with a specific storage protocol.
     * <p>
     * This method maps storage protocols (FTP, TFTP, SFTP) to their corresponding
     * runbook plugin IDs for storage operations.
     *
     * @param protocol The storage protocol to get the runbook ID for
     * @return The runbook ID for the specified protocol, or DUMMY_ID if no mapping exists
     */
    public static long getStorageProfileRunbookId(StorageProfile.StorageProtocol protocol)
    {
        return switch (protocol)
        {
            case FTP -> Runbook.RunbookPluginId.FTP.getName();

            case TFTP -> Runbook.RunbookPluginId.TFTP.getName();

            case SFTP -> Runbook.RunbookPluginId.SFTP.getName();

            default -> GlobalConstants.DUMMY_ID;
        };
    }

    /**
     * Enumeration of supported runbook plugin types.
     * <p>
     * This enum defines the different types of runbook plugins that can be created and executed.
     * Each type represents a different technology or execution method for runbooks.
     */
    public enum RunbookPluginType
    {
        CUSTOM_SCRIPT("Custom Script"),

        SSH_SCRIPT("SSH Script"),

        POWERSHELL_SCRIPT("Powershell Script"),

        HTTP_SCRIPT("HTTP Script"),

        DATABASE_SCRIPT("Database Script"),

        SNMP("SNMP"),

        PING("Ping"),

        TRACE_ROUTE("Trace Route"),

        MAC_SCANNER("MAC Scanner"),

        ANSIBLE_PLAYBOOK("Ansible Playbook"),

        SHUTDOWN_MOTADATA("Shutdown Motadata"),

        WHOIS_LOOKUP("Whois Lookup");

        private static final Map<String, RunbookPluginType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(RunbookPluginType::getName, e -> e)));
        private final String name;

        RunbookPluginType(String name)
        {
            this.name = name;
        }

        public static RunbookPluginType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    /**
     * Enumeration of predefined runbook plugin IDs.
     * <p>
     * This enum defines unique identifiers for system-provided runbook plugins.
     * These IDs are used to reference specific built-in runbooks throughout the system.
     */
    public enum RunbookPluginId
    {
        PING(10000000000001L),

        TRACE_ROUTE(10000000000002L),

        SNMP_NEXT_HOP(10000000000003L),

        MAC_SCANNER(10000000000004L),

        INTERFACE_STATUS(10000000000021L),

        FTP(10000000000022L),

        TFTP(10000000000023L),

        SFTP(10000000000024L),

        SERVICEOPS_TICKET(10000000000025L),

        WHOIS_LOOKUP(10000000000044L);

        private static final Map<Long, RunbookPluginId> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(RunbookPluginId::getName, e -> e)));
        private final long name;

        RunbookPluginId(long name)
        {
            this.name = name;
        }

        public static RunbookPluginId valueOfName(long name)
        {
            return VALUES.get(name);
        }

        public long getName()
        {
            return name;
        }
    }

    /**
     * Enumeration of runbook categories.
     * <p>
     * This enum defines the different categories that runbooks can be classified into,
     * based on their purpose and functionality. Categories help organize runbooks
     * and make them easier to discover and manage.
     */
    public enum RunbookCategory
    {
        DIAGNOSTICS("Diagnostics"),

        TROUBLESHOOTING("Troubleshooting"),

        INTEGRATION("Integration"),

        LOG_COLLECTION("Log Collection"),

        CONFIG("Config"),

        SYSTEM_MANAGEMENT("System Management"),

        PERFORMANCE_MONITORING("Performance Monitoring"),

        STORAGE_PROFILE("Storage Profile"),

        ANSIBLE_PLAYBOOK("Ansible Playbook"),

        OTHER("Other");

        private static final Map<String, RunbookCategory> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(RunbookCategory::getName, e -> e)));
        private final String name;

        RunbookCategory(String name)
        {
            this.name = name;
        }

        public static RunbookCategory valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

}
