/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			        Notes
 *  6-Mar-2025		Pruthviraj Jadeja		Initial commit
 */

package com.mindarray.visualization;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.api.User;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.netroute.NetRouteConstants;
import com.mindarray.store.UserConfigStore;
import com.mindarray.store.WidgetConfigStore;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.datastore.DatastoreConstants.PluginId.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.visualization.VisualizationConstants.*;

public class VisualizationNetRouteManager extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(VisualizationNetRouteManager.class, GlobalConstants.MOTADATA_VISUALIZATION, "Visualization NetRoute Manager");

    private final JsonObject columns = new JsonObject();

    private final Map<Long, Map<Long, String>> queryStatuses = new HashMap<>();//queryId along with subqueryIds and status

    private final Map<Long, JsonObject> subQueries = new HashMap<>(); //subquery context

    private final Map<Long, JsonObject> queryContexts = new HashMap<>(); //query original request context

    private final Map<Long, String> queries = new HashMap<>(); //queryid

    private final Map<Long, Short> queryTrackers = new HashMap<>();//query progress tracker

    private final Map<Long, Map<Long, Short>> subQueryTrackers = new HashMap<>();//query progress tracker

    private final List<Long> runningQueries = new ArrayList<>();

    private final List<Long> queuedQueries = new ArrayList<>();

    private final Map<Long, JsonObject> queuedQueryContexts = new HashMap<>();

    private final Map<Long, Integer> queryTickers = new HashMap<>();//query timer after time complete query not completed removing its context

    private final StringBuilder builder = new StringBuilder();

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_COLUMN_MAPPER_QUERY, "", reply ->
            {
                if (reply.succeeded())
                {
                    //invalid queries kindly remove it as response already received or query aborted
                    vertx.setPeriodic(30 * 1000L, periodicTimer ->
                    {
                        var iterator = queryTickers.entrySet().iterator();

                        while (iterator.hasNext())
                        {
                            var entry = iterator.next();

                            entry.setValue(entry.getValue() - 30);

                            if (entry.getValue() <= 0)
                            {
                                vertx.eventBus().send(EVENT_PUBLICATION_DATASTORE_READ, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                                        .put(EventBusConstants.EVENT_TOPIC, DATASTORE_QUERY_TOPIC)
                                        .put(EventBusConstants.EVENT_CONTEXT, Buffer.buffer().appendByte(DatastoreConstants.OperationType.QUERY_ABORT.getName()).appendBytes(new JsonObject().put(QUERY_ID, entry.getKey()).encode().getBytes()).getBytes()));

                                if (queryStatuses.containsKey(entry.getKey()))
                                {
                                    for (var subQueryId : queryStatuses.get(entry.getKey()).keySet())
                                    {
                                        Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(packError(String.format(ErrorMessageConstants.VISUALIZATION_QUERY_ABORTED, "Execution time limit reached"), entry.getKey(), subQueryId).getBytes()));

                                        Bootstrap.vertx().eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(packError(String.format(ErrorMessageConstants.VISUALIZATION_QUERY_ABORTED, "Execution time limit reached"), entry.getKey(), subQueryId).getBytes()));
                                    }
                                }

                                iterator.remove();
                            }
                        }

                        if (runningQueries.removeIf(query -> !queries.containsKey(query)) && !queuedQueries.isEmpty())
                        {
                            send();
                        }
                    });

                    columns.mergeIn(reply.result().body().getJsonObject(DatastoreConstants.METRIC_COLUMNS));

                    vertx.eventBus().<byte[]>localConsumer(EVENT_DATASTORE_QUERY_RESPONSE, message ->
                    {
                        try
                        {

                            var buffer = Buffer.buffer(CodecUtil.toBytes(message.body()));

                            var widgetId = 0L;

                            var queryId = buffer.getLongLE(0);

                            var subQueryId = buffer.getLongLE(8);

                            var subQueryCalculatedProgress = buffer.getUnsignedByte(16);

                            var subQueryProgress = buffer.getUnsignedByte(16);

                            var status = buffer.getUnsignedByte(33);

                            var validResult = true;

                            //there are certain times it happens due to ZMQ 50 percent progress is received after 100 so ignoring 50 percent result
                            if (queryStatuses.containsKey(queryId) && queryStatuses.get(queryId).containsKey(subQueryId))
                            {
                                subQueryTrackers.computeIfAbsent(queryId, value -> new HashMap<>()).computeIfAbsent(subQueryId, val -> subQueryProgress);

                                if (subQueryTrackers.get(queryId).get(subQueryId) <= subQueryProgress)
                                {
                                    subQueryTrackers.get(queryId).put(subQueryId, subQueryProgress);
                                }
                                else
                                {
                                    validResult = false;
                                }
                            }

                            if (validResult)
                            {
                                if (queryStatuses.containsKey(queryId) && queryStatuses.get(queryId).containsKey(subQueryId))
                                {
                                    var succeeded = true;

                                    short queryProgress = 0;

                                    subQueryTrackers.computeIfAbsent(queryId, value -> new HashMap<>()).put(subQueryId, subQueryCalculatedProgress);

                                    if (queryStatuses.get(queryId).size() > 1)
                                    {
                                        queryProgress = CommonUtil.getShort(subQueryTrackers.get(queryId).values().stream().mapToInt(CommonUtil::getInteger).sum() / queryStatuses.get(queryId).size());

                                        buffer.setUnsignedByte(16, queryProgress >= 100 ? 100 : queryProgress);

                                        queryTrackers.put(queryId, queryProgress);
                                    }

                                    else
                                    {
                                        queryTrackers.put(queryId, subQueryCalculatedProgress);
                                    }

                                    if (queries.containsKey(queryId))
                                    {
                                        var tokens = queries.get(queryId).split(SEPARATOR_WITH_ESCAPE);

                                        if (CommonUtil.getLong(tokens[0]) > 0)//other than preview request
                                        {
                                            widgetId = CommonUtil.getLong(tokens[0]);
                                        }
                                    }

                                    if (status == 0)//fail
                                    {
                                        var errorLength = 38 + buffer.getIntLE(34);

                                        var errorMessage = buffer.getString(38, errorLength);

                                        if (buffer.length() < errorLength + 1)//if after error length nothing is there then it contains only error so event failed no data is received
                                        {
                                            succeeded = false;
                                        }

                                        queryStatuses.get(queryId).put(subQueryId, errorMessage);

                                        vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(EventBusConstants.EVENT_TYPE, EVENT_VISUALIZATION).put(MESSAGE, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, widgetId > 0 ? WidgetConfigStore.getStore().getItem(widgetId).getString(VisualizationConstants.VISUALIZATION_NAME) : "Preview Widget", errorMessage)).put(STATUS, STATUS_FAIL));
                                    }
                                    else
                                    {
                                        queryStatuses.get(queryId).put(subQueryId, STATUS_SUCCEED);
                                    }

                                    EventBusConstants.updateEvent(queryId, String.format(InfoMessageConstants.EVENT_TRACKER_VISUALIZATION_RESPONSE_RECEIVED, subQueryId, status, subQueryCalculatedProgress, queryProgress, DateTimeUtil.timestamp()));

                                    if (subQueries.containsKey(subQueryId) || queryContexts.containsKey(queryId))
                                    {
                                        var context = subQueries.containsKey(subQueryId) ? subQueries.get(subQueryId) : queryContexts.get(queryId);

                                        if (context.containsKey(EVENT_TYPE))
                                        {
                                            var publish = queryTrackers.get(queryId) >= 100;

                                            if (context.containsKey(PUBLISH_SUB_QUERY_PROGRESS) && context.getBoolean(PUBLISH_SUB_QUERY_PROGRESS))
                                            {
                                                publish = true;
                                            }

                                            if (publish)//if require event to be published to other events will be publishing it
                                            {
                                                if (context.containsKey(VISUALIZATION_DECODE_RESPONSE) && context.getString(VISUALIZATION_DECODE_RESPONSE).equalsIgnoreCase(YES))
                                                {
                                                    vertx.eventBus().send(context.getString(EVENT_TYPE), context.put(QUERY_PROGRESS, queryTrackers.get(queryId)).put(RESULT, VisualizationConstants.unpack(buffer, LOGGER, queryContexts.get(queryId), queryContexts.get(queryId).getBoolean(DISCARD_DUMMY_ROWS, true))));
                                                }
                                                else
                                                {
                                                    vertx.eventBus().send(context.getString(EVENT_TYPE), new JsonObject().mergeIn(context).put(QUERY_CONTEXT, queryContexts.get(queryId)).put(QUERY_PROGRESS, queryTrackers.get(queryId)).put(RESULT, buffer.getBytes()));
                                                }
                                            }
                                        }
                                        else
                                        {
                                            //in some widgets like hardware sensor UI needs data only after its subquery progress is 100 otherwise its not able to merge multiple subqueries into single
                                            if (((queryContexts.get(queryId) != null && !queryContexts.get(queryId).containsKey(PUBLISH_SUB_QUERY_PROGRESS)) || subQueryProgress == 100))
                                            {
                                                vertx.eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(buffer.getBytes()));
                                            }
                                        }
                                    }

                                    if (subQueryProgress == 100)
                                    {
                                        subQueries.remove(subQueryId);
                                    }

                                    if (queryTrackers.get(queryId) >= 100)
                                    {
                                        runningQueries.remove(queryId);

                                        if (!queuedQueries.isEmpty())
                                        {
                                            send();
                                        }

                                        queryStatuses.get(queryId).forEach((key, value) ->
                                        {
                                            if (!value.equalsIgnoreCase(STATUS_SUCCEED))
                                            {
                                                builder.append(value).append(NEW_LINE);
                                            }
                                        });

                                        if (succeeded)
                                        {
                                            vertx.eventBus().send(EventBusConstants.EVENT_SUCCEED, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(ERROR, builder.toString()).put(EventBusConstants.EVENT_ID, queryId));
                                        }
                                        else
                                        {
                                            vertx.eventBus().send(EventBusConstants.EVENT_FAIL, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(EventBusConstants.EVENT_ID, queryId).put(ERROR, builder.toString()));
                                        }

                                        if (queryStatuses.get(queryId).size() == 1)
                                        {
                                            vertx.eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(buffer.getBytes()));
                                        }

                                        if (CommonUtil.traceEnabled())
                                        {
                                            //as of now dumping trace log will be having query explorer to track each and every query
                                            LOGGER.trace("Query Stats:" + new JsonObject().put("queued.background.queries", queuedQueries.size()).put("running.queries", queryStatuses.size()).put("running.sub.queries", subQueries.size()).put("running.background.queries", runningQueries.size())
                                                    .put("query.trackers", queryTrackers.size()).put("sub.query.trackers", subQueryTrackers.size()).put("queued.background.queries.context", queuedQueryContexts.size()).put("query.contexts", queryContexts.size())
                                                    .put("queries", queries.size()));
                                        }

                                        cleanUp(queryId);

                                        builder.setLength(0);
                                    }
                                }
                            }
                        }

                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }

                    });

                    vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE, message ->
                    {
                        try
                        {
                            var event = message.body();

                            var tokens = event.getString(DatastoreConstants.MAPPER).split(GlobalConstants.COLUMN_SEPARATOR, -1);

                            if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(EventBusConstants.ChangeNotificationType.UPDATE_METRIC_COLUMN.name()))
                            {
                                update(columns, tokens, true);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    }).exceptionHandler(LOGGER::error);

                    vertx.eventBus().<JsonObject>localConsumer(config().getString(EVENT_TYPE), message ->
                    {
                        var queryId = new AtomicLong();

                        try
                        {
                            var event = message.body();

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace("Visualization Metric Request received:" + event);
                            }

                            queryId.set(event.getLong(VisualizationConstants.QUERY_ID));

                            var user = UserConfigStore.getStore().getItemByValue(User.USER_NAME, event.getString(User.USER_NAME));

                            var filters = event.containsKey(FILTERS) && !event.getJsonObject(FILTERS).isEmpty() ? JsonObject.mapFrom(event.remove(FILTERS)) : new JsonObject();

                            var dataSources = new JsonArray().addAll((JsonArray) event.remove(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                            if (!dataSources.isEmpty())
                            {
                                var buffer = Buffer.buffer();

                                var visualizationDataSource = dataSources.getJsonObject(0);

                                var entities = new HashMap<String, Object>();

                                var plugins = new HashSet<>();

                                var error = EMPTY_VALUE;

                                var subQueryId = event.getLong(SUB_QUERY_ID);

                                queryContexts.put(queryId.get(), new JsonObject().mergeIn(event).put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, new JsonArray().add(visualizationDataSource)));

                                queryStatuses.computeIfAbsent(queryId.get(), value -> new HashMap<>()).put(subQueryId, EMPTY_VALUE);

                                if (visualizationDataSource.containsKey(VisualizationConstants.VISUALIZATION_TIMELINE))
                                {
                                    event.remove(VisualizationConstants.VISUALIZATION_TIMELINE);

                                    DateTimeUtil.buildTimeline(visualizationDataSource, event, user);

                                    event.put(VisualizationConstants.VISUALIZATION_TIMELINE, visualizationDataSource.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE));

                                    visualizationDataSource.remove(VisualizationConstants.VISUALIZATION_TIMELINE);
                                }

                                if (!filters.isEmpty())
                                {
                                    visualizationDataSource.put(FILTERS, filters);
                                }

                                VisualizationConstants.validateFilters(visualizationDataSource.getJsonObject(FILTERS));

                                switch (VisualizationConstants.VisualizationDataSource.valueOfName(visualizationDataSource.getString(VisualizationConstants.TYPE)))
                                {
                                    case NETROUTE_METRIC, NETROUTE_AVAILABILITY ->
                                    {
                                        var visualizationDataPoints = new JsonArray();

                                        var statuses = new HashSet<>();

                                        var dataPoints = visualizationDataSource.getJsonArray(DATA_POINTS);

                                        for (var i = 0; i < dataPoints.size(); i++)
                                        {
                                            if (visualizationDataPoints.size() < 14)
                                            {
                                                var visualizationDataPoint = dataPoints.getJsonObject(i);

                                                if (event.containsKey(ENTITY_TYPE))
                                                {
                                                    visualizationDataPoint.put(ENTITY_TYPE, event.getString(ENTITY_TYPE));

                                                    visualizationDataPoint.put(ENTITIES, event.getJsonArray(ENTITIES));
                                                }

                                                visualizationDataPoint.put(VisualizationConstants.FILTER_KEYS, new JsonArray());

                                                var pluginIds = new JsonArray();

                                                var column = visualizationDataPoint.getString(VisualizationConstants.DATA_POINT);

                                                if (column.equalsIgnoreCase(SEVERITY) || column.equalsIgnoreCase(STATUS))
                                                {
                                                    pluginIds.add(NETROUTE_METRIC.getName());
                                                }
                                                else if (columns.containsKey(column))
                                                {
                                                    pluginIds = columns.getJsonObject(column).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);
                                                }
                                                else if (column.equalsIgnoreCase(NetRouteConstants.NETROUTE_EVENT_COLUMN))
                                                {
                                                    pluginIds.add(NETROUTE_EVENT.getName());
                                                }

                                                LOGGER.info(String.format("plugin id : %s for data point : %s", plugins, column));

                                                if (!pluginIds.isEmpty())
                                                {
                                                    var qualifiedEntities = qualifyEntities(visualizationDataPoint, pluginIds);

                                                    if (CommonUtil.debugEnabled())
                                                    {
                                                        LOGGER.debug(String.format("qualified entities for visualization data point: %s", qualifiedEntities));
                                                    }

                                                    if (qualifiedEntities != null && !qualifiedEntities.isEmpty())
                                                    {
                                                        visualizationDataPoint.put(ENTITIES, qualifiedEntities.get(ENTITIES));

                                                        entities.putAll((Map<? extends String, ? extends Long>) qualifiedEntities.get(ENTITIES));

                                                        plugins.addAll(entities.values());

                                                        visualizationDataPoint.put(VisualizationConstants.PLUGINS, qualifiedEntities.remove(VisualizationConstants.PLUGINS));

                                                        visualizationDataPoints.add(visualizationDataPoint);
                                                    }

                                                    if ((visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.OBJECT_AVAILABILITY.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.CONFIG_METRIC.getName()))
                                                            && event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationCategory.GAUGE.getName()))//data will be fetched from in memory ObjectStatusCacheStore
                                                    {
                                                        var status = EMPTY_VALUE;

                                                        status = column.split("\\.")[1];

                                                        statuses.add(status);
                                                    }
                                                }
                                            }
                                        }

                                        if (!visualizationDataPoints.isEmpty())
                                        {
                                            visualizationDataSource.put(VisualizationConstants.DATA_POINTS, visualizationDataPoints);

                                            visualizationDataSource.put(ENTITIES, entities);

                                            visualizationDataSource.put(GROUP_BY, EMPTY_VALUE);

                                            visualizationDataSource.put(STATUS, new ArrayList<>(statuses));

                                            visualizationDataSource.put(VisualizationConstants.INSTANCE_TYPE, EMPTY_VALUE);

                                            if (visualizationDataSource.getString(TYPE).equalsIgnoreCase(VisualizationDataSource.CUMULATIVE_OBJECT_STATUS_FLAP.getName()))
                                            {
                                                applyFilter(true, event.put(OBJECT_FILTER, true), false, visualizationDataSource, null, null, entities.keySet());
                                            }
                                        }
                                        else
                                        {
                                            error = ErrorMessageConstants.NO_ENTITY_QUALIFIED;
                                        }
                                    }
                                }

                                if (error.isEmpty())
                                {
                                    visualizationDataSource.put(VisualizationConstants.PLUGINS, new ArrayList<>(plugins));

                                    var subQueryContext = new JsonObject().mergeIn(event).put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, new JsonObject().mergeIn(visualizationDataSource)).put(VisualizationConstants.ADMIN_ROLE, true).put(VisualizationConstants.QUERY_ID, queryId.get()).put(VisualizationConstants.SUB_QUERY_ID, subQueryId);

                                    setQueryParameters(event, subQueryContext, null, null);

                                    if (event.containsKey(EVENT_TYPE))
                                    {
                                        queryTickers.put(queryId.get(), INTERVAL_SECONDS);
                                    }

                                    buffer.appendByte(DatastoreConstants.OperationType.DATA_READ.getName()).appendBytes(subQueryContext.encode().getBytes());

                                    EventBusConstants.updateEvent(queryId.get(), String.format(InfoMessageConstants.EVENT_TRACKER_VISUALIZATION_REQUEST_DISPATCHED, subQueryId, DateTimeUtil.timestamp()));

                                    subQueries.put(subQueryId, new JsonObject().mergeIn(subQueryContext).put("cache", subQueryContext.getString(CONTAINER_TYPE, "dashboard").equalsIgnoreCase("Template") || subQueryContext.getString("drill.down.type", "no").equalsIgnoreCase("yes")).put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, new JsonArray().add(visualizationDataSource)));//ui requires same datasource as sent to identify in some custom widgets

                                    if (subQueryContext.getInteger(QUERY_PRIORITY) == QueryPriority.P0.getName())
                                    {
                                        vertx.eventBus().send(EVENT_PUBLICATION_DATASTORE_READ, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                                                .put(EventBusConstants.EVENT_TOPIC, DATASTORE_QUERY_TOPIC)
                                                .put(EventBusConstants.EVENT_CONTEXT, buffer.getBytes()));
                                    }

                                    else
                                    {
                                        send(queryId.get(), new JsonObject()
                                                .put(EventBusConstants.EVENT_TOPIC, DATASTORE_QUERY_TOPIC)
                                                .put(EventBusConstants.EVENT_CONTEXT, buffer.getBytes()));
                                    }
                                }
                                else
                                {
                                    VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), null, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getLong(ID) > 0 ? event.getString(VisualizationConstants.VISUALIZATION_NAME) : "Preview Widget", error), queryId.get(), subQueryId, LOGGER, EVENT_DATASTORE_QUERY_RESPONSE);

                                }
                            }
                            else
                            {
                                VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), null, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getLong(ID) > 0 ? event.getString(VisualizationConstants.VISUALIZATION_NAME) : "Preview Widget", ErrorMessageConstants.INVALID_DATA_SOURCE), queryId.get(), event.getLong(SUB_QUERY_ID), LOGGER, EVENT_VISUALIZATION_RESPONSE);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    });
                }
            });

             /*
                Following Local Consumer Purpose is to get acknowledgment from db and then to set the timer as per the ack in queryTicker.
             */
            Bootstrap.vertx().eventBus().<Long>localConsumer(EVENT_DATASTORE_ACKNOWLEDGEMENT, message ->
            {
                try
                {
                    if (message.body() != null)
                    {
                        var queryId = message.body();

                        if (queryTickers.containsKey(queryId))
                        {
                            queryTickers.put(queryId, INTERVAL_SECONDS);
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

            }).exceptionHandler(LOGGER::error);
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        promise.complete();
    }

    private Map<String, Object> qualifyEntities(JsonObject context, JsonArray pluginIds)
    {
        Map<String, Object> qualifiedEntities = null;

        try
        {
            Map<String, String> entities = null;

            var pluginEntities = new HashMap<String, Set<Long>>();

            var plugins = new HashSet<>();

            if (!context.containsKey(ENTITY_TYPE))
            {
                context.put(ENTITY_TYPE, "all");
            }

            var items = context.getJsonArray(ENTITIES);

            if (items != null && !items.isEmpty())
            {
                entities = new HashMap<>();

                if (pluginIds.contains(NETROUTE_STATUS_DURATION_METRIC.getName()))
                {
                    for (var item : items)
                    {
                        entities.put(CommonUtil.getString(item), NETROUTE_STATUS_DURATION_METRIC.getName() + DASH_SEPARATOR + NetRouteConstants.NETROUTE_AVAILABILITY_PLUGIN);

                        pluginEntities.computeIfAbsent(NETROUTE_STATUS_DURATION_METRIC.getName() + DASH_SEPARATOR + NetRouteConstants.NETROUTE_AVAILABILITY_PLUGIN, value -> new HashSet<>()).add(CommonUtil.getLong(item));
                    }

                    plugins.add(NETROUTE_STATUS_DURATION_METRIC.getName() + DASH_SEPARATOR + NetRouteConstants.NETROUTE_AVAILABILITY_PLUGIN);
                }
                else if (pluginIds.contains(NETROUTE_METRIC.getName()))
                {
                    for (var item : items)
                    {
                        entities.put(CommonUtil.getString(item), NETROUTE_METRIC.getName() + DASH_SEPARATOR + NetRouteConstants.NETROUTE_AVAILABILITY_PLUGIN);

                        pluginEntities.computeIfAbsent(NETROUTE_METRIC.getName() + DASH_SEPARATOR + NetRouteConstants.NETROUTE_AVAILABILITY_PLUGIN, value -> new HashSet<>()).add(CommonUtil.getLong(item));
                    }

                    plugins.add(NETROUTE_METRIC.getName() + DASH_SEPARATOR + NetRouteConstants.NETROUTE_AVAILABILITY_PLUGIN);
                }
                else if (pluginIds.contains(NETROUTE_EVENT.getName()))
                {
                    for (var item : items)
                    {
                        entities.put(CommonUtil.getString(item), NETROUTE_EVENT.getName() + DASH_SEPARATOR + NetRouteConstants.NETROUTE_AVAILABILITY_PLUGIN);

                        pluginEntities.computeIfAbsent(NETROUTE_EVENT.getName() + DASH_SEPARATOR + NetRouteConstants.NETROUTE_AVAILABILITY_PLUGIN, value -> new HashSet<>()).add(CommonUtil.getLong(item));
                    }

                    plugins.add(NETROUTE_EVENT.getName() + DASH_SEPARATOR + NetRouteConstants.NETROUTE_AVAILABILITY_PLUGIN);

                    var entityKeys = context.getString(ENTITY_KEYS);

                    context.put(ENTITY_KEYS, new JsonObject().put(entityKeys, NETROUTE_EVENT.getName() + DASH_SEPARATOR + NetRouteConstants.NETROUTE_AVAILABILITY_PLUGIN));
                }
            }

            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug(String.format("plugins : %s with context : %s ", plugins, context.encode()));
            }

            if (entities != null && !entities.isEmpty())
            {
                qualifiedEntities = new HashMap<>();

                qualifiedEntities.put(ENTITIES, entities);

                qualifiedEntities.put(VisualizationConstants.PLUGINS, new ArrayList<>(plugins));

                qualifiedEntities.put(VisualizationConstants.PLUGIN_ENTITIES, pluginEntities);
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return qualifiedEntities;
    }

    private void send()
    {
        var id = queuedQueries.removeFirst();

        send(id, queuedQueryContexts.remove(id));
    }

    private void send(long id, JsonObject event)
    {
        if (event != null)
        {
            if (runningQueries.size() == MotadataConfigUtil.getMetricQueryQueueSize())
            {
                queuedQueryContexts.put(id, event);

                queuedQueries.add(id);
            }

            else
            {
                runningQueries.add(id);

                vertx.eventBus().send(EVENT_PUBLICATION_DATASTORE_READ, event.put(EventBusConstants.EVENT_COPY_REQUIRED, false));
            }
        }
    }

    private void cleanUp(long queryId, long subQueryId)
    {
        try
        {
            //will be removing all subquerycontext
            if (queryStatuses.containsKey(queryId))
            {
                queryStatuses.get(queryId).remove(subQueryId);
            }

            subQueries.remove(subQueryId);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void cleanUp(long queryId)
    {
        try
        {
            queryTrackers.remove(queryId);

            if (queryStatuses.containsKey(queryId))
            {
                queryStatuses.remove(queryId).keySet().forEach(subQueries::remove);
            }

            queries.remove(queryId);

            subQueryTrackers.remove(queryId);

            queryContexts.remove(queryId);

            queryTickers.remove(queryId);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
