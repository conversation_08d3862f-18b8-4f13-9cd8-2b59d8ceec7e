/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			        Notes
 *  20-Feb-2025		Pruthviraj Jadeja		Initial commit
 */

package com.mindarray.netroute;

import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.NetRoute;
import com.mindarray.api.RunbookPlugin;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.runbook.Runbook;
import com.mindarray.store.NetRouteCacheStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.RunbookPluginConfigStore;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.nio.charset.StandardCharsets;
import java.util.*;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.NetRoute.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.netroute.NetRouteConstants.*;

/**
 * Processes responses from network route polling operations.
 * <p>
 * This class is responsible for handling the responses received from network route polling
 * operations. It processes the raw data, calculates metrics, builds the network topology,
 * and stores the results for further analysis and visualization.
 * <p>
 * Key responsibilities include:
 * <ul>
 *   <li>Processing raw network route polling responses</li>
 *   <li>Calculating latency and other metrics between network hops</li>
 *   <li>Building a hierarchical representation of the network topology</li>
 *   <li>Enriching the data with additional information</li>
 *   <li>Storing the processed data for analysis and visualization</li>
 *   <li>Sending events and notifications about network route status</li>
 * </ul>
 * <p>
 * The processor handles complex network path analysis, including handling of timeouts,
 * packet loss calculation, and link transit probability computation.
 */
public class NetRouteResponseProcessor extends AbstractVerticle
{
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(NetRouteResponseProcessor.class, GlobalConstants.MOTADATA_NETROUTE, "NetRoute Response Processor");

    /** Delivery options for event bus messages */
    private static final DeliveryOptions DELIVERY_OPTIONS = new DeliveryOptions();

    /** Set of metric names to extract from network route polling results */
    private static final Set<String> METRICS = Set.of(NetRouteMetrics.SENT.getName(), NetRouteMetrics.RECEIVED.getName(), NETROUTE_HOP_CONNECTED_LINK, SEVERITIES);

    /** TreeSet to store and sort severity levels */
    private final TreeSet<Severity> severities = new TreeSet<>();

    /**
     * Initializes the NetRouteResponseProcessor verticle.
     * <p>
     * This method sets up an event bus consumer to listen for network route polling responses.
     * When a response is received, it processes the data, calculates metrics, and stores the
     * results for further analysis and visualization.
     *
     * @param promise Promise to be completed when initialization is done
     */
    @Override
    public void start(Promise<Void> promise)
    {
        // Set up an event bus consumer to listen for network route polling responses
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_NETROUTE_POLL_RESPONSE, message ->
        {
            var event = message.body();

            try
            {
                // Log debug information if enabled
                if (CommonUtil.debugEnabled())
                {
                    LOGGER.debug(String.format("NetRoute %s result received at %s with status : %s ", event.getString(NETROUTE_NAME), event.getLong(EVENT_TIMESTAMP), event.getString(STATUS)));
                }

                // Process successful responses that contain result data
                if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED) && event.containsKey(RESULT))
                {
                    var id = event.getLong(ID);

                    // Update the timestamp of the last successful poll
                    NetRouteCacheStore.getStore().updateMetricPollTimestamp(id, event.getLong(EVENT_TIMESTAMP));

                    var result = event.getJsonObject(RESULT);

                    // Log result status if debug is enabled
                    if (CommonUtil.debugEnabled())
                    {
                        LOGGER.info(String.format("NetRoute %s result status : %s ", event.getString(NETROUTE_NAME), result.getString(STATUS)));
                    }

                    // Update the network route status in the cache store
                    NetRouteCacheStore.getStore().updateItem(id, result.getString(STATUS, STATUS_UNKNOWN), event.getLong(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()));

                    // Create a response object with the source-to-destination result
                    var response = new JsonObject().put(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName(), new JsonArray().add(result));

                    // Processing the NetRoute response.
                    // Since we probe multiple times, the result contains an array of hop lists,
                    // where each hop has metrics such as latency (best, worst, avg), packet loss, NAT, MPLS, etc.
                    // we have to calculate metrics for the 'links' between consecutive hops, rather than individual hops.
                    // For example, if the hops are (A, B, C, D), we compute and store metrics for the links (A → B), (B → C), and (C → D).
                    // Latency for a link is computed as: `Latency(B) - Latency(A)`.    If a node times out, its latency and packet loss are considered as 0.
                    // Also overlapping links from multiple results are merged to avoid duplicates link and required for policy inspection.

                    var items = result.getJsonArray(NetRouteConstants.NETROUTE_ROUTES);

                    result.remove(NetRouteConstants.NETROUTE_ROUTES);

                    if (items != null && !items.isEmpty())
                    {
                        var source = ObjectConfigStore.getStore().getItemByAgentId(event.getLong(NETROUTE_SOURCE)).getString(AIOpsObject.OBJECT_IP);

                        var target = event.getJsonArray(NETROUTE_DESTINATION_IP).getString(0);

                        var objects = new HashSet<String>();

                        var connections = new HashMap<String, JsonObject>();                                         // for overlapped links. (unique link -> metric)

                        var links = new JsonArray();

                        var hopInfo = NetRouteCacheStore.getStore().getHopRegistryInfo(id);

                        for (var index = 0; index < items.size(); index++)
                        {
                            var hops = items.getJsonObject(index).getJsonArray(NetRouteMetrics.HOPS.getName());

                            if (hops != null)
                            {
                                var sourceHop = new JsonObject().put(NETROUTE_SOURCE, source);

                                for (var hopIndex = 0; hopIndex < hops.size(); hopIndex++)
                                {
                                    try
                                    {
                                        var destinationHop = hops.getJsonObject(hopIndex);

                                        var hosts = !destinationHop.getJsonArray(NetRouteMetrics.HOSTS.getName()).isEmpty() ? destinationHop.getJsonArray(NetRouteMetrics.HOSTS.getName()).getJsonObject(0) : null;

                                        var destinationIP = hosts != null ? hosts.getString(NetRouteMetrics.IP.getName()) : NETROUTE_TIMEOUT_HOP + DASH_SEPARATOR + destinationHop.getInteger(NetRouteMetrics.TTL.getName());

                                        var unreachable = destinationIP.contains(NETROUTE_TIMEOUT_HOP);

                                        var sourceIP = sourceHop.getString(NETROUTE_SOURCE);

                                        // first hop which will be our motadata machine, so just create the link , no need to calculate latency
                                        if (!sourceIP.equalsIgnoreCase(source))
                                        {
                                            if (!unreachable)
                                            {
                                                calculateLatency(destinationHop, sourceHop);
                                            }
                                            else
                                            {
                                                // if last node which is actual destination, is timeout node, then we'll update the last link and metric based on the over all (Source-to-destination) result
                                                // this might happen if destination server is not responding to UDP packet.
                                                if (hopIndex == hops.size() - 1)
                                                {
                                                    destinationHop.put(NetRouteMetrics.BEST.getName(), CommonUtil.getFloat(result.getFloat(NetRouteConstants.NETROUTE_MIN_LATENCY)))
                                                            .put(NetRouteMetrics.WORST.getName(), CommonUtil.getFloat(result.getFloat(NetRouteConstants.NETROUTE_MAX_LATENCY))).put(NetRouteMetrics.AVG.getName(), CommonUtil.getFloat(result.getFloat(NetRouteConstants.NETROUTE_LATENCY)))
                                                            .put(NetRouteMetrics.LOST_PACKET_PERCENT.getName(), CommonUtil.getFloat(result.getFloat(NetRouteConstants.NETROUTE_PACKET_LOST_PERCENT))).put(NetRouteMetrics.SENT.getName(), 1).put(NetRouteMetrics.RECEIVED.getName(), 1)
                                                            .put(NetRouteMetrics.HOSTS.getName(), new JsonArray().add(new JsonObject().put(NetRouteMetrics.IP.getName(), target).put(NetRouteMetrics.HOSTNAME.getName(), target)));

                                                    destinationIP = target;

                                                    unreachable = false;

                                                    calculateLatency(destinationHop, sourceHop);
                                                }
                                                else
                                                {
                                                    destinationHop.put(NetRouteMetrics.SENT.getName(), 1).put(NetRouteMetrics.RECEIVED.getName(), 1);
                                                }
                                            }
                                        }

                                        var connection = sourceIP + VALUE_SEPARATOR + destinationIP + VALUE_SEPARATOR + destinationHop.getInteger(NetRouteMetrics.TTL.getName());

                                        var link = connections.getOrDefault(connection, new JsonObject());

                                        if (!connections.containsKey(connection))
                                        {
                                            links.add(link);
                                        }

                                        enrich(link, destinationHop);

                                        link.put(NETROUTE_SOURCE, sourceIP).put(NetRoute.NETROUTE_DESTINATION, unreachable ? NETROUTE_TIMEOUT_HOP : hosts != null ? hosts.getString(NetRouteMetrics.HOSTNAME.getName()) : destinationIP)
                                                .put(NETROUTE_DESTINATION_IP, destinationIP).put(NetRouteConstants.NETROUTE_HOP_CONNECTED_LINK, connection)
                                                .put(NETROUTE_HOP_TTL, destinationHop.getInteger(NetRouteMetrics.TTL.getName()));

                                        connections.put(connection, link);

                                        // for runbook
                                        if (!hopInfo.containsKey(destinationIP) && !destinationIP.contains(NETROUTE_TIMEOUT_HOP))
                                        {
                                            objects.add(destinationIP);
                                        }

                                        // current destination.hop will become source , in next iteration
                                        sourceHop = destinationHop;

                                        sourceHop.put(NETROUTE_SOURCE, destinationIP);
                                    }
                                    catch (Exception exception)
                                    {
                                        LOGGER.error(exception);
                                    }
                                }
                            }
                        }

                        if (!objects.isEmpty())
                        {
                            event.put(NMSConstants.OBJECTS, new JsonArray(new ArrayList(objects)));
                        }

                        response.put(NetRouteConstants.NetRouteType.HOP_BY_HOP.getName(), links);

                        LOGGER.info(String.format("total unique links : %s ", connections.size()));
                    }

                    event.put(RESULT, response);

                    vertx.eventBus().send(EVENT_NETROUTE_POLICY, event.put(NetRoute.NETROUTE_ID, id));

                    lookup(event);

                    // for test case
                    if (MotadataConfigUtil.devMode())
                    {
                        vertx.eventBus().send(EventBusConstants.EVENT_NETROUTE_POLL_RESPONSE + ".test", event);
                    }
                }
                else
                {
                    LOGGER.warn(String.format("Failed to process netroute response : %s", event.getJsonArray(ERRORS)));
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                LOGGER.warn(String.format("Failed to process netroute response for : %s", event.getString(NETROUTE_NAME)));
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_NETROUTE_POLICY_RESPONSE, message ->
        {
            var event = message.body();

            try
            {
                LOGGER.info(String.format("NetRoute %s policy result received at %s ", event.getString(NETROUTE_NAME), event.getLong(EVENT_TIMESTAMP)));

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("NetRoute policy response received: %s", event.encode()));
                }

                var result = event.getJsonObject(RESULT);

                var object = result.getJsonArray(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName()).getJsonObject(0);

                severities.clear();

                if (object.containsKey(SEVERITIES) && !object.getJsonArray(SEVERITIES).isEmpty())
                {
                    severities.addAll(object.getJsonArray(SEVERITIES).getList());

                    object.put(SEVERITY, severities.last().ordinal());                                               // store the highest severity
                }
                else
                {
                    object.put(SEVERITY, Severity.UNKNOWN.ordinal());
                }

                object.put(STATUS, StatusType.valueOf(object.getString(STATUS).toUpperCase(Locale.ROOT)).ordinal());

                object.remove(SEVERITIES);

                // send to db
                send(object.put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP)).put(NETROUTE_ID, event.getLong(NETROUTE_ID)), true);

                var source = ObjectConfigStore.getStore().getItemByAgentId(event.getLong(NETROUTE_SOURCE)).getString(AIOpsObject.OBJECT_IP);

                if (result.getJsonArray(NetRouteConstants.NetRouteType.HOP_BY_HOP.getName()) != null)
                {
                    // build a recursive result from raw entries to store in DB

                    var hops = result.getJsonArray(NetRouteConstants.NetRouteType.HOP_BY_HOP.getName());

                    var connections = new HashMap<Integer, Map<String, JsonArray>>();        // ttl -> (source -> [destinations])

                    for (var index = 0; index < hops.size(); index++)
                    {
                        var hop = hops.getJsonObject(index);

                        var ttl = hop.getInteger(NETROUTE_HOP_TTL);          // ttl can be considered as level

                        connections.computeIfAbsent(ttl, value -> new HashMap<>());

                        connections.get(ttl).computeIfAbsent(hop.getString(NETROUTE_SOURCE), value -> new JsonArray()).add(hop);
                    }

                    hops.clear();

                    buildHierarchy(connections, hops, 1, source);

                    result.remove(NetRouteConstants.NetRouteType.HOP_BY_HOP.getName());

                    send(new JsonObject().put(NETROUTE_ID, event.getLong(NETROUTE_ID)).put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP))
                            .put(NETROUTE_EVENT_COLUMN, new JsonObject().put(CommonUtil.getString(NetRouteOrdinal.NETROUTE_HOPS.ordinal()), hops).encode()), false);
                }
                else
                {
                    object.put(CommonUtil.getString(NetRouteOrdinal.NETROUTE_SOURCE.ordinal()), source)
                            .put(CommonUtil.getString(NetRouteOrdinal.NETROUTE_DESTINATION.ordinal()), event.getString(NETROUTE_DESTINATION))
                            .put(CommonUtil.getString(NetRouteOrdinal.NETROUTE_DESTINATION_IP.ordinal()), event.getString(NETROUTE_DESTINATION))
                            .put(CommonUtil.getString(NetRouteOrdinal.NETROUTE_HOP_TTL.ordinal()), 1)
                            .put(SEVERITY, Severity.values()[object.getInteger(SEVERITY)]);

                    transform(object);

                    send(new JsonObject().put(NETROUTE_ID, event.getLong(NETROUTE_ID)).put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP))
                            .put(NETROUTE_EVENT_COLUMN, new JsonObject().put(CommonUtil.getString(NetRouteOrdinal.NETROUTE_HOPS.ordinal()), new JsonArray().add(object)).encode()), false);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                LOGGER.warn(String.format("Failed to process netroute policy response: %s", event.encode()));
            }
        });

        promise.complete();
    }

    /**
     * Calculates the latency metrics between two network hops.
     * <p>
     * This method computes three latency metrics between a source hop and a destination hop:
     * <ul>
     *   <li>Minimum latency (best case) - Using the best latency values from both hops</li>
     *   <li>Maximum latency (worst case) - Using the worst latency values from both hops</li>
     *   <li>Average latency - Using the average latency values from both hops</li>
     * </ul>
     * For each metric, the source hop's value is subtracted from the destination hop's value.
     * If any calculated latency is negative (which can happen due to measurement errors or
     * network anomalies), it is set to zero using Math.max().
     *
     * @param destinationHop The destination hop JsonObject
     * @param sourceHop The source hop JsonObject
     */
    private void calculateLatency(JsonObject destinationHop, JsonObject sourceHop)
    {
        // Calculate minimum latency (best case)
        var latency = CommonUtil.getFloat(destinationHop.getString(NetRouteMetrics.BEST.getName())) - CommonUtil.getFloat(sourceHop.getString(NetRouteMetrics.BEST.getName()));

        // Store minimum latency, ensuring it's not negative
        destinationHop.put(NetRouteConstants.NETROUTE_MIN_LATENCY, Math.max(latency, 0));

        // Calculate maximum latency (worst case)
        latency = CommonUtil.getFloat(destinationHop.getString(NetRouteMetrics.WORST.getName())) - CommonUtil.getFloat(sourceHop.getString(NetRouteMetrics.WORST.getName()));

        // Store maximum latency, ensuring it's not negative
        destinationHop.put(NETROUTE_MAX_LATENCY, Math.max(latency, 0));

        // Calculate average latency
        latency = CommonUtil.getFloat(destinationHop.getString(NetRouteMetrics.AVG.getName())) - CommonUtil.getFloat(sourceHop.getString(NetRouteMetrics.AVG.getName()));

        // Store average latency, ensuring it's not negative
        destinationHop.put(NETROUTE_LATENCY, Math.max(latency, 0));
    }

    /**
     * Performs WHOIS lookups for newly discovered network hops.
     * <p>
     * This method runs a WHOIS runbook for all newly discovered network hops found in the current
     * polling result. It fetches additional information about each hop, such as:
     * <ul>
     *   <li>Email addresses associated with the network</li>
     *   <li>Phone numbers for the network operator</li>
     *   <li>Autonomous System Number (ASN)</li>
     *   <li>Organization name</li>
     *   <li>Other network registration details</li>
     * </ul>
     * This information enriches the network route data and provides valuable context for
     * network path analysis and troubleshooting.
     *
     * @param event The event containing network route polling results
     */
    private void lookup(JsonObject event)
    {
        try
        {
            if (event.containsKey(NMSConstants.OBJECTS) && !event.getJsonArray(NMSConstants.OBJECTS).isEmpty())
            {
                var context = new JsonObject().mergeIn(event);

                context.remove(RESULT);

                var runbook = RunbookPluginConfigStore.getStore().getItem(Runbook.RunbookPluginId.WHOIS_LOOKUP.getName());

                if (runbook != null)
                {
                    runbook.mergeIn(runbook.getJsonObject(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT));

                    runbook.remove(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT);

                    context.put(EventBusConstants.EVENT_REPLY, YES).put(EVENT_PLUGIN_ENGINE, PluginEngineConstants.PluginEngine.PYTHON.getName()).put(REQUEST_TIMEOUT, runbook.getInteger(TIMEOUT)).put(TIMEOUT, runbook.getInteger(TIMEOUT))
                            .put(ID, runbook.getLong(ID)).put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray(new ArrayList<Long>(1)).add(ObjectConfigStore.getStore().getItemByAgentId(context.getLong(NETROUTE_SOURCE)).getLong(ID)))
                            .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_RUNBOOK);

                    LOGGER.info(String.format("triggering runbook : %s for netroute : %s with objects : %s ", runbook.getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME), context.getString(NETROUTE_NAME), context.getJsonArray(NMSConstants.OBJECTS)));

                    vertx.eventBus().<JsonObject>request(EVENT_RUNBOOK, context, DELIVERY_OPTIONS.setSendTimeout(runbook.getInteger(TIMEOUT) * 1000), reply ->
                    {
                        try
                        {
                            if (reply.succeeded())
                            {
                                var result = reply.result().body().getJsonArray(EVENT_REPLY_CONTEXTS).getJsonObject(0);

                                if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                                {
                                    LOGGER.info(String.format("runbook : %s for for netroute : %s ran successfully...", runbook.getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME), context.getString(NETROUTE_NAME)));

                                    var objects = result.getJsonArray(NMSConstants.OBJECTS);

                                    var item = NetRouteCacheStore.getStore().getHopRegistryInfo(context.getLong(NetRoute.NETROUTE_ID));

                                    for (var index = 0; index < objects.size(); index++)
                                    {
                                        var object = objects.getJsonObject(index);

                                        item.put(object.getString(AIOpsObject.OBJECT_IP), object);
                                    }

                                    // update the cache from current result. so from next time , runbook will only run for the new hops if found
                                    NetRouteCacheStore.getStore().updateHopRegistryInfo(context.getLong(NetRoute.NETROUTE_ID), item);
                                }
                                else
                                {
                                    LOGGER.warn(String.format("Failed to run runbook : %s for netroute : %s ", result.getString(MESSAGE), context.getString(NETROUTE_NAME)));
                                }
                            }
                            else
                            {
                                LOGGER.warn(String.format("Failed to run runbook : %s ", reply.cause().getMessage()));

                                LOGGER.error(reply.cause());
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    });
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void buildHierarchy(Map<Integer, Map<String, JsonArray>> connections, JsonArray hops, int ttl, String source)
    {
        if (connections.containsKey(ttl) && connections.get(ttl).containsKey(source))
        {
            var destinations = connections.get(ttl).get(source);

            for (var index = 0; index < destinations.size(); index++)
            {
                var destination = destinations.getJsonObject(index);

                if (destination.containsKey(SEVERITIES) && !destination.getJsonArray(SEVERITIES).isEmpty())
                {
                    severities.clear();

                    severities.addAll(destination.getJsonArray(SEVERITIES).getList());                                                  // store the highest severity

                    destination.put(SEVERITY, severities.last().name());
                }
                else
                {
                    destination.put(SEVERITY, destination.getString(CommonUtil.getString(NetRouteOrdinal.NETROUTE_SEVERITY.ordinal()), Severity.UNKNOWN.name()));
                }

                METRICS.forEach(destination::remove);

                destination.put(NETROUTE_ROUTES, new JsonArray());

                transform(destination);

                hops.add(destination);

                buildHierarchy(connections, destination.getJsonArray(CommonUtil.getString(NetRouteOrdinal.NETROUTE_HOPS.ordinal())), ttl + 1, destination.getString(CommonUtil.getString(NetRouteOrdinal.NETROUTE_DESTINATION_IP.ordinal())));
            }
        }
    }

    // convert normal keys to ordinal
    private void transform(JsonObject destination)
    {
        for (var key : NetRouteOrdinal.values())
        {
            if (destination.containsKey(key.getName()))
            {
                destination.put(CommonUtil.getString(key.ordinal()), destination.getValue(key.getName()));

                destination.remove(key.getName());
            }
        }
    }

    // This method will update/calculate the latency(best,worst,avg) and packet loss for the overlapped link
    private void enrich(JsonObject link, JsonObject hop)
    {
        try
        {
            var minLatency = hop.containsKey(NetRouteConstants.NETROUTE_MIN_LATENCY) ? CommonUtil.getFloat(hop.getString(NetRouteConstants.NETROUTE_MIN_LATENCY)) : CommonUtil.getFloat(hop.getString(NetRouteMetrics.BEST.getName()));

            var maxLatency = hop.containsKey(NetRouteConstants.NETROUTE_MAX_LATENCY) ? CommonUtil.getFloat(hop.getString(NetRouteConstants.NETROUTE_MAX_LATENCY)) : CommonUtil.getFloat(hop.getString(NetRouteMetrics.WORST.getName()));

            link.put(NetRouteConstants.NETROUTE_MIN_LATENCY, link.containsKey(NetRouteConstants.NETROUTE_MIN_LATENCY) ? Math.min(link.getFloat(NetRouteConstants.NETROUTE_MIN_LATENCY), minLatency) : minLatency);

            link.put(NetRouteConstants.NETROUTE_MAX_LATENCY, link.containsKey(NetRouteConstants.NETROUTE_MAX_LATENCY) ? Math.max(link.getFloat(NetRouteConstants.NETROUTE_MAX_LATENCY), maxLatency) : maxLatency);

            link.put(NetRouteConstants.NETROUTE_LATENCY, CommonUtil.getFloat((link.getFloat(NetRouteConstants.NETROUTE_MIN_LATENCY) + link.getFloat(NETROUTE_MAX_LATENCY)) / 2));

            link.put(NetRouteMetrics.SENT.getName(), link.getInteger(NetRouteMetrics.SENT.getName(), 0) + CommonUtil.getInteger(hop.getString(NetRouteMetrics.SENT.getName())));

            link.put(NetRouteMetrics.RECEIVED.getName(), link.getInteger(NetRouteMetrics.RECEIVED.getName(), 0) + CommonUtil.getInteger(hop.getString(NetRouteMetrics.RECEIVED.getName())));

            link.put(NetRouteConstants.NETROUTE_PACKET_LOST_PERCENT, 100 - (CommonUtil.getFloat(link.getFloat(NetRouteMetrics.RECEIVED.getName()) / link.getFloat(NetRouteMetrics.SENT.getName())) * 100));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void updateBuffer(String column, String value, Buffer buffer)
    {
        try
        {
            var category = DatastoreConstants.getDataCategory(true, column, value);

            buffer.appendByte(category);

            var bytes = column.getBytes(StandardCharsets.UTF_8);

            buffer.appendIntLE(bytes.length).appendBytes(bytes);

            if (category == DatastoreConstants.DataCategory.FLOAT.getName())
            {
                ByteUtil.writeDouble(buffer, CommonUtil.getDouble(value));
            }
            else if (category == DatastoreConstants.DataCategory.NUMERIC.getName())
            {
                buffer.appendLongLE(Math.round(CommonUtil.getDouble(value)));
            }
            else
            {
                bytes = value.getBytes(StandardCharsets.UTF_8);

                buffer.appendIntLE(bytes.length).appendBytes(bytes);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void send(JsonObject event, boolean isMetric)
    {
        var buffer = Buffer.buffer("UTF-8");

        buffer.setLongLE(0, CommonUtil.getLong(event.remove(EVENT_TIMESTAMP)));

        var plugin = DatastoreConstants.PluginId.NETROUTE_METRIC.getName() + DASH_SEPARATOR + NETROUTE_AVAILABILITY_PLUGIN;

        if (!isMetric)
        {
            plugin = DatastoreConstants.PluginId.NETROUTE_EVENT.getName() + DASH_SEPARATOR + NETROUTE_AVAILABILITY_PLUGIN;
        }

        if (CommonUtil.debugEnabled())
        {
            LOGGER.debug(String.format("event send to db with plugin : %s  for netroute id : %s ", plugin, event.getLong(NETROUTE_ID)));
        }

        var bytes = plugin.getBytes(StandardCharsets.UTF_8);

        buffer.appendIntLE(bytes.length);

        buffer.appendBytes(bytes);

        buffer.appendByte(DatastoreConstants.DatastoreFormat.VERTICAL.getName().byteValue());

        buffer.appendByte(CommonUtil.getByteValue(DatastoreConstants.DatastoreType.NETROUTE_METRIC.ordinal()));

        buffer.appendLongLE(CommonUtil.getLong(event.remove(NETROUTE_ID)));

        buffer.appendIntLE(EMPTY_VALUE.length()).appendString(EMPTY_VALUE);

        event.getMap().forEach((key, value) ->
        {
            if (value != null)
            {
                updateBuffer(key, CommonUtil.getString(value), buffer);
            }
        });

        vertx.eventBus().send(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.METRIC.getName(), buffer.getBytes());
    }

    @Override
    public void stop(Promise<Void> promise)
    {
        promise.complete();
    }
}
