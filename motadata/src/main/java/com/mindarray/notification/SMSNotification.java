/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.notification;

import com.mindarray.*;
import com.mindarray.api.ProxyServer;
import com.mindarray.api.SMSGatewayConfiguration;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.SMSGatewayConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.WebClientUtil;
import io.vertx.circuitbreaker.CircuitBreaker;
import io.vertx.circuitbreaker.CircuitBreakerOptions;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;
import org.apache.http.HttpStatus;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;

import static com.mindarray.GlobalConstants.*;

/**
 * Implementation of the Notification abstract class for sending SMS notifications.
 * <p>
 * This class handles SMS notifications by sending HTTP requests to a configured
 * SMS gateway service. It supports:
 * <ul>
 *   <li>Sending SMS messages to multiple recipients in parallel</li>
 *   <li>Circuit breaker pattern to handle SMS service failures</li>
 *   <li>Proxy server configuration for SMS gateway access</li>
 *   <li>URL templating for different SMS gateway providers</li>
 *   <li>Configurable timeout for SMS gateway requests</li>
 * </ul>
 * <p>
 * The notification process:
 * <ol>
 *   <li>Retrieves SMS gateway configuration from the configuration store</li>
 *   <li>Validates that the gateway URL and recipients are specified</li>
 *   <li>Sends HTTP requests to the SMS gateway for each recipient</li>
 *   <li>Tracks successful deliveries and reports results</li>
 * </ol>
 * <p>
 * The notification event should contain:
 * <ul>
 *   <li>{@link Notification#SMS_NOTIFICATION_RECIPIENTS}: JsonArray of recipient phone numbers</li>
 *   <li>{@link Notification#SMS_NOTIFICATION_MESSAGE}: The text message to send</li>
 *   <li>{@link GlobalConstants#TIMEOUT} (optional): Timeout in seconds for the HTTP request</li>
 * </ul>
 * <p>
 * This implementation uses a circuit breaker to prevent cascading failures when
 * the SMS gateway service is experiencing issues.
 */
public class SMSNotification extends Notification
{
    /**
     * Logger for SMS notification operations
     */
    private static final Logger LOGGER = new Logger(SMSNotification.class, GlobalConstants.MOTADATA_NOTIFICATION, "SMS Notification");

    /**
     * Circuit breaker to handle SMS service failures.
     * <p>
     * The circuit breaker:
     * <ul>
     *   <li>Opens after 5 consecutive failures</li>
     *   <li>Considers a request failed if it takes more than 30 seconds</li>
     *   <li>Stays open for 60 seconds before attempting to reset</li>
     *   <li>Calls the fallback handler when open</li>
     * </ul>
     */
    private final CircuitBreaker circuitBreaker = CircuitBreaker.create("sms-service-circuit-breaker", Bootstrap.vertx(),
            new CircuitBreakerOptions()
                    .setMaxFailures(5) // number of failure before opening the circuit
                    .setTimeout(30000) // consider a failure if the operation does not succeed in time
                    .setFallbackOnFailure(true) // do we call the fallback on failure
                    .setResetTimeout(60000) // time spent in open state before attempting to re-try
    ).openHandler(handler ->
    {
        // When circuit opens, notify the UI and log the service failure
        EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_SERVICE_HEALTH, new JsonObject().put(STATUS, STATUS_FAIL)
                .put(MESSAGE, ErrorMessageConstants.SMS_SERVICE_FAILED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SMS_SERVICE_DOWN));

        LOGGER.warn(ErrorMessageConstants.SMS_SERVICE_FAILED);

        // Send a notification to users about the service failure
        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().put(STATUS, STATUS_FAIL)
                .put(EventBusConstants.EVENT_TYPE, NotificationType.SMS.getName())
                .put(MESSAGE, ErrorMessageConstants.SMS_SERVICE_FAILED));

    }).closeHandler(handler ->
    {
        // When circuit closes, notify the UI and log the service restoration
        EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_SERVICE_HEALTH, new JsonObject().put(STATUS, STATUS_SUCCEED)
                .put(MESSAGE, InfoMessageConstants.SMS_SERVICE_RESTORED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS));

        LOGGER.info(InfoMessageConstants.SMS_SERVICE_RESTORED);

        // Send a notification to users about the service restoration
        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().put(STATUS, STATUS_SUCCEED)
                .put(EventBusConstants.EVENT_TYPE, NotificationType.SMS.getName())
                .put(MESSAGE, InfoMessageConstants.SMS_SERVICE_RESTORED));

    }).fallback(handler -> handler.initCause(new Exception(ErrorMessageConstants.SMS_SERVICE_FAILED)));

    /**
     * Default constructor for SMSNotification.
     */
    public SMSNotification()
    {
        // No initialization needed
    }

    /**
     * Processes and sends SMS notifications based on the provided event data.
     * <p>
     * This method:
     * <ul>
     *   <li>Retrieves SMS gateway configuration</li>
     *   <li>Validates that the gateway URL and recipients are specified</li>
     *   <li>Sends HTTP requests to the SMS gateway for each recipient</li>
     *   <li>Tracks successful deliveries and reports results</li>
     * </ul>
     * <p>
     * The method uses a circuit breaker to prevent cascading failures when
     * the SMS gateway service is experiencing issues.
     *
     * @param event A JsonObject containing the SMS notification parameters
     *              (recipients, message, timeout, etc.)
     * @return A Promise that resolves to a JsonObject containing the result of the
     *         SMS notification attempt, including success/failure status and any error details
     */
    @Override
    public Promise<JsonObject> notify(JsonObject event)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            // Retrieve SMS gateway configuration from the store
            var item = SMSGatewayConfigStore.getStore().getItem();

            // Get the gateway URL from the configuration
            var gatewayURL = item.getString(SMSGatewayConfiguration.SMS_SERVER_GATEWAY_URL);

            // Execute the SMS sending logic with circuit breaker protection
            circuitBreaker.<JsonObject>execute(future ->
            {
                // Validate that the gateway URL and recipients are specified
                if (CommonUtil.isNotNullOrEmpty(gatewayURL) && event.getJsonArray(Notification.SMS_NOTIFICATION_RECIPIENTS) != null)
                {
                    // Create collections to track futures and successful recipients
                    var futures = new ArrayList<Future<Object>>();
                    var recipients = new ArrayList<String>();

                    // Process each recipient in parallel
                    for (var recipient : event.getJsonArray(Notification.SMS_NOTIFICATION_RECIPIENTS))
                    {
                        var result = Promise.promise();
                        futures.add(result.future());

                        // Determine whether to use a proxy for the HTTP request
                        var webClient = item.getValue(ProxyServer.PROXY_ENABLED) != null &&
                                item.getString(ProxyServer.PROXY_ENABLED).equalsIgnoreCase(YES) &&
                                WebClientUtil.getProxyOptions() != null
                                ? WebClientUtil.getProxyWebClient()
                                : WebClientUtil.getWebClient();

                        // Create the SMS gateway URL with recipient and message parameters
                        webClient.getAbs(gatewayURL
                                        .replace("$$number$$", URLEncoder.encode(recipient.toString(), StandardCharsets.UTF_8))
                                        .replace("$$message$$", URLEncoder.encode(event.getString(Notification.SMS_NOTIFICATION_MESSAGE), StandardCharsets.UTF_8)))
                                // Set timeout (use provided timeout or default to 30 seconds)
                                .timeout(event.containsKey(TIMEOUT) ? event.getLong(TIMEOUT) * 1000L : 30 * 1000L)
                                // Send the HTTP request
                                .send(asyncResult ->
                                {
                                    // Handle the HTTP response
                                    if (asyncResult.succeeded())
                                    {
                                        // If the HTTP request was successful but returned a non-200 status code
                                        if (asyncResult.result().statusCode() != HttpStatus.SC_OK)
                                        {
                                            LOGGER.warn(asyncResult.result().statusMessage() + ", " + asyncResult.result().bodyAsJsonObject().encode());
                                        }
                                        // If the HTTP request was successful with 200 OK status
                                        else if (asyncResult.result().statusCode() == HttpStatus.SC_OK)
                                        {
                                            // Add the recipient to the list of successful deliveries
                                            recipients.add(recipient.toString());
                                        }

                                        // Mark this recipient's request as complete
                                        result.complete();
                                    }
                                    // If the HTTP request failed (connection error, timeout, etc.)
                                    else
                                    {
                                        // Log the error
                                        LOGGER.error(asyncResult.cause());

                                        // Mark this recipient's request as failed
                                        result.fail(asyncResult.cause());
                                    }
                                });
                    }

                    // Wait for any of the futures to complete (success or failure)
                    Future.any(futures).onComplete(result ->
                    {
                        // If at least one SMS was sent successfully
                        if (result.succeeded())
                        {
                            // Mark the notification as successful and include the list of successful recipients
                            future.complete(event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS)
                                    .put(RESULT, GlobalConstants.STATUS_SUCCEED)
                                    .put("recipients", recipients));
                        }
                        // If all SMS sending attempts failed
                        else
                        {
                            // Propagate the failure
                            future.fail(result.cause());
                        }
                    });
                }
                // If the gateway URL or recipients are not specified
                else
                {
                    // Mark the notification as failed with a bad request error
                    future.complete(event.put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST));
                }

            }).onComplete(result ->
            {
                // Handle the result of the circuit breaker execution
                if (result.succeeded())
                {
                    // Complete the promise with the result
                    promise.complete(result.result());
                }
                // If the circuit breaker execution failed
                else
                {
                    // Complete the promise with a failure status and error details
                    promise.complete(event.put(STATUS, STATUS_FAIL)
                            .put(MESSAGE, result.cause().getMessage())
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                            .put(ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));
                }
            });
        }
        // Handle any exceptions that occur during SMS notification processing
        catch (Exception exception)
        {
            // Log the exception
            LOGGER.error(exception);

            // Complete the promise with a failure status and error details
            promise.complete(event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(GlobalConstants.MESSAGE, exception.getMessage())
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));
        }

        return promise;
    }
}
