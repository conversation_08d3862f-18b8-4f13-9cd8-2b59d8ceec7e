/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.notification;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorCodes;
import com.mindarray.GlobalConstants;
import com.mindarray.api.SNMPTrapListenerConfiguration;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.SNMPTrapListenerConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;
import org.snmp4j.*;
import org.snmp4j.mp.SnmpConstants;
import org.snmp4j.smi.OctetString;
import org.snmp4j.smi.UdpAddress;

import static com.mindarray.GlobalConstants.ERROR_CODE;

/**
 * Implementation of the Notification abstract class for sending SNMP trap notifications.
 * <p>
 * This class handles SNMP trap notifications by using the SNMP4J library to send
 * trap PDUs (Protocol Data Units) to configured target systems. It supports:
 * <ul>
 *   <li>SNMP v1 traps</li>
 *   <li>SNMP v2c traps</li>
 *   <li>SNMP v3 traps with various security levels</li>
 * </ul>
 * <p>
 * The notification process:
 * <ol>
 *   <li>Extracts the PDU from the notification event</li>
 *   <li>Retrieves SNMP trap forwarder configuration</li>
 *   <li>Builds the appropriate target based on SNMP version</li>
 *   <li>Sends the trap to the specified destination</li>
 *   <li>Reports success or failure</li>
 * </ol>
 * <p>
 * The notification event should contain:
 * <ul>
 *   <li>{@link EventBusConstants#EVENT}: The binary-encoded SNMP PDU</li>
 *   <li>{@link GlobalConstants#TARGET}: The destination address in format "host/port"</li>
 *   <li>{@link NMSConstants#SNMP_TRAP_VERSION}: The SNMP version to use</li>
 * </ul>
 * <p>
 * This implementation uses Vert.x's executeBlocking to perform the SNMP operations
 * without blocking the event loop, as SNMP4J operations are synchronous.
 */
class SNMPTrapNotification extends Notification
{
    /**
     * Logger for SNMP trap notification operations
     */
    private static final Logger LOGGER = new Logger(SNMPTrapNotification.class, GlobalConstants.MOTADATA_NOTIFICATION, "SNMP Trap Notification");

    /** Timeout in seconds for SNMP operations */
    private static final int TIMEOUT_SECONDS = MotadataConfigUtil.getSNMPTrapForwarderTimeoutSeconds();

    /** Number of retries for SNMP operations */
    private static final int RETRIES = MotadataConfigUtil.getSNMPTrapForwarderRetries();

    /** SNMP client instance used to send traps */
    private final Snmp snmp;

    /**
     * Constructs an SNMPTrapNotification with the specified SNMP client.
     *
     * @param snmp The SNMP client instance to use for sending traps
     */
    public SNMPTrapNotification(Snmp snmp)
    {
        this.snmp = snmp;
    }

    /**
     * Processes and sends an SNMP trap notification based on the provided event data.
     * <p>
     * This method:
     * <ul>
     *   <li>Extracts the PDU from the notification event</li>
     *   <li>Determines the SNMP version to use</li>
     *   <li>Builds the appropriate target based on configuration</li>
     *   <li>Sends the trap to the specified destination</li>
     *   <li>Reports success or failure</li>
     * </ul>
     * <p>
     * The method uses Vert.x's executeBlocking to perform the SNMP operations
     * without blocking the event loop, as SNMP4J operations are synchronous.
     *
     * @param event A JsonObject containing the SNMP trap notification parameters
     * @return A Promise that resolves to a JsonObject containing the result of the
     *         SNMP trap notification attempt, including success/failure status and any error details
     */
    @Override
    public Promise<JsonObject> notify(JsonObject event)
    {
        var promise = Promise.<JsonObject>promise();

        // Execute SNMP operations in a blocking context to avoid blocking the event loop
        Bootstrap.vertx().<JsonObject>executeBlocking(future ->
        {
            try
            {
                // Extract the PDU from the binary data in the event
                var pdu = (PDU) CodecUtil.toObject(event.getBinary(EventBusConstants.EVENT));

                if (pdu != null)
                {
                    // Get the SNMP trap listener configuration
                    var context = SNMPTrapListenerConfigStore.getStore().getItem();

                    // Add the target address to the context
                    context.put(GlobalConstants.TARGET, event.getString(GlobalConstants.TARGET));

                    // Handle SNMP v1 trap
                    if (pdu.getType() == PDU.V1TRAP && context.getString(SNMPTrapListenerConfiguration.SNMP_TRAP_LISTENER_V1_V2_STATUS).equalsIgnoreCase(GlobalConstants.YES))
                    {
                        send(pdu, buildV1V2Target(context, SnmpConstants.version1), future, event);
                    }
                    else
                    {
                        // Handle SNMP v2c trap
                        if (event.getInteger(NMSConstants.SNMP_TRAP_VERSION) == SnmpConstants.version2c &&
                                context.getString(SNMPTrapListenerConfiguration.SNMP_TRAP_LISTENER_V1_V2_STATUS).equalsIgnoreCase(GlobalConstants.YES))
                        {
                            send(pdu, buildV1V2Target(context, SnmpConstants.version2c), future, event);
                        }
                        // Handle SNMP v3 trap
                        else if (event.getInteger(NMSConstants.SNMP_TRAP_VERSION) == SnmpConstants.version3 &&
                                context.containsKey(SNMPTrapListenerConfiguration.SNMP_TRAP_LISTENER_V3_STATUS) &&
                                context.getString(SNMPTrapListenerConfiguration.SNMP_TRAP_LISTENER_V3_STATUS).equalsIgnoreCase(GlobalConstants.YES))
                        {
                            // Create a UserTarget for SNMP v3
                            var target = new UserTarget<>();

                            // Set the target address
                            target.setAddress(new UdpAddress(event.getString(GlobalConstants.TARGET)));

                            // Configure retries and timeout
                            target.setRetries(RETRIES);
                            target.setTimeout(TIMEOUT_SECONDS);

                            // Set SNMP version to v3
                            target.setVersion(SnmpConstants.version3);

                            // Determine the security level based on configuration
                            var level = SNMPTrapListenerConfiguration.SNMP_V3_SECURITY_LEVEL1; // No auth, no privacy

                            if (context.getString(SNMPTrapListenerConfiguration.SNMP_V3_SECURITY_LEVEL)
                                    .equalsIgnoreCase(SNMPTrapListenerConfiguration.AUTHENTICATION_NO_PRIVACY))
                            {
                                level = SNMPTrapListenerConfiguration.SNMP_V3_SECURITY_LEVEL2; // Auth, no privacy
                            }
                            else if (context.getString(SNMPTrapListenerConfiguration.SNMP_V3_SECURITY_LEVEL)
                                    .equalsIgnoreCase(SNMPTrapListenerConfiguration.AUTHENTICATION_PRIVACY))
                            {
                                level = SNMPTrapListenerConfiguration.SNMP_V3_SECURITY_LEVEL3; // Auth and privacy
                            }

                            // Set security level and username
                            target.setSecurityLevel(level);
                            target.setSecurityName(new OctetString(context.getString(SNMPTrapListenerConfiguration.SNMP_V3_SECURITY_USERNAME)));

                            // Create a scoped PDU for SNMP v3
                            var scopedPDU = new ScopedPDU();
                            scopedPDU.setType(pdu.getType());
                            scopedPDU.setRequestID(pdu.getRequestID());
                            scopedPDU.addAll(pdu.getVariableBindings());

                            // Send the SNMP v3 trap
                            send(scopedPDU, target, future, event);
                        }
                    }
                }
                else
                {
                    // Handle case where PDU could not be extracted from the event
                    future.complete(event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST));
                }
            }
            catch (Exception exception)
            {
                // Log and handle any exceptions during trap sending
                LOGGER.error(exception);

                future.complete(event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.MESSAGE, exception.getMessage())
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                        .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));
            }
        }, false, result ->
        {
            // Handle the result of the blocking operation
            if (result.succeeded())
            {
                promise.complete(result.result());
            }
            else
            {
                promise.fail(result.cause());
            }
        });

        return promise;
    }

    /**
     * Sends an SNMP trap PDU to the specified target.
     * <p>
     * This method:
     * <ul>
     *   <li>Sends the PDU using the SNMP client</li>
     *   <li>Completes the future with success or failure status</li>
     *   <li>Includes error details in case of failure</li>
     * </ul>
     *
     * @param pdu          The PDU to send
     * @param target       The target to send the PDU to
     * @param future       The promise to complete with the result
     * @param notification The original notification event
     */
    private void send(PDU pdu, Target target, Promise<JsonObject> future, JsonObject notification)
    {
        try
        {
            // Send the PDU to the target
            snmp.send(pdu, target);

            // Mark the notification as successful
            future.complete(notification.put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS));
        }
        catch (Exception exception)
        {
            // Log the exception
            LOGGER.error(exception);

            // Mark the notification as failed with error details
            future.complete(notification.put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(GlobalConstants.MESSAGE, exception.getMessage())
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));
        }
    }

    /**
     * Builds an SNMP v1 or v2c target based on the provided context and version.
     * <p>
     * This method:
     * <ul>
     *   <li>Creates a CommunityTarget with the specified community string</li>
     *   <li>Sets the SNMP version (v1 or v2c)</li>
     *   <li>Configures retries and timeout</li>
     *   <li>Sets the target address</li>
     * </ul>
     *
     * @param context The configuration context containing community string and target address
     * @param version The SNMP version to use (SnmpConstants.version1 or SnmpConstants.version2c)
     * @return A configured Target object ready for use with the SNMP client
     */
    private Target buildV1V2Target(JsonObject context, int version)
    {
        // Create a new community target
        var target = new CommunityTarget<>();

        // Set the community string from configuration
        target.setCommunity(new OctetString(context.getString(SNMPTrapListenerConfiguration.SNMP_COMMUNITY)));

        // Set the SNMP version (v1 or v2c)
        target.setVersion(version);

        // Configure retries and timeout
        target.setRetries(MotadataConfigUtil.getSNMPTrapForwarderRetries());
        target.setTimeout(MotadataConfigUtil.getSNMPTrapForwarderTimeoutSeconds());

        // Set the target address
        target.setAddress(new UdpAddress(context.getString(GlobalConstants.TARGET)));

        return target;
    }
}
