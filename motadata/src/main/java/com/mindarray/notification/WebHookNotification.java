/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.notification;

import com.mindarray.ErrorCodes;
import com.mindarray.GlobalConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.WebClientUtil;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;
import org.apache.http.HttpStatus;

import static com.mindarray.GlobalConstants.ERROR_CODE;

/**
 * Implementation of the Notification abstract class for sending webhook notifications.
 * <p>
 * This class handles webhook notifications by sending HTTP POST requests to
 * specified target URLs. Webhooks are a common integration mechanism that allow
 * external systems to be notified of events in the Motadata system.
 * <p>
 * The notification process:
 * <ol>
 *   <li>Validates the target URL</li>
 *   <li>Sends an HTTP POST request to the target URL</li>
 *   <li>Handles the HTTP response (success or failure)</li>
 *   <li>Reports the result of the webhook notification attempt</li>
 * </ol>
 * <p>
 * The notification event should contain:
 * <ul>
 *   <li>{@link GlobalConstants#TARGET}: The target URL to send the webhook to</li>
 *   <li>{@link GlobalConstants#TIMEOUT} (optional): Timeout in seconds for the HTTP request</li>
 * </ul>
 * <p>
 * This implementation uses Vert.x's WebClient to perform non-blocking HTTP requests.
 */
class WebHookNotification extends Notification
{
    /**
     * Logger for webhook notification operations
     */
    private static final Logger LOGGER = new Logger(WebHookNotification.class, GlobalConstants.MOTADATA_NOTIFICATION, "WebHook Notification");

    /**
     * Default constructor for WebHookNotification.
     */
    public WebHookNotification()
    {
        // No initialization needed
    }

    /**
     * Processes and sends a webhook notification based on the provided event data.
     * <p>
     * This method:
     * <ul>
     *   <li>Validates that the target URL is specified</li>
     *   <li>Sends an HTTP POST request to the target URL</li>
     *   <li>Processes the HTTP response</li>
     *   <li>Returns the result of the webhook notification attempt</li>
     * </ul>
     *
     * @param event A JsonObject containing the webhook notification parameters
     *              (target URL, timeout, etc.)
     * @return A Promise that resolves to a JsonObject containing the result of the
     *         webhook notification attempt, including success/failure status and any error details
     */
    @Override
    public Promise<JsonObject> notify(JsonObject event)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            // Validate that the target URL is specified
            if (CommonUtil.isNotNullOrEmpty(event.getString(GlobalConstants.TARGET)))
            {
                // Create and configure the HTTP POST request
                WebClientUtil.getWebClient().postAbs(event.getString(GlobalConstants.TARGET))
                        // Set timeout (use provided timeout or default to 30 seconds)
                        .timeout(event.containsKey(GlobalConstants.TIMEOUT) ?
                                event.getLong(GlobalConstants.TIMEOUT) * 1000L : 30 * 1000L)
                        // Send an empty JSON object as the request body
                        .sendJsonObject(new JsonObject(), result ->
                        {
                            // Handle the HTTP response
                            if (result.succeeded())
                            {
                                // If the HTTP request was successful with 200 OK status
                                if (result.result().statusCode() == HttpStatus.SC_OK)
                                {
                                    // Mark the notification as successful and include the response body
                                    promise.complete(event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS)
                                            .put(GlobalConstants.RESULT, result.result().body().toString()));
                                }
                                // If the HTTP request was successful but returned a non-200 status code
                                else
                                {
                                    // Mark the notification as failed and include the status message
                                    promise.complete(event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_RESPONSE)
                                            .put(GlobalConstants.MESSAGE, result.result().statusMessage()));
                                }
                            }
                            // If the HTTP request failed (connection error, timeout, etc.)
                            else
                            {
                                // Log the error
                                LOGGER.error(result.cause());

                                // Mark the notification as failed and include error details
                                promise.complete(event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                        .put(GlobalConstants.MESSAGE, result.cause().getMessage())
                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_RESPONSE)
                                        .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));
                            }
                        });
            }
            // If no target URL was specified
            else
            {
                // Complete the promise with the original event (no changes)
                promise.complete(event);
            }
        }
        // Handle any exceptions that occur during webhook processing
        catch (Exception exception)
        {
            // Log the exception
            LOGGER.error(exception);

            // Mark the notification as failed and include error details
            promise.complete(event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(GlobalConstants.MESSAGE, exception.getMessage())
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));
        }

        return promise;
    }
}
