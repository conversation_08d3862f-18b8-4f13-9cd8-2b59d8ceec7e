/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.compliance;

import com.mindarray.GlobalConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AsyncResult;
import io.vertx.core.Future;
import io.vertx.core.Handler;
import io.vertx.core.Vertx;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.pgclient.PgBuilder;
import io.vertx.pgclient.PgConnectOptions;
import io.vertx.sqlclient.Pool;
import io.vertx.sqlclient.PoolOptions;
import io.vertx.sqlclient.Row;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.compliance.ComplianceConstants.COMPLIANCE_DB;

public class ComplianceDBServiceImpl implements ComplianceDBService
{
    private static final Logger LOGGER = new Logger(ComplianceDBServiceImpl.class, GlobalConstants.MOTADATA_DB, "Compliance DB Service");
    private final Vertx vertx;
    private Pool complianceDB;

    ComplianceDBServiceImpl(Vertx vertx, Handler<AsyncResult
            <ComplianceDBService>> handler)
    {

        this.vertx = vertx;

        vertx.executeBlocking(future ->
                {
                    complianceDB = PgBuilder
                            .pool()
                            .with(new PoolOptions()
                                    .setMaxSize(5))
                            .connectingTo(new PgConnectOptions()
                                    .setPort(MotadataConfigUtil.getPostgresDatabasePort())
                                    .setHost(MotadataConfigUtil.getPostgresDatabaseHost())
                                    .setDatabase("motadata")
                                    .setUser("motadata")
                                    .setPassword("TRACEorg@2025")
                                    .setReconnectAttempts(3).setReconnectInterval(10 * 1000L))//will reconnect attempts 3 with 10 sec interval
                            .using(vertx)
                            .build();


                    complianceDB.query("SELECT 1").execute(result ->
                    {
                        if (result.succeeded())
                        {
                            future.complete();
                        }
                        else
                        {
                            LOGGER.info(result.cause().getMessage());

                            future.fail(result.cause().getMessage());
                        }
                    });
                },
                false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(this));

                        complianceDB.query(ComplianceConstants.TABLE_QUERY_COMPLIANCE_STATS_POLICY).execute(asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                LOGGER.info(ComplianceConstants.TABLE_COMPLIANCE_STATS_POLICY + " Table Existed/Created Successfully ");
                            }
                            else
                            {
                                LOGGER.error(asyncResult.cause().getCause());
                            }
                        });

                        complianceDB.query(ComplianceConstants.TABLE_QUERY_COMPLIANCE_STATS_ENTITY).execute(asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                complianceDB.query(String.format(ComplianceConstants.INDEX_QUERY_COMPLIANCE_POLICY_ID, ComplianceConstants.TABLE_COMPLIANCE_STATS_ENTITY)).execute(response ->
                                        LOGGER.info(ComplianceConstants.TABLE_COMPLIANCE_STATS_ENTITY + " : Indexable Existed/Column Created Successfully for compliance_policy_id"));

                                complianceDB.query(String.format(ComplianceConstants.INDEX_QUERY_OBJECT_ID, ComplianceConstants.TABLE_COMPLIANCE_STATS_ENTITY)).execute(response ->
                                        LOGGER.info(ComplianceConstants.TABLE_COMPLIANCE_STATS_ENTITY + " : Indexable Existed/Column Created Successfully for object_id"));

                                LOGGER.info(ComplianceConstants.TABLE_COMPLIANCE_STATS_ENTITY + " Table Existed/Created Successfully");
                            }
                            else
                            {
                                LOGGER.error(asyncResult.cause().getCause());
                            }
                        });

                        complianceDB.query(ComplianceConstants.TABLE_QUERY_COMPLIANCE_TRAIL).execute(asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                complianceDB.query(String.format(ComplianceConstants.INDEX_QUERY_COMPLIANCE_POLICY_ID, ComplianceConstants.TABLE_COMPLIANCE_TRAIL)).execute(response ->
                                        LOGGER.info(ComplianceConstants.TABLE_COMPLIANCE_TRAIL + " : Indexable Column Created Successfully for compliance_policy_id"));

                                complianceDB.query(String.format(ComplianceConstants.INDEX_QUERY_OBJECT_ID, ComplianceConstants.TABLE_COMPLIANCE_TRAIL)).execute(response ->
                                        LOGGER.info(ComplianceConstants.TABLE_COMPLIANCE_TRAIL + " : Indexable Column Created Successfully for object_id"));

                                complianceDB.query(ComplianceConstants.INDEX_QUERY_RULE_ID).execute(response ->
                                        LOGGER.info(ComplianceConstants.TABLE_COMPLIANCE_TRAIL + " : Indexable Column Created Successfully for compliance_rule_id"));

                                LOGGER.info(ComplianceConstants.TABLE_COMPLIANCE_TRAIL + " Table Existed/Created Successfully");
                            }
                            else
                            {
                                LOGGER.error(asyncResult.cause().getCause());
                            }
                        });
                    }
                    else
                    {
                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });
    }

    @Override
    public ComplianceDBService save(String table, JsonObject document, String user, String remoteIP, Handler<AsyncResult<Long>> handler)
    {
        vertx.<Long>executeBlocking(future ->
                {
                    var id = document.containsKey(GlobalConstants.ID) ? document.getLong(GlobalConstants.ID) : CommonUtil.newId();

                    complianceDB.preparedQuery(populate(table, document)).execute(result ->
                    {
                        if (result.succeeded())
                        {
                            future.complete(id);
                        }
                        else
                        {
                            future.fail(result.cause().getMessage());
                        }
                    });
                },

                false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            HAConstants.sync(table, null, document, user, remoteIP, HAConstants.HASyncOperation.SAVE.getName(), COMPLIANCE_DB);
                        }
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });

        return this;
    }

    @Override
    public ComplianceDBService execute(String table, String document, String user, String remoteIP, Handler<AsyncResult<Long>> handler)
    {
        vertx.<Long>executeBlocking(future ->
                        complianceDB.preparedQuery(document).execute(result ->
                        {
                            if (result.succeeded())
                            {
                                future.complete();
                            }
                            else
                            {
                                future.fail(result.cause().getMessage());
                            }
                        }),

                false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });

        return this;
    }

    @Override
    public ComplianceDBService saveAll(String table, JsonArray documents, String user, String remoteIP, Handler<AsyncResult<Long>> handler)
    {
        vertx.<Long>executeBlocking(future ->
                        complianceDB.preparedQuery(populateBatch(table, documents)).execute(result ->
                        {
                            if (result.succeeded())
                            {
                                future.complete();
                            }
                            else
                            {
                                future.fail(result.cause().getMessage());
                            }
                        }),

                false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            HAConstants.sync(table, documents, user, HAConstants.HASyncOperation.SAVE_ALL.getName(), COMPLIANCE_DB);
                        }
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });

        return this;
    }

    @Override
    public ComplianceDBService get(String table, JsonObject query, Handler<AsyncResult<JsonObject>> handler)
    {
        vertx.<JsonObject>executeBlocking(future ->
                {
                    var item = new JsonObject();

                    if (query != null && !query.isEmpty())
                    {
                        complianceDB.preparedQuery(query.getString(ComplianceConstants.QUERY)).execute()
                                .onComplete(result ->
                                {
                                    if (result.succeeded())
                                    {
                                        if (!query.containsKey(ComplianceConstants.TRANSFORM) || query.getBoolean(ComplianceConstants.TRANSFORM))//fetch data in visualization format or list of result format
                                        {
                                            result.result().forEach(value -> transform(value, item));
                                        }
                                        else
                                        {
                                            var values = new JsonArray();

                                            result.result().forEach(value -> transform(value, values));

                                            if (!values.isEmpty())
                                            {
                                                item.put(RESULT, values);
                                            }
                                        }

                                        future.complete(item);
                                    }
                                    else
                                    {
                                        future.fail(result.cause().getMessage());
                                    }
                                });
                    }
                },
                false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });
        return this;
    }

    @Override
    public ComplianceDBService delete(String table, JsonObject query, String user, String remoteIP, Handler<AsyncResult<JsonArray>> handler)
    {
        var items = new JsonArray();

        vertx.<JsonArray>executeBlocking(future ->
                {

                    if (!query.isEmpty() && !table.isEmpty())
                    {
                        complianceDB.preparedQuery(delete(table, query)).execute().onComplete(result ->
                        {
                            if (result.succeeded())
                            {
                                future.complete(items);
                            }
                            else
                            {
                                future.fail(result.cause().getMessage());
                            }

                        });
                    }
                },

                true,

                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            HAConstants.sync(table, query, null, user, remoteIP, HAConstants.HASyncOperation.DELETE.getName(), COMPLIANCE_DB);
                        }
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });

        return this;
    }

    @Override
    public void close(Handler<AsyncResult<Void>> handler)
    {
        vertx.<JsonObject>executeBlocking(future ->
                {
                    if (complianceDB != null)
                    {
                        complianceDB.close();
                    }

                    future.complete();
                },

                result ->
                {

                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture());
                    }

                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });

    }

    private String populate(String tableName, JsonObject document)
    {
        var query = new StringBuilder();

        var columns = new StringBuilder();

        var values = new StringBuilder();

        for (var entry : document.getMap().entrySet())
        {
            columns.append("\"").append(entry.getKey()).append("\"").append(COMMA_SEPARATOR);

            if (entry.getValue() instanceof String)
            {
                values.append("'").append(entry.getValue()).append("'").append(COMMA_SEPARATOR);
            }
            else
            {
                values.append(entry.getValue()).append(COMMA_SEPARATOR);
            }
        }

        columns.deleteCharAt(columns.length() - 1);

        values.deleteCharAt(values.length() - 1);

        return query.append("INSERT INTO ").append(tableName).append(" (").append(columns).append(") ").append(" VALUES ").append("(").append(values).append(") ").toString();
    }

    private String populateBatch(String tableName, JsonArray documents)
    {
        var query = new StringBuilder();

        try
        {
            var columns = new StringBuilder();

            var values = new StringBuilder();

            // Build column names based on the first JSON object
            var headers = documents.getJsonObject(0);

            for (var column : headers.getMap().keySet())
            {
                column = column.replace(".", "_");

                columns.append("\"").append(column).append("\", ");
            }
            // Remove trailing comma and space
            columns.delete(columns.length() - 2, columns.length());

            // Build values for each JSON object in the array
            for (var i = 0; i < documents.size(); i++)
            {
                var document = documents.getJsonObject(i);

                values.append("("); // Start row values

                for (var entry : document.getMap().entrySet())
                {
                    var value = entry.getValue();

                    // Add quotes for string values, else add raw value
                    if (value instanceof String)
                    {
                        values.append("'").append(value).append("', ");
                    }
                    else
                    {
                        values.append(value).append(", ");
                    }
                }

                // Remove trailing comma and space for this row
                values.delete(values.length() - 2, values.length());

                values.append("), "); // End row values
            }

            // Remove trailing comma and space after the last row
            values.delete(values.length() - 2, values.length());

            // Build the final INSERT query
            query.append("INSERT INTO ").append(tableName)
                    .append(" (").append(columns).append(") ")
                    .append("VALUES ").append(values);


        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return query.toString();
    }


    private String delete(String table, JsonObject condition)
    {
        var result = new StringBuilder();

        ComplianceConstants.delete(table, condition, result);

        return result.toString();
    }

    public void transform(Row row, JsonObject item)
    {
        try
        {
            for (var i = 0; i < row.size(); i++)
            {
                var column = row.getColumnName(i).replace("_", ".");

                if (item.containsKey(column))
                {
                    //As rule_status would be containing integer thus need to change it to severity data.
                    if (column.equalsIgnoreCase(ComplianceConstants.CURRENT_SCAN_STATUS) || column.equalsIgnoreCase(ComplianceConstants.LAST_SCAN_STATUS))
                    {
                        item.getJsonArray(column).add(CommonUtil.getString(ComplianceConstants.RuleStatus.valueOfName(CommonUtil.getInteger(row.getValue(i)))).toLowerCase());
                    }
                    else
                    {
                        item.getJsonArray(column).add(row.getValue(i));
                    }
                }
                else
                {
                    //As rule_status would be containing integer thus need to change it to severity data.
                    if (column.equalsIgnoreCase(ComplianceConstants.CURRENT_SCAN_STATUS) || column.equalsIgnoreCase(ComplianceConstants.LAST_SCAN_STATUS))
                    {
                        item.put(column, new JsonArray().add(CommonUtil.getString(ComplianceConstants.RuleStatus.valueOfName(CommonUtil.getInteger(row.getValue(i)))).toLowerCase()));
                    }
                    else
                    {
                        item.put(column, new JsonArray().add(row.getValue(i)));
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

    }

    public void transform(Row row, JsonArray items)
    {
        try
        {
            var item = new JsonObject();

            for (var i = 0; i < row.size(); i++)
            {
                item.put(row.getColumnName(i).replace("_", "."), row.getValue(i));
            }

            items.add(item);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

    }

}
