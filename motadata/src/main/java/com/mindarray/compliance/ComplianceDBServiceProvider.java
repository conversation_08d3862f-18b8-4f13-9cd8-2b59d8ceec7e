/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.compliance;

import com.mindarray.GlobalConstants;
import com.mindarray.util.Logger;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.serviceproxy.ServiceBinder;

public class ComplianceDBServiceProvider extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(ComplianceDBServiceProvider.class, GlobalConstants.MOTADATA_DB, "Compliance DB Service Provider");

    @Override
    public void start(Promise<Void> promise) throws Exception
    {

        ComplianceDBService.execute(vertx, result ->
        {
            if (result.succeeded())
            {
                try
                {
                    var binder = new ServiceBinder(vertx);

                    binder.setAddress("compliance.db.service").registerLocal(ComplianceDBService.class, result.result());

                    promise.complete();
                }

                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    LOGGER.fatal("failed to start compliance db service...");

                    promise.fail(exception.getCause());
                }

            }

            else
            {
                LOGGER.error(result.cause());

                promise.fail(result.cause());
            }
        });
    }

}
