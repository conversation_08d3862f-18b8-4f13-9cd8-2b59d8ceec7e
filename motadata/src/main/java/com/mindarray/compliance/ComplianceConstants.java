/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*  Change Logs:
 *  Date			Author		    Notes
 *  21-Apr-2025		Bharat		    MOTADATA-5798: Enhance compliance in the plugin engine by implementing batching for CLI sessions.
 */

package com.mindarray.compliance;

import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Tag;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.ComplianceWeightedCalculationConfigStore;
import com.mindarray.store.ConfigurationConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.TagConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;

public class ComplianceConstants
{
    //PG Tables for Compliance
    public static final String COMPLIANCE_DB = "Compliance DB";
    public static final String TABLE_COMPLIANCE_TRAIL = "tbl_compliance_trail";
    public static final String TABLE_COMPLIANCE_STATS_ENTITY = "tbl_compliance_stats_entity";
    public static final String TABLE_COMPLIANCE_STATS_POLICY = "tbl_compliance_stats_policy";
    public static final String QUERY_COMPLIANCE_TRAIL_TABLE = "SELECT compliance_policy_id, object_id, compliance_rule_id, current_scan_timestamp, current_scan_status FROM tbl_compliance_trail";
    public static final String QUERY_COMPLIANCE_STATS_POLICY_TABLE = "SELECT compliance_policy_id, compliance_percentage FROM tbl_compliance_stats_policy";
    public static final String QUERY = "query";
    public static final String COLUMNS = "columns";
    public static final String COLUMN_ALIASES = "column.aliases";
    public static final String SELECT = "SELECT";
    public static final String WHERE = "WHERE";
    public static final String FROM = "FROM";
    public static final String GROUP_BY = "GROUP BY";
    public static final String AS = "AS";
    public static final String DELETE = "DELETE";
    public static final String INSERT = "INSERT";
    public static final String LIMIT = "LIMIT";
    public static final String ORDER_BY = "ORDER BY";
    public static final String OR = "OR";
    public static final String UPDATE = "UPDATE";
    public static final String SET = "SET";
    public static final String TRANSFORM = "transform";
    public static final String TABLE_QUERY_COMPLIANCE_STATS_POLICY = "CREATE TABLE IF NOT EXISTS tbl_compliance_stats_policy (compliance_policy_id BIGINT PRIMARY KEY, compliance_percentage INTEGER CHECK (compliance_percentage BETWEEN 0 AND 100), vulnerable INTEGER DEFAULT 0, poor INTEGER DEFAULT 0, moderate INTEGER DEFAULT 0, secure INTEGER DEFAULT 0, last_scan_timestamp BIGINT NOT NULL)";
    public static final String TABLE_QUERY_COMPLIANCE_TRAIL = "CREATE TABLE IF NOT EXISTS tbl_compliance_trail (id BIGSERIAL PRIMARY KEY, compliance_policy_id BIGINT NOT NULL, compliance_benchmark_id BIGINT NOT NULL, compliance_rule_id BIGINT NOT NULL, object_id INTEGER NOT NULL, compliance_rule_severity VARCHAR NOT NULL, current_scan_status INTEGER NOT NULL, last_scan_status INTEGER NOT NULL, current_scan_timestamp BIGINT NOT NULL, last_scan_timestamp BIGINT NOT NULL)";
    public static final String TABLE_QUERY_COMPLIANCE_STATS_ENTITY = "CREATE TABLE IF NOT EXISTS tbl_compliance_stats_entity (id BIGSERIAL PRIMARY KEY, compliance_policy_id BIGINT NOT NULL, object_id INTEGER NOT NULL, compliance_percentage INTEGER NOT NULL, severity VARCHAR, last_scan_status INTEGER NOT NULL, last_scan_timestamp BIGINT NOT NULL, scanned_rule INTEGER NOT NULL, message VARCHAR NULL)";
    public static final String INDEX_QUERY_COMPLIANCE_POLICY_ID = "CREATE INDEX IF NOT EXISTS idx_compliance_policy_id ON %s (compliance_policy_id)";
    public static final String INDEX_QUERY_OBJECT_ID = "CREATE INDEX IF NOT EXISTS idx_compliance_object_id ON %s (object_id)";
    public static final String INDEX_QUERY_RULE_ID = "CREATE INDEX IF NOT EXISTS idx_compliance_rule_id ON tbl_compliance_trail (compliance_rule_id)";
    public static final String COMPLIANCE_RULE_ID = "compliance.rule.id";
    public static final String COMPLIANCE_POLICY_ID = "compliance.policy.id";
    public static final String COMPLIANCE_BENCHMARK_ID = "compliance.benchmark.id";
    public static final String RULE_CONDITION = "condition";
    public static final String RULE_OCCURRENCE = "occurrence";
    public static final String RESULT_PATTERN = "result.pattern";
    public static final String CONTENT = "content";
    public static final String LAST_SCAN_TIMESTAMP = "last.scan.timestamp";
    public static final String LAST_SCAN_STATUS = "last.scan.status";
    public static final String CURRENT_SCAN_TIMESTAMP = "current.scan.timestamp";
    public static final String CURRENT_SCAN_STATUS = "current.scan.status";
    public static final String COMPLIANCE_POLICY_EMAIL_NOTIFICATION_RECIPIENT = "compliance.policy.email.notification.recipients";
    public static final String COMPLIANCE_POLICY_USER_NOTIFICATION_RECIPIENT = "compliance.policy.user.notification.recipients";
    public static final String COMPLIANCE_POLICY_NOTIFICATION_CONTEXT = "compliance.policy.notification.context";
    public static final String SCANNED_RULE = "scanned.rule";
    public static final String COMPLIANCE_PERCENTAGE = "compliance.percentage";
    public static final String BLOCK_CRITERIA_REGEX_PATTERN = "%s.*?%s";
    public static final String BLOCK_CRITERIA_REGEX_PATTERN_WITH_START = "%s.*?.*";
    // Weightage calculation constants... as of now creating constants here... once API are available, we will move them.
    public static final String RULE_WEIGHTAGE = "rule.weightage";
    public static final String OBJECT_STATE_WEIGHTAGE = "object.state.weightage";
    public static final String START_RANGE = "start.range";
    public static final String END_RANGE = "end.range";
    public static final String COMPLIANCE_CLI_IDENTIFIER = "compliance.cli.identifier";
    private static final Logger LOGGER = new Logger(ComplianceConstants.class, GlobalConstants.MOTADATA_COMPLIANCE, "Compliance Constants");

    /*
     This Method is used to qualify objects as per Category for Compliance.
     */
    public static JsonArray qualifyObjects(JsonObject context, NMSConstants.Category category)
    {
        var objects = new JsonArray();

        if (category == NMSConstants.Category.NETWORK)
        {
            if (context.getString(ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.OBJECT.getName()))
            {
                objects = ObjectConfigStore.getStore().getItems(context.getJsonArray(ENTITIES));
            }
            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.GROUP.getName()))
            {
                // As Group , only NCM monitors are required thus filtering out only items which are configured for it.
                var items = ConfigurationConfigStore.getStore().getItemsByDiscoveryStatus(ObjectConfigStore.getStore().getUniqueObjectIdsByGroups(context.getJsonArray(ENTITIES)));

                if (items != null)
                {
                    objects = items;
                }
            }
            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG))
            {
                var counts = new HashSet<Long>();

                var items = ObjectConfigStore.getStore().getItemsByMultiValueFieldAny(AIOpsObject.OBJECT_TAGS,
                        new JsonArray(TagConfigStore.getStore().getIdsByItems(context.getJsonArray(ENTITIES))
                                .stream().map(CommonUtil::getInteger).toList()));

                for (var index = 0; index < items.size(); index++)
                {
                    var item = items.getJsonObject(index);

                    if (counts.add(item.getLong(ID)))
                    {
                        objects.add(item);
                    }
                }
            }
        }
        else
        {
            LOGGER.warn(String.format("Unsupported category %s", category.getName()));
        }

        return objects;
    }

    public static String getState(long score)
    {
        for (var entry : ComplianceWeightedCalculationConfigStore.getStore().getItem(GlobalConstants.DEFAULT_ID).getJsonObject(ComplianceConstants.OBJECT_STATE_WEIGHTAGE))
        {
            if (score >= ((JsonObject) entry.getValue()).getInteger(ComplianceConstants.START_RANGE) && score <= ((JsonObject) entry.getValue()).getInteger(ComplianceConstants.END_RANGE))
            {
                return entry.getKey();
            }
        }

        return ComplianceState.SECURE.name();
    }

    public static int getScoreByRuleWeightage(ComplianceConstants.RuleSeverity severity)
    {
        return ComplianceWeightedCalculationConfigStore.getStore().getItem(GlobalConstants.DEFAULT_ID).getJsonObject(ComplianceConstants.RULE_WEIGHTAGE).getInteger(severity.name());
    }

    public static void where(StringBuilder filter, StringBuilder query)
    {
        query.append(" ").append(WHERE).append(" ").append(filter);
    }

    public static void select(Map<String, JsonArray> columns, StringBuilder query)
    {
        query.append(SELECT).append(" ");

        for (var i = 0; i < columns.get(COLUMNS).size(); i++)
        {
            query.append(columns.get(COLUMNS).getString(i).replace(".", "_")).append(" ");

            if (columns.containsKey(COLUMN_ALIASES) && !columns.get(COLUMN_ALIASES).getString(i).isEmpty())
            {
                query.append(" ").append(AS).append(" ").append(columns.get(COLUMN_ALIASES).getString(i)).append(" ");
            }

            if (i < columns.get(COLUMNS).size() - 1)
            {
                query.append(", ");
            }
        }
    }

    // Add OR WHERE condition (subsequent conditions)
    public static StringBuilder orWhere(String condition, Object value, StringBuilder query)
    {
        return query.append(" ").append(OR).append(" ").append(condition);
    }

    public static void from(String table, StringBuilder query)
    {
        query.append(" ").append(FROM).append(" ").append(table);
    }

    public static StringBuilder orderBy(String column, boolean ascending, StringBuilder query)
    {

        return query.append(" ").append(ORDER_BY).append(" ").append(column).append(ascending ? " ASC" : " DESC");
    }

    public static void groupBy(JsonArray columns, StringBuilder query)
    {
        query.append(" ").append(GROUP_BY).append(" ");

        for (var i = 0; i < columns.size(); i++)
        {
            query.append(columns.getString(i).replace(".", "_"));

            if (i < columns.size() - 1)
            {
                query.append(", ");
            }
        }

    }

    public static StringBuilder AS(String alias, StringBuilder query)
    {

        return query.append(" ").append(AS).append(" ").append(alias);
    }

    public static StringBuilder limit(int limit, StringBuilder query)
    {

        return query.append(" ").append(LIMIT).append(" ").append(limit);
    }

    public static StringBuilder update(String table, StringBuilder query)
    {

        return query.append(UPDATE).append(" ").append(table);
    }

    public static StringBuilder set(String column, Object value, Integer Count, StringBuilder query)
    {
        /*
         todo: code required to add for parameter..
         */
        if (query.indexOf("SET") == -1)
        {
            query.append(" SET ");
        }
        else
        {
            query.append(", ");
        }

        query.append(column).append(" = $").append(Count);

        return query;
    }

    /**
     * Generates a dynamic Upsert query for a single ID and column.
     *
     * @param table          The name of the table.
     * @param columns        A HashMap containing the column name and its value for the clause.
     * @param conflictColumn name of the conflict columns.
     * @param query          A StringBuilder object to build the query.
     * @return The completed query as a StringBuilder.
     */
    public static StringBuilder upsert(String table, Map<String, Object> columns, String conflictColumn, StringBuilder query)
    {
        query.append("INSERT INTO ").append(table).append(" (");

        // Add column names and placeholders
        String columnNames = String.join(", ", columns.keySet());

        String placeholders = columns.keySet().stream().map(k -> "$").collect(Collectors.joining(", "));

        query.append(columnNames).append(") VALUES (").append(placeholders).append(")");

        // Add ON CONFLICT clause
        query.append(" ON CONFLICT (").append(conflictColumn).append(") DO UPDATE SET ");

        // Build the update clause
        String updates = columns.keySet().stream()
                .map(column -> column + " = excluded." + column)
                .collect(Collectors.joining(", "));
        query.append(updates);

        // Add values to parameters
        //parameters.addAll(values.values());

        return query;
    }

    /**
     * Generates a dynamic DELETE query for a single ID and column.
     *
     * @param table     The name of the table.
     * @param condition A JsonObject containing the column name and its value for the WHERE clause.
     * @param query     A StringBuilder object to build the query.
     */
    public static void delete(String table, JsonObject condition, StringBuilder query)
    {
        query.append(DELETE).append(" ").append(FROM).append(" ").append(table).append(" ").append(WHERE).append(" ");

        for (var result : condition.getMap().entrySet())
        {
            query.append(result.getKey()).append(" = '").append(result.getValue()).append("'");
        }

    }

    public enum ComplianceState
    {
        VULNERABLE,
        MODERATE,
        POOR,
        SECURE
    }

    public enum RuleCheckIn
    {
        CONFIG_FILE("Config File"),
        CLI("CLI");

        private static final Map<String, RuleCheckIn> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(ComplianceConstants.RuleCheckIn::getName, entity -> entity)));
        private final String name;

        RuleCheckIn(String name)
        {
            this.name = name;
        }

        public static ComplianceConstants.RuleCheckIn valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }


    public enum RuleCheckConfiguration
    {
        BASIC,
        ADVANCED
    }

    public enum RuleSeverity
    {
        CRITICAL,
        HIGH,
        MEDIUM,
        LOW,
        INFO
    }

    public enum RuleStatus
    {
        UNKNOWN(0),

        SUCCEEDED(1),

        FAILED(2);

        private static final Map<Integer, RuleStatus> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(RuleStatus::getType, e -> e)));

        private final Integer type;

        RuleStatus(Integer type)
        {
            this.type = type;
        }

        public static RuleStatus valueOfName(Integer type)
        {
            return VALUES.get(type);
        }

        public Integer getType()
        {
            return type;
        }
    }
}
