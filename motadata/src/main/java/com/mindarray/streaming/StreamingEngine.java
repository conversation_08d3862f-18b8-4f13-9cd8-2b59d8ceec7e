/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.streaming;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.APIConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.LocalEventRouter;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;
import java.util.stream.Collectors;

/**
 * The StreamingEngine is responsible for managing streaming sessions and routing streaming events.
 * <p>
 * This class serves as the central coordinator for all streaming activities in the system. It:
 * <ul>
 *   <li>Manages the lifecycle of streaming sessions</li>
 *   <li>Routes streaming events to appropriate handlers</li>
 *   <li>Enforces session timeouts to prevent resource leaks</li>
 *   <li>Coordinates with StreamingBroadcaster to deliver streaming data to clients</li>
 * </ul>
 * <p>
 * The StreamingEngine supports multiple types of streaming (defined in the {@link StreamingType} enum)
 * and maintains separate session maps for each type to ensure proper isolation and management.
 * <p>
 * This class is implemented as a Vert.x verticle and uses the Vert.x event bus for communication
 * with other components in the system.
 */
public class StreamingEngine extends AbstractVerticle
{
    /**
     * Key used to identify the streaming type in event messages
     */
    public static final String STREAMING_TYPE = "streaming.type";

    /** Logger instance for this class */
    private static final Logger LOGGER = new Logger(StreamingEngine.class, GlobalConstants.MOTADATA_STREAMING, "Streaming Engine");

    /** Map of active streaming sessions organized by streaming type */
    private final Map<StreamingEngine.StreamingType, Map<String, String>> sessions = new EnumMap<>(StreamingEngine.StreamingType.class); // stream type ->  sessionId

    /** Map of session timers to track session activity and enforce timeouts */
    private final Map<StreamingEngine.StreamingType, Map<String, Long>> sessionTimers = new EnumMap<>(StreamingEngine.StreamingType.class); // stream type ->  sessionId timer

    /**
     * Initializes the StreamingEngine verticle and sets up event bus handlers.
     * <p>
     * This method:
     * <ul>
     *   <li>Sets up a periodic timer to clean up inactive sessions (except in dev mode)</li>
     *   <li>Initializes the LocalEventRouter for streaming events</li>
     *   <li>Sets up event bus handlers for various streaming operations</li>
     * </ul>
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        // Set up periodic cleanup of inactive sessions (every 5 seconds)
        if (!MotadataConfigUtil.devMode())
        {
            vertx.setPeriodic(5 * 1000L, timer -> clear());
        }

        // Initialize the LocalEventRouter for streaming events
        Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_STREAM
                        , APIConstants.SESSION_ID, 1, StreamingBroadcaster.class.getCanonicalName(), false), EventBusConstants.EVENT_STREAM, null)
                .onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        // Handler for starting streaming sessions
                        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_STREAMING_START, message ->
                        {
                            var event = message.body();

                            switch (StreamingType.valueOfName(event.getString(STREAMING_TYPE)))
                            {
                                // For EVENT_TRACKER and EVENT_ENGINE_STATS, use session ID directly
                                case EVENT_TRACKER, EVENT_ENGINE_STATS ->
                                {
                                    // Create session entry if not exists and store session ID
                                    sessions.computeIfAbsent(StreamingEngine.StreamingType.valueOfName(event.getString(STREAMING_TYPE)), value -> new HashMap<>()).put(event.getString(APIConstants.SESSION_ID), event.getString(APIConstants.SESSION_ID));

                                    // Record session start time for timeout tracking
                                    sessionTimers.computeIfAbsent(StreamingEngine.StreamingType.valueOfName(event.getString(STREAMING_TYPE)), value -> new HashMap<>()).put(event.getString(APIConstants.SESSION_ID), System.currentTimeMillis());

                                    // Forward the start event to the event stream
                                    vertx.eventBus().send(EventBusConstants.EVENT_STREAM, event.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_STREAMING_START));
                                }
                                // For other streaming types (LOG_TAIL, TRAP_TAIL), use composite session ID
                                default ->
                                {
                                    // Create composite session ID from session ID and UI event UUID
                                    var session = event.getString(APIConstants.SESSION_ID) + GlobalConstants.SEPARATOR + event.getString(EventBusConstants.UI_EVENT_UUID);

                                    // Create session entry if not exists and store composite session ID
                                    sessions.computeIfAbsent(StreamingEngine.StreamingType.valueOfName(event.getString(STREAMING_TYPE)), value -> new HashMap<>()).put(session, session);

                                    // Record session start time for timeout tracking
                                    sessionTimers.computeIfAbsent(StreamingEngine.StreamingType.valueOfName(event.getString(STREAMING_TYPE)), value -> new HashMap<>()).put(session, System.currentTimeMillis());

                                    // Forward the start event to the event stream
                                    vertx.eventBus().send(EventBusConstants.EVENT_STREAM, event.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_STREAMING_START));
                                }
                            }

                        }).exceptionHandler(LOGGER::error);

                        // Handler for stopping streaming sessions
                        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_STREAMING_STOP, message ->
                        {
                            var event = message.body();

                            switch (StreamingType.valueOfName(event.getString(STREAMING_TYPE)))
                            {
                                // For EVENT_TRACKER and EVENT_ENGINE_STATS, use session ID directly
                                case EVENT_TRACKER, EVENT_ENGINE_STATS ->
                                {
                                    // Forward the stop event to the event stream
                                    vertx.eventBus().send(EventBusConstants.EVENT_STREAM, event.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_STREAMING_STOP));

                                    // Remove session from active sessions map
                                    sessions.get(StreamingEngine.StreamingType.valueOfName(event.getString(STREAMING_TYPE))).remove(event.getString(APIConstants.SESSION_ID));

                                    // Remove session timer
                                    sessionTimers.get(StreamingEngine.StreamingType.valueOfName(event.getString(STREAMING_TYPE))).remove(event.getString(APIConstants.SESSION_ID));

                                    // Remove routes for this session
                                    vertx.eventBus().send(EventBusConstants.EVENT_STREAM + ".remove.routes", event.put("routes", new JsonArray().add(event.getString(APIConstants.SESSION_ID))));
                                }
                                // For other streaming types (LOG_TAIL, TRAP_TAIL), use composite session ID
                                default ->
                                {
                                    // Forward the stop event to the event stream
                                    vertx.eventBus().send(EventBusConstants.EVENT_STREAM, event.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_STREAMING_STOP));

                                    // Remove session from active sessions map using composite session ID
                                    sessions.get(StreamingEngine.StreamingType.valueOfName(event.getString(STREAMING_TYPE))).remove(event.getString(APIConstants.SESSION_ID) + GlobalConstants.SEPARATOR + event.getString(EventBusConstants.UI_EVENT_UUID));

                                    // Remove session timer using composite session ID
                                    sessionTimers.get(StreamingEngine.StreamingType.valueOfName(event.getString(STREAMING_TYPE))).remove(event.getString(APIConstants.SESSION_ID) + GlobalConstants.SEPARATOR + event.getString(EventBusConstants.UI_EVENT_UUID));

                                    // Remove routes for this session
                                    vertx.eventBus().send(EventBusConstants.EVENT_STREAM + ".remove.routes", event.put("routes", new JsonArray().add(event.getString(APIConstants.SESSION_ID))));
                                }
                            }

                        }).exceptionHandler(LOGGER::error);

                        // Handler for worker tracker events
                        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_STREAMING_WORKER_TRACKER, message ->
                        {
                            {
                                var event = message.body();

                                // Forward worker tracker events to the event stream
                                vertx.eventBus().send(EventBusConstants.EVENT_STREAM, event.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_STREAMING_WORKER_TRACKER));
                            }
                        }).exceptionHandler(LOGGER::error);

                        // Handler for broadcasting streaming data to clients
                        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_STREAMING_BROADCAST, message ->
                        {
                            try
                            {
                                var event = message.body();

                                // Get the streaming type from the event
                                var streamType = StreamingType.valueOfName(event.getString(STREAMING_TYPE));

                                // If there are active sessions for this streaming type, broadcast to all of them
                                if (sessions.containsKey(streamType) && !sessions.get(streamType).isEmpty())
                                {
                                    // For each session, send the event with the session ID
                                    sessions.get(streamType).keySet().forEach(session -> vertx.eventBus().send(EventBusConstants.EVENT_STREAM, event.put(APIConstants.SESSION_ID, session).put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_STREAMING_BROADCAST)));
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }

                        }).exceptionHandler(LOGGER::error);

                        // Initialization complete
                        promise.complete();
                    }
                });
    }

    /**
     * Cleans up inactive streaming sessions that have exceeded their timeout period.
     * <p>
     * This method is called periodically to:
     * <ul>
     *   <li>Identify sessions that have been inactive for longer than the configured timeout</li>
     *   <li>Remove these sessions from the session maps</li>
     *   <li>Send stop events for these sessions to properly clean up resources</li>
     * </ul>
     * <p>
     * The timeout period is configurable via {@link MotadataConfigUtil#getStreamingSessionTimeoutMillis()}.
     */
    private void clear()
    {
        try
        {
            // Iterate through all streaming types and their session timers
            for (var entry : sessionTimers.entrySet())
            {
                var iterator = entry.getValue().entrySet().iterator();

                while (iterator.hasNext())
                {
                    var item = iterator.next();

                    // Check if session has exceeded timeout period
                    if (System.currentTimeMillis() - item.getValue() > MotadataConfigUtil.getStreamingSessionTimeoutMillis())
                    {
                        // Remove the session timer
                        iterator.remove();

                        // Remove the session and get its components (session ID and possibly UI event UUID)
                        var tokens = this.sessions.get(entry.getKey()).remove(item.getKey()).split(GlobalConstants.SEPARATOR_WITH_ESCAPE);

                        // Send a stop event to properly clean up resources
                        // First token is session ID, second token (if exists) is UI event UUID
                        vertx.eventBus().send(EventBusConstants.EVENT_STREAM, new JsonObject()
                                .put(STREAMING_TYPE, entry.getKey().getName())
                                .put(APIConstants.SESSION_ID, tokens[0])
                                .put(EventBusConstants.UI_EVENT_UUID, tokens.length > 1 ? tokens[1] : tokens[0])
                                .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_STREAMING_STOP));
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Defines the different types of streaming supported by the system.
     * <p>
     * Each streaming type represents a different category of data that can be streamed
     * to clients and may have different handling logic in the StreamingBroadcaster.
     */
    public enum StreamingType
    {
        /** Streams log messages in real-time */
        LOG_TAIL("Log Tail"),

        /** Streams SNMP trap messages as they arrive */
        TRAP_TAIL("Trap Tail"),

        /** Streams statistics from the event processing engine */
        EVENT_ENGINE_STATS("Event Engine Stats"),

        /** Streams event tracking information */
        EVENT_TRACKER("Event Tracker");

        /** Map for efficient lookup of enum values by name */
        private static final Map<String, StreamingType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(StreamingType::getName, e -> e)));

        /** The display name of the streaming type */
        private final String name;

        /**
         * Constructs a StreamingType with the specified display name.
         *
         * @param name The display name for this streaming type
         */
        StreamingType(String name)
        {
            this.name = name;
        }

        /**
         * Looks up a StreamingType by its display name.
         *
         * @param name The display name to look up
         * @return The corresponding StreamingType, or null if not found
         */
        public static StreamingType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        /**
         * Gets the display name of this streaming type.
         *
         * @return The display name
         */
        public String getName()
        {
            return name;
        }
    }
}
