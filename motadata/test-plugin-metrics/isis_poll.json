{"***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"isis.neighbour": [{"isis.neighbour": "***********", "isis.neighbour.3way.state": "Down", "isis.neighbour.hold.time": " 0 day 0 hour 0 minute  0 second", "isis.neighbour.last.up.time": " 49 days 17 hours 2 minutes 47 seconds", "isis.neighbour.protocol": "IPV4", "isis.neighbour.state": "Up", "isis.neighbour.system.type": "L1L2 Intermediate System"}, {"isis.neighbour": "***********", "isis.neighbour.3way.state": "Down", "isis.neighbour.hold.time": " 0 day 0 hour 0 minute  0 second", "isis.neighbour.last.up.time": " 34 days 19 hours 56 minutes 58 seconds", "isis.neighbour.protocol": "IPV6", "isis.neighbour.state": "Up", "isis.neighbour.system.type": "L1L2 Intermediate System"}, {"isis.neighbour.protocol": "IPV4"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Juniper Networks", "port": 161, "rediscover.job": "Network Metric", "result": {"isis.neighbour": [{"isis.neighbour": "***********", "isis.neighbour.3way.state": "Initializing", "isis.neighbour.hold.time": " 0 day 0 hour 0 minute  0 second", "isis.neighbour.last.up.time": " 34 days 19 hours 57 minutes 16 seconds", "isis.neighbour.protocol": "IPV4", "isis.neighbour.state": "Up", "isis.neighbour.system.type": "L2 Intermediate System"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"isis.neighbour": [{"isis.neighbour": "***********", "isis.neighbour.3way.state": "Down", "isis.neighbour.hold.time": " 0 day 0 hour 0 minute  0 second", "isis.neighbour.last.up.time": " 5 days 4 hours 24 minutes 32 seconds", "isis.neighbour.protocol": "IPV4", "isis.neighbour.state": "Up", "isis.neighbour.system.type": "L1L2 Intermediate System"}, {"isis.neighbour": "***********", "isis.neighbour.3way.state": "Down", "isis.neighbour.hold.time": " 0 day 0 hour 0 minute  0 second", "isis.neighbour.last.up.time": " 5 days 4 hours 20 minutes 3 seconds", "isis.neighbour.protocol": "IPV6", "isis.neighbour.state": "Up", "isis.neighbour.system.type": "L1L2 Intermediate System"}, {"isis.neighbour.protocol": "IPV4"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}}