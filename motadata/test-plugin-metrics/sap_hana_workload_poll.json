{"************": {"result": {"correlation.metrics": ["sap.hana.job", "sap.hana.expensive.query"], "sap.hana.job": [{"sap.hana.job.host": "vhcalhdbdb", "sap.hana.job.connection.id": "400798", "sap.hana.job.schema.name": "sample_schema", "sap.hana.job.start.time": "JUN 15, 2014 6:25:34.234", "sap.hana.job.name": "Column table reloading on startup_400798", "sap.hana.job.current.progress": "123"}], "sap.hana.expensive.query": [{"sap.hana.expensive.query.host": "vhcalhdbdb", "sap.hana.expensive.query.connection.id": "400798", "sap.hana.expensive.query.id": "1234567890123456", "sap.hana.expensive.query.duration.ms": 3301.138, "sap.hana.expensive.query.records": 4311248, "sap.hana.expensive.query.memory.bytes": 40000000, "sap.hana.expensive.query.cpu.time.ms": "34981235.667", "sap.hana.expensive.query.db.user": "SYSTEM", "sap.hana.expensive.query.operation": "AGGREGATED_EXECUTION", "sap.hana.expensive.query.text": "SELECT SUM (\"XVAL\"), SUM(YVAL), COUNT(*) FROM \"_SYS_BIC\".\"PERFORMANCE/CA_PERF_GRA_001\"", "sap.hana.expensive.query.error.code": 20345, "sap.hana.expensive.query.error": "error executing statement due to high utilization of cpu and memory"}]}, "errors": []}}