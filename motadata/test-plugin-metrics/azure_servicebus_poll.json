{"127.0.0.1": {"215": {"metadata.fields": {"name": "servbsq12", "type": "srvcbus"}}, "_type": "1", "cloud.client.id": "aaa130f3-5ed7-404b-87f5-90a5a42750ea", "cloud.secret.key": "**********************************", "cloud.tenant.id": "5b4acec3-2592-4187-9489-98c654cc6c87", "cloud.type": "Azure Cloud", "credential.profile.name": "Azure-Cloud-Test1655203790900", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 24, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "Azure-Cloud-Test-1655203790903", "discovery.object.type": "Azure Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:20:25.670 pm 14/06/2022", "discovery.total.objects": 24, "errors": [], "event.id": 176993700500108, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Azure Service Bus", "metric.object": ***************, "metric.plugin": "azureservicebus", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Azure Service Bus", "object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:35.940 pm 14/06/2022", "object.custom.fields": {"***************": "srvcbus", "***************": "servbsq12"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 67, "object.name": "servbsq12(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.state": "ENABLE", "object.target": "servbsq12(cloud-shell-storage-centralindia)", "object.type": "Azure Service Bus", "object.vendor": "Azure Cloud", "plugin.engine": "go", "plugin.id": 215, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"215": {"metadata.fields": {"name": "servbsq12", "type": "srvcbus"}}, "azure.location": "South Central US", "azure.name": "servbsq12", "azure.psb.abandoned.messages": 0, "azure.psb.active.connections": 0, "azure.psb.active.messages": 0, "azure.psb.bytes": 0, "azure.psb.closed.connections": 0, "azure.psb.completed.messages": 0, "azure.psb.dead.lettered.messages": 0, "azure.psb.incoming.messages": 0, "azure.psb.incoming.requests": 1, "azure.psb.messages": 0, "azure.psb.opened.connections": 0, "azure.psb.outgoing.messages": 0, "azure.psb.scheduled.messages": 0, "azure.psb.server.errors": 0, "azure.psb.successful.requests": 1, "azure.psb.throttled.requests": 0, "azure.psb.user.errors": 0, "azure.service.queues": [{"azure.service.active.messages": 0, "azure.service.dead.letter.messages": 0, "azure.service.default.message.ttl": "1.00:00:00", "azure.service.max.deliveries": 2, "azure.service.max.in.bytes": 1073741824, "azure.service.messages": 0, "azure.service.queue.name": "demoq1", "azure.service.scheduled.messages": 0, "azure.service.size.in.bytes": 0, "azure.service.status": "Active", "azure.service.transferred.dead.letter.messages": 0, "azure.service.transferred.messages": 0}, {"azure.service.active.messages": 0, "azure.service.dead.letter.messages": 0, "azure.service.default.message.ttl": "1.00:00:00", "azure.service.max.deliveries": 2, "azure.service.max.in.bytes": 1073741824, "azure.service.messages": 0, "azure.service.queue.name": "sampleq1", "azure.service.scheduled.messages": 0, "azure.service.size.in.bytes": 0, "azure.service.status": "Active", "azure.service.transferred.dead.letter.messages": 0, "azure.service.transferred.messages": 0}], "azure.sku.name": "Basic", "azure.status": "Active", "azure.type": "Microsoft.ServiceBus/namespaces"}, "status": "succeed", "timeout": 60}}