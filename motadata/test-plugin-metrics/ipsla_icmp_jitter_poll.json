{"***********": {"errors": [], "object.ip": "***********", "port": 161, "result": {"ipsla": [{"ipsla": "Jio-************->**********", "ipsla.admin.status": "active", "ipsla.operation.type": "ipslaicmpjitter", "ipsla.owner": "MOTADATA-IP-SLA-368-site2.test2.com", "ipsla.rtt.completion.status": "timeout", "status": "Down"}, {"ipsla": "Vodafone-**********->************", "ipsla.admin.status": "active", "ipsla.avg.jitter.ms": 2, "ipsla.destination.to.source.avg.jitter.ms": 1, "ipsla.destination.to.source.avg.latency.ms": 0, "ipsla.destination.to.source.avg.negative.jitter.ms": 4, "ipsla.destination.to.source.avg.positive.jitter.ms": 4, "ipsla.latency.ms": 4, "ipsla.lost.packets": 0, "ipsla.max.dropped.packets": 0, "ipsla.max.latency.ms": 12, "ipsla.min.dropped.packets": 0, "ipsla.min.latency.ms": 1, "ipsla.operation.type": "ipslaicmpjitter", "ipsla.owner": "MOTADATA-IP-SLA-370-site2.test2.com", "ipsla.rtt.completion.status": "ok", "ipsla.source.to.destination.avg.jitter.ms": 2, "ipsla.source.to.destination.avg.latency.ms": 1, "ipsla.source.to.destination.avg.negative.jitter.ms": 2, "ipsla.source.to.destination.avg.positive.jitter.ms": 3, "ipsla.timeout.packets": 0, "status": "Up"}, {"ipsla": "You-**********->**********", "ipsla.admin.status": "active", "ipsla.avg.jitter.ms": 6, "ipsla.destination.to.source.avg.jitter.ms": 5, "ipsla.destination.to.source.avg.latency.ms": 0, "ipsla.destination.to.source.avg.negative.jitter.ms": 4, "ipsla.destination.to.source.avg.positive.jitter.ms": 4, "ipsla.latency.ms": 14, "ipsla.lost.packets": 0, "ipsla.max.dropped.packets": 0, "ipsla.max.latency.ms": 28, "ipsla.min.dropped.packets": 0, "ipsla.min.latency.ms": 4, "ipsla.operation.type": "ipslaicmpjitter", "ipsla.owner": "MOTADATA-IP-SLA-371-site2.test2.com", "ipsla.rtt.completion.status": "ok", "ipsla.source.to.destination.avg.jitter.ms": 7, "ipsla.source.to.destination.avg.latency.ms": 0, "ipsla.source.to.destination.avg.negative.jitter.ms": 6, "ipsla.source.to.destination.avg.positive.jitter.ms": 9, "ipsla.timeout.packets": 0, "status": "Up"}]}, "snmp.community": "public", "snmp.version": "v2c", "url.protocol": "snmp", "status": "succeed"}}