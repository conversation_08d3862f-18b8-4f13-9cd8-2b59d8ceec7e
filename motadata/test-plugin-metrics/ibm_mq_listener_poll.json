{"************": {"result": {"ibm.mq.listener": [{"ibm.mq.listener": "LISTENER.TCP", "ibm.mq.listener.description": null, "ibm.mq.listener.backlog": 100, "ibm.mq.listener.port": 2424, "ibm.mq.listener.availability": "Up", "ibm.mq.listener.status": "Running", "ibm.mq.listener.sessions": null}]}, "errors": []}, "***********": {"result": {"ibm.mq.listener": [{"ibm.mq.listener": "LISTENER1", "ibm.mq.listener.description": null, "ibm.mq.listener.backlog": 100, "ibm.mq.listener.port": 1419, "ibm.mq.listener.availability": "Up", "ibm.mq.listener.status": "Running", "ibm.mq.listener.sessions": null}]}, "errors": []}}