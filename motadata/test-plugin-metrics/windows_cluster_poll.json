{"************": {"errors": [], "metric.timeout": 60, "object.ip": "************", "password": "Mind@123", "port": 5985, "result": {"windows.cluster.disk.capacity.bytes": 16097738752, "windows.cluster.disk.free.bytes": 15974006784, "windows.cluster.disk.used.bytes": 123731968, "windows.cluster.disk.used.percent": 0.77, "windows.cluster.ip": "************", "windows.cluster.network": [{"windows.cluster.network": "hyper-v02", "windows.cluster.network.received.bytes": 23647737598, "windows.cluster.network.received.bytes.per.sec": 0, "windows.cluster.network.received.messages": 154633041, "windows.cluster.network.received.messages.per.sec": 0, "windows.cluster.network.reconnects": 0, "windows.cluster.network.sent.bytes": 23647737598, "windows.cluster.network.sent.bytes.per.sec": 0, "windows.cluster.network.sent.messages": 157113173, "windows.cluster.network.sent.messages.per.sec": 0, "windows.cluster.normal.message.queue.length": 0, "windows.cluster.normal.messages.per.sec": 0, "windows.cluster.unacknowledged.message.queue.length": 1, "windows.cluster.unacknowledged.messages.per.sec": 0, "windows.cluster.urgent.message.queue.length": 0, "windows.cluster.urgent.messages.per.sec": 0}, {"windows.cluster.network": "hyper-v01", "windows.cluster.network.received.bytes": 17066863794, "windows.cluster.network.received.bytes.per.sec": 0, "windows.cluster.network.received.messages": 98047602, "windows.cluster.network.received.messages.per.sec": 0, "windows.cluster.network.reconnects": 3, "windows.cluster.network.sent.bytes": 15014803630, "windows.cluster.network.sent.bytes.per.sec": 0, "windows.cluster.network.sent.messages": 101097595, "windows.cluster.network.sent.messages.per.sec": 0, "windows.cluster.normal.message.queue.length": 0, "windows.cluster.normal.messages.per.sec": 0, "windows.cluster.unacknowledged.message.queue.length": 0, "windows.cluster.unacknowledged.messages.per.sec": 0, "windows.cluster.urgent.message.queue.length": 0, "windows.cluster.urgent.messages.per.sec": 0}], "windows.cluster.networks": 2, "windows.cluster.node": [{"windows.cluster.node": "************", "windows.cluster.node.state": "Up"}], "windows.cluster.node.network": [{"windows.cluster.node.network": "Cluster Network 1", "windows.cluster.node.network.address": "**********", "windows.cluster.node.network.role": "3", "windows.cluster.node.network.state": "Up"}, {"windows.cluster.node.network": "Cluster Network 2", "windows.cluster.node.network.address": "", "windows.cluster.node.network.role": "1", "windows.cluster.node.network.state": "Up"}], "windows.cluster.nodes": 2, "windows.cluster.offline.resource.groups": 0, "windows.cluster.offline.resources": 0, "windows.cluster.online.groups": 1, "windows.cluster.online.resource.groups": 5, "windows.cluster.online.resources": 5, "windows.cluster.outstanding.messages": 0, "windows.cluster.quorum.path": "E", "windows.cluster.quorum.resource": "Cluster Disk 2", "windows.cluster.quorum.type": "NodeAndDiskMajority", "windows.cluster.resource": [{"windows.cluster.online.resources": 0, "windows.cluster.resource": "virtual machine", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131803, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "network address translator", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131803, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 1, "windows.cluster.resource": "ipv6 address", "windows.cluster.resource.controls": 560435, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 2, "windows.cluster.resource.type.controls": 131825, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "file server", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131803, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "disjoint ipv6 address", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131803, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "disjoint ipv4 address", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131803, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "dhcp service", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131819, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 1, "windows.cluster.resource": "network name", "windows.cluster.resource.controls": 770929, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131821, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "file share quorum witness", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131803, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "wins service", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131820, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "task scheduler", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131803, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "storage pool", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131811, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "sql server filestream share", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131819, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "message queue triggers", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 2, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "ipv6 tunnel address", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131819, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 1, "windows.cluster.resource": "ip address", "windows.cluster.resource.controls": 869450, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 7733, "windows.cluster.resource.type.controls": 131839, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "scale out file server", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131803, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "message queuing", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 2, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "generic application", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131819, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "distributed file system", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131821, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 5, "windows.cluster.resource": "_total", "windows.cluster.resource.controls": 24549693, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 7735, "windows.cluster.resource.type.controls": 4086181, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "isns cluster resource", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131819, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "virtual machine replication broker", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131802, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "sql server availability group", "windows.cluster.resource.controls": 81, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131819, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "hyper-v network virtualization provider address", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131803, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 2, "windows.cluster.resource": "physical disk", "windows.cluster.resource.controls": 22348798, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131808, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "distributed network name", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131803, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "virtual machine configuration", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131802, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "distributed transaction coordinator", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131821, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "dfs replicated folder", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131803, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "network file system", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131803, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "iscsi target server", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131819, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "generic service", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131819, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "generic script", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 131819, "windows.cluster.resource.type.controls.per.sec": 0}], "windows.cluster.rhs.processes": 4, "windows.cluster.rhs.restarts": 0, "windows.cluster.sent.messages": 97944320, "windows.cluster.sent.messages.per.sec": 0}, "username": "Administrator"}, "************": {"errors": [], "metric.timeout": 20, "object.ip": "************", "password": "Mind@#123", "port": 5985, "result": {"windows.cluster.disk.capacity.bytes": 0, "windows.cluster.disk.free.bytes": 0, "windows.cluster.disk.used.bytes": 0, "windows.cluster.disk.used.percent": 0, "windows.cluster.ip": "************", "windows.cluster.network": [{"windows.cluster.network": "win-1l0gr56tgf", "windows.cluster.network.received.bytes": 1844217, "windows.cluster.network.received.bytes.per.sec": 0, "windows.cluster.network.received.messages": 6468, "windows.cluster.network.received.messages.per.sec": 0, "windows.cluster.network.reconnects": 0, "windows.cluster.network.sent.bytes": 1844217, "windows.cluster.network.sent.bytes.per.sec": 0, "windows.cluster.network.sent.messages": 12404, "windows.cluster.network.sent.messages.per.sec": 0, "windows.cluster.normal.message.queue.length": 0, "windows.cluster.normal.messages.per.sec": 0, "windows.cluster.unacknowledged.message.queue.length": 1, "windows.cluster.unacknowledged.messages.per.sec": 0, "windows.cluster.urgent.message.queue.length": 0, "windows.cluster.urgent.messages.per.sec": 0}, {"windows.cluster.network": "win-1l0gr7iggbd", "windows.cluster.network.received.bytes": 12972226, "windows.cluster.network.received.bytes.per.sec": 0, "windows.cluster.network.received.messages": 28438, "windows.cluster.network.received.messages.per.sec": 0, "windows.cluster.network.reconnects": 0, "windows.cluster.network.sent.bytes": 8952190, "windows.cluster.network.sent.bytes.per.sec": 0, "windows.cluster.network.sent.messages": 49410, "windows.cluster.network.sent.messages.per.sec": 0, "windows.cluster.normal.message.queue.length": 0, "windows.cluster.normal.messages.per.sec": 0, "windows.cluster.unacknowledged.message.queue.length": 0, "windows.cluster.unacknowledged.messages.per.sec": 0, "windows.cluster.urgent.message.queue.length": 0, "windows.cluster.urgent.messages.per.sec": 0}], "windows.cluster.networks": 1, "windows.cluster.node": [{"windows.cluster.node": "win-1l0gr56tgf.hyperv2008.local", "windows.cluster.node.state": "Up"}, {"windows.cluster.node": "win-1l0gr7iggbd.hyperv2008.local", "windows.cluster.node.state": "Up"}], "windows.cluster.node.network": [{"windows.cluster.node.network": "Cluster Network 1", "windows.cluster.node.network.address": "**********", "windows.cluster.node.network.role": "3", "windows.cluster.node.network.state": "Up"}], "windows.cluster.nodes": 2, "windows.cluster.online.groups": 0, "windows.cluster.online.resource.groups": 5, "windows.cluster.online.resources": 5, "windows.cluster.outstanding.messages": 0, "windows.cluster.quorum.path": "E", "windows.cluster.quorum.resource": "Cluster Disk 2", "windows.cluster.quorum.type": "NodeAndDiskMajority", "windows.cluster.resource": [{"windows.cluster.online.resources": 0, "windows.cluster.resource": "generic script", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 669, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "generic application", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 669, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "dhcp service", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 669, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "volume shadow copy service task", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 669, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "print spooler", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 669, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "message queuing", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 2, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "ipv6 tunnel address", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 669, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "_total", "windows.cluster.resource.controls": 1, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 14503, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "wins service", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 669, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "physical disk", "windows.cluster.resource.controls": 1, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 1117, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "network name", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 669, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "message queue triggers", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 2, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "isns cluster resource", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 669, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "ip address", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 669, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "file share quorum witness", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 669, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "file server", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 669, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "virtual machine", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 669, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "nfs share", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 669, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "dfs replicated folder", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 669, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "generic service", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 669, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "distributed transaction coordinator", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 671, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "distributed file system", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 669, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "virtual machine configuration", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 669, "windows.cluster.resource.type.controls.per.sec": 0}, {"windows.cluster.online.resources": 0, "windows.cluster.resource": "ipv6 address", "windows.cluster.resource.controls": 0, "windows.cluster.resource.controls.per.sec": 0, "windows.cluster.resource.failure.access.violations": 0, "windows.cluster.resource.failure.deadlocks": 0, "windows.cluster.resource.failures": 0, "windows.cluster.resource.type.controls": 669, "windows.cluster.resource.type.controls.per.sec": 0}], "windows.cluster.rhs.processes": 3, "windows.cluster.rhs.restarts": 0, "windows.cluster.sent.messages": 19792, "windows.cluster.sent.messages.per.sec": 0}, "username": "hyperv2008.local\\Administrator"}}