{"127.0.0.1": {"216": {"metadata.fields": {"name": "appgateway123", "type": "appgateway"}}, "_type": "1", "cloud.client.id": "aaa130f3-5ed7-404b-87f5-90a5a42750ea", "cloud.secret.key": "**********************************", "cloud.tenant.id": "5b4acec3-2592-4187-9489-98c654cc6c87", "cloud.type": "Azure Cloud", "credential.profile.name": "Azure-Cloud-Test1655203790900", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 24, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "Azure-Cloud-Test-1655203790903", "discovery.object.type": "Azure Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:20:25.670 pm 14/06/2022", "discovery.total.objects": 24, "errors": [{"error": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: BackendConnectTime, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\"", "error.code": "MD031", "message": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: BackendConnectTime, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\""}, {"error": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: BackendFirstByteResponseTime, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\"", "error.code": "MD031", "message": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: BackendFirstByteResponseTime, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\""}, {"error": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: BackendLastByteResponseTime, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\"", "error.code": "MD031", "message": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: BackendLastByteResponseTime, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\""}, {"error": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: ApplicationGatewayTotalTime, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\"", "error.code": "MD031", "message": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: ApplicationGatewayTotalTime, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\""}, {"error": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: ClientRtt, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\"", "error.code": "MD031", "message": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: ClientRtt, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\""}, {"error": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: NewConnectionsPerSecond, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\"", "error.code": "MD031", "message": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: NewConnectionsPerSecond, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\""}, {"error": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: BytesReceived, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\"", "error.code": "MD031", "message": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: BytesReceived, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\""}, {"error": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: BytesSent, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\"", "error.code": "MD031", "message": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: BytesSent, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\""}, {"error": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: TlsProtocol, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\"", "error.code": "MD031", "message": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: TlsProtocol, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\""}, {"error": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: ComputeUnits, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\"", "error.code": "MD031", "message": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: ComputeUnits, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\""}, {"error": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: CapacityUnits, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\"", "error.code": "MD031", "message": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: CapacityUnits, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\""}, {"error": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: MatchedCount, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\"", "error.code": "MD031", "message": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: MatchedCount, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\""}, {"error": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: EstimatedBilledCapacityUnits, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\"", "error.code": "MD031", "message": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: EstimatedBilledCapacityUnits, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\""}, {"error": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: FixedBillableCapacityUnits, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\"", "error.code": "MD031", "message": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: FixedBillableCapacityUnits, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\""}, {"error": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: BlockedCount, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\"", "error.code": "MD031", "message": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: BlockedCount, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\""}, {"error": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: BlockedReqCount, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\"", "error.code": "MD031", "message": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Network, resource Type: applicationGateways, metric: BlockedReqCount, Valid metrics: Throughput,UnhealthyHostCount,HealthyHostCount,TotalRequests,AvgRequestCountPerHealthyHost,FailedRequests,ResponseStatus,CurrentConnections,CpuUtilization\""}], "event.id": 176993700500112, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Azure Application Gateway", "metric.object": ***************, "metric.plugin": "azureapplicationgateway", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Azure Application Gateway", "object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:35.729 pm 14/06/2022", "object.custom.fields": {"***************": "appgateway", "***************": "appgateway123"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 47, "object.name": "appgateway123(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.state": "ENABLE", "object.target": "appgateway123(cloud-shell-storage-centralindia)", "object.type": "Azure Application Gateway", "object.vendor": "Azure Cloud", "plugin.engine": "go", "plugin.id": 216, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"216": {"metadata.fields": {"name": "appgateway123", "type": "appgateway"}}, "azure.application.gateway.active.connections": 0, "azure.application.gateway.cpu.percent": 0, "azure.application.gateway.failed.requests": 0, "azure.application.gateway.healthy.hosts": 0, "azure.application.gateway.requests.per.healthy.host": 0, "azure.application.gateway.requests.rate": 0, "azure.application.gateway.response.status": 1, "azure.application.gateway.throughput.bytes.per.sec": 0, "azure.application.gateway.unhealthy.hosts": 1, "azure.etag": "W/\"bc79d58f-7cb2-4ea1-b51b-62a069b09e91\"", "azure.location": "southcentralus", "azure.name": "appgateway123", "azure.sku.name": "Standard_Small", "azure.status": "Succeeded", "azure.type": "Microsoft.Network/applicationGateways"}, "status": "succeed", "timeout": 60}}