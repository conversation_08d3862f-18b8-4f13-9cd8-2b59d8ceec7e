{"***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"ip.route": [{"ip.route": "***********", "ip.route.interface.index": 4, "ip.route.last.updated": " 40 days 1 hours 17 minutes 52 seconds", "ip.route.last.updated.sec": 3460672, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 2, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 2, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "20", "ip.route.next.hop": "***********", "ip.route.protocol": "IS-IS", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 8, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "**********", "ip.route.interface.index": 4, "ip.route.last.updated": " 40 days 1 hours 17 minutes 52 seconds", "ip.route.last.updated.sec": 3460672, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 2, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "0.0.0.0", "ip.route.interface.index": 0, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "0.0.0.0", "ip.route.type": "Indirect", "ip.routing.type": "Static and Default"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "10", "ip.route.next.hop": "***********", "ip.route.protocol": "IS-IS", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Juniper Networks", "port": 161, "rediscover.job": "Network Metric", "result": {"ip.route": [{"ip.route": "***********", "ip.route.interface.index": 0, "ip.route.last.updated": " 34 days 23 hours 35 minutes 35 seconds", "ip.route.last.updated.sec": 3022535, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "**********", "ip.route.interface.index": 529, "ip.route.last.updated": " 34 days 23 hours 35 minutes 35 seconds", "ip.route.last.updated.sec": 3022535, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct", "ip.routing.type": "Static"}, {"ip.route": "***********", "ip.route.interface.index": 531, "ip.route.last.updated": " 34 days 19 hours 57 minutes 15 seconds", "ip.route.last.updated.sec": 3009435, "ip.route.metric": "20", "ip.route.next.hop": "***********", "ip.route.protocol": "IS-IS", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 531, "ip.route.last.updated": " 34 days 23 hours 35 minutes 35 seconds", "ip.route.last.updated.sec": 3022535, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct", "ip.routing.type": "Static"}, {"ip.route": "***********", "ip.route.interface.index": 16, "ip.route.last.updated": " 34 days 23 hours 40 minutes 34 seconds", "ip.route.last.updated.sec": 3022834, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct", "ip.routing.type": "Static"}, {"ip.route": "***********", "ip.route.interface.index": 0, "ip.route.last.updated": " 34 days 23 hours 35 minutes 35 seconds", "ip.route.last.updated.sec": 3022535, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "0.0.0.0", "ip.route.interface.index": 529, "ip.route.last.updated": " 34 days 23 hours 35 minutes 35 seconds", "ip.route.last.updated.sec": 3022535, "ip.route.metric": "-1", "ip.route.next.hop": "***********", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "0.0.0.0", "ip.route.type": "Indirect", "ip.routing.type": "Static and Default"}, {"ip.route": "***********", "ip.route.interface.index": 531, "ip.route.last.updated": " 34 days 19 hours 57 minutes 15 seconds", "ip.route.last.updated.sec": 3009435, "ip.route.metric": "30", "ip.route.next.hop": "***********", "ip.route.protocol": "IS-IS", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 531, "ip.route.last.updated": " 34 days 19 hours 57 minutes 15 seconds", "ip.route.last.updated.sec": 3009435, "ip.route.metric": "20", "ip.route.next.hop": "***********", "ip.route.protocol": "IS-IS", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"ip.route": [{"ip.route": "***********", "ip.route.interface.index": 8, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "************", "ip.route.interface.index": 0, "ip.route.last.updated": " 5 days 2 hours 1 minutes 58 seconds", "ip.route.last.updated.sec": 439318, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "BGP", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "0.0.0.0", "ip.route.interface.index": 0, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "0.0.0.0", "ip.route.type": "Indirect", "ip.routing.type": "Static and Default"}, {"ip.route": "***********", "ip.route.interface.index": 0, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "BGP", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "**********", "ip.route.interface.index": 4, "ip.route.last.updated": " 40 days 1 hours 14 minutes 19 seconds", "ip.route.last.updated.sec": 3460459, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 4, "ip.route.last.updated": " 40 days 1 hours 14 minutes 19 seconds", "ip.route.last.updated.sec": 3460459, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 5, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 5, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Juniper Networks", "port": 161, "rediscover.job": "Network Metric", "result": {"ip.route": [{"ip.route": "0.0.0.0", "ip.route.interface.index": 537, "ip.route.last.updated": " 22 days 4 hours 11 minutes 52 seconds", "ip.route.last.updated.sec": 1915912, "ip.route.metric": "-1", "ip.route.next.hop": "***********", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "0.0.0.0", "ip.route.type": "Indirect", "ip.routing.type": "Static and Default"}, {"ip.route": "***********", "ip.route.interface.index": 536, "ip.route.last.updated": " 22 days 4 hours 11 minutes 48 seconds", "ip.route.last.updated.sec": 1915908, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "BGP", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "**********", "ip.route.interface.index": 537, "ip.route.last.updated": " 22 days 4 hours 11 minutes 52 seconds", "ip.route.last.updated.sec": 1915912, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct", "ip.routing.type": "Static"}, {"ip.route": "***********", "ip.route.interface.index": 536, "ip.route.last.updated": " 22 days 4 hours 11 minutes 52 seconds", "ip.route.last.updated.sec": 1915912, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct", "ip.routing.type": "Static"}, {"ip.route": "***********", "ip.route.interface.index": 536, "ip.route.last.updated": " 22 days 4 hours 11 minutes 48 seconds", "ip.route.last.updated.sec": 1915908, "ip.route.metric": "-1", "ip.route.next.hop": "***********", "ip.route.protocol": "BGP", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 0, "ip.route.last.updated": " 22 days 4 hours 11 minutes 52 seconds", "ip.route.last.updated.sec": 1915912, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 536, "ip.route.last.updated": " 22 days 4 hours 11 minutes 48 seconds", "ip.route.last.updated.sec": 1915908, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "BGP", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 0, "ip.route.last.updated": " 22 days 4 hours 11 minutes 52 seconds", "ip.route.last.updated.sec": 1915912, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "************", "ip.route.interface.index": 536, "ip.route.last.updated": " 5 days 2 hours 1 minutes 35 seconds", "ip.route.last.updated.sec": 439295, "ip.route.metric": "-1", "ip.route.next.hop": "***********", "ip.route.protocol": "BGP", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "**********": {"errors": [], "metric.timeout": 60, "object.ip": "**********", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"ip.route": [{"ip.route": "***********", "ip.route.interface.index": 54, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 53, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "**********", "ip.route.interface.index": 58, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 59, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "0.0.0.0", "ip.route.interface.index": 0, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "0.0.0.0", "ip.route.type": "Indirect", "ip.routing.type": "Static and Default"}, {"ip.route": "**********", "ip.route.interface.index": 62, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "**********", "ip.route.interface.index": 63, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "**********", "ip.route.interface.index": 56, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "**********", "ip.route.interface.index": 57, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "**********", "ip.route.interface.index": 58, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 59, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "**********", "ip.route.interface.index": 62, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "**********", "ip.route.interface.index": 54, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 61, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 52, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "**********", "ip.route.interface.index": 57, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 61, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "**********", "ip.route.interface.index": 63, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***********", "ip.route.type": "Direct"}, {"ip.route": "**********", "ip.route.interface.index": 56, "ip.route.last.updated": " 49 days 17 hours 2 minutes 47 seconds", "ip.route.last.updated.sec": 4294967, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"ip.route": [{"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "2", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 20 days 22 hours 38 minutes 11 seconds", "ip.route.last.updated.sec": 1809491, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 20 days 22 hours 38 minutes 11 seconds", "ip.route.last.updated.sec": 1809491, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "1", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "**********", "ip.route.interface.index": 2, "ip.route.last.updated": " 0 day 0 hour 53 minutes 25 seconds", "ip.route.last.updated.sec": 3205, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "************", "ip.route.interface.index": 4, "ip.route.last.updated": " 0 day 0 hour 2 minutes 55 seconds", "ip.route.last.updated.sec": 175, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "0.0.0.0", "ip.route.interface.index": 0, "ip.route.last.updated": " 20 days 22 hours 38 minutes 14 seconds", "ip.route.last.updated.sec": 1809494, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "0.0.0.0", "ip.route.type": "Indirect", "ip.routing.type": "Static and Default"}, {"ip.route": "**********", "ip.route.interface.index": 0, "ip.route.last.updated": " 20 days 22 hours 38 minutes 14 seconds", "ip.route.last.updated.sec": 1809494, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect", "ip.routing.type": "Static"}, {"ip.route": "***********", "ip.route.interface.index": 2, "ip.route.last.updated": " 0 day 0 hour 53 minutes 25 seconds", "ip.route.last.updated.sec": 3205, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 6, "ip.route.last.updated": " 20 days 22 hours 38 minutes 16 seconds", "ip.route.last.updated.sec": 1809496, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "2", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "3", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 20 days 22 hours 38 minutes 13 seconds", "ip.route.last.updated.sec": 1809493, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 20 days 22 hours 38 minutes 13 seconds", "ip.route.last.updated.sec": 1809493, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "************", "ip.route.interface.index": 4, "ip.route.last.updated": " 0 day 0 hour 2 minutes 55 seconds", "ip.route.last.updated.sec": 175, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"ip.route": [{"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 22 days 3 hours 11 minutes 45 seconds", "ip.route.last.updated.sec": 1912305, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 22 days 3 hours 11 minutes 45 seconds", "ip.route.last.updated.sec": 1912305, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "2", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect"}, {"ip.route": "************", "ip.route.interface.index": 4, "ip.route.last.updated": " 0 day 0 hour 0 minute  22 seconds", "ip.route.last.updated.sec": 22, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 6, "ip.route.last.updated": " 22 days 3 hours 11 minutes 47 seconds", "ip.route.last.updated.sec": 1912307, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "**********", "ip.route.interface.index": 2, "ip.route.last.updated": " 0 day 2 hours 44 minutes 25 seconds", "ip.route.last.updated.sec": 9865, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 2, "ip.route.last.updated": " 0 day 2 hours 44 minutes 25 seconds", "ip.route.last.updated.sec": 9865, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 22 days 3 hours 11 minutes 43 seconds", "ip.route.last.updated.sec": 1912303, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "************", "ip.route.interface.index": 4, "ip.route.last.updated": " 0 day 0 hour 0 minute  22 seconds", "ip.route.last.updated.sec": 22, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 22 days 3 hours 11 minutes 43 seconds", "ip.route.last.updated.sec": 1912303, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "0.0.0.0", "ip.route.interface.index": 0, "ip.route.last.updated": " 22 days 3 hours 11 minutes 46 seconds", "ip.route.last.updated.sec": 1912306, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "0.0.0.0", "ip.route.type": "Indirect", "ip.routing.type": "Static and Default"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "2", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "2", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "2", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"ip.route": [{"ip.route": "0.0.0.0", "ip.route.interface.index": 0, "ip.route.last.updated": " 22 days 3 hours 13 minutes 32 seconds", "ip.route.last.updated.sec": 1912412, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "0.0.0.0", "ip.route.type": "Indirect", "ip.routing.type": "Static and Default"}, {"ip.route": "**********", "ip.route.interface.index": 6, "ip.route.last.updated": " 22 days 3 hours 13 minutes 33 seconds", "ip.route.last.updated.sec": 1912413, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "2", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 5, "ip.route.last.updated": " 22 days 3 hours 13 minutes 34 seconds", "ip.route.last.updated.sec": 1912414, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "**********", "ip.route.interface.index": 2, "ip.route.last.updated": " 0 day 0 hour 59 minutes 32 seconds", "ip.route.last.updated.sec": 3572, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 22 days 3 hours 13 minutes 30 seconds", "ip.route.last.updated.sec": 1912410, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "**********", "ip.route.interface.index": 6, "ip.route.last.updated": " 22 days 3 hours 13 minutes 34 seconds", "ip.route.last.updated.sec": 1912414, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "2", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "3", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "3", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 22 days 3 hours 13 minutes 30 seconds", "ip.route.last.updated.sec": 1912410, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "**********", "ip.route.interface.index": 0, "ip.route.last.updated": " 22 days 3 hours 13 minutes 32 seconds", "ip.route.last.updated.sec": 1912412, "ip.route.metric": "0", "ip.route.next.hop": "*************", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect", "ip.routing.type": "Static"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "3", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 2, "ip.route.last.updated": " 0 day 0 hour 59 minutes 32 seconds", "ip.route.last.updated.sec": 3572, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Juniper Networks", "port": 161, "rediscover.job": "Network Metric", "result": {"ip.route": [{"ip.route": "***********", "ip.route.interface.index": 16, "ip.route.last.updated": " 22 days 3 hours 4 minutes 28 seconds", "ip.route.last.updated.sec": 1911868, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct", "ip.routing.type": "Static"}, {"ip.route": "**********", "ip.route.interface.index": 117, "ip.route.last.updated": " 22 days 3 hours 4 minutes 28 seconds", "ip.route.last.updated.sec": 1911868, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct", "ip.routing.type": "Static"}, {"ip.route": "***********", "ip.route.interface.index": 24, "ip.route.last.updated": " 20 days 22 hours 38 minutes 29 seconds", "ip.route.last.updated.sec": 1809509, "ip.route.metric": "2", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 24, "ip.route.last.updated": " 20 days 22 hours 38 minutes 24 seconds", "ip.route.last.updated.sec": 1809504, "ip.route.metric": "3", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 0, "ip.route.last.updated": " 22 days 3 hours 4 minutes 28 seconds", "ip.route.last.updated.sec": 1911868, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "*********", "ip.route.interface.index": 0, "ip.route.last.updated": " 22 days 3 hours 4 minutes 31 seconds", "ip.route.last.updated.sec": 1911871, "ip.route.metric": "1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Other"}, {"ip.route": "***********", "ip.route.interface.index": 24, "ip.route.last.updated": " 20 days 22 hours 38 minutes 29 seconds", "ip.route.last.updated.sec": 1809509, "ip.route.metric": "2", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 24, "ip.route.last.updated": " 20 days 22 hours 38 minutes 24 seconds", "ip.route.last.updated.sec": 1809504, "ip.route.metric": "3", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 0, "ip.route.last.updated": " 22 days 3 hours 4 minutes 28 seconds", "ip.route.last.updated.sec": 1911868, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 24, "ip.route.last.updated": " 22 days 3 hours 4 minutes 28 seconds", "ip.route.last.updated.sec": 1911868, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct", "ip.routing.type": "Static"}, {"ip.route": "0.0.0.0", "ip.route.interface.index": 117, "ip.route.last.updated": " 22 days 3 hours 4 minutes 27 seconds", "ip.route.last.updated.sec": 1911867, "ip.route.metric": "-1", "ip.route.next.hop": "***********", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "0.0.0.0", "ip.route.type": "Indirect", "ip.routing.type": "Static and Default"}, {"ip.route": "***********", "ip.route.interface.index": 24, "ip.route.last.updated": " 20 days 22 hours 38 minutes 24 seconds", "ip.route.last.updated.sec": 1809504, "ip.route.metric": "4", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"ip.route": [{"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "20", "ip.route.next.hop": "***********", "ip.route.protocol": "IS-IS", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "**********", "ip.route.interface.index": 3, "ip.route.last.updated": " 4 days 16 hours 55 minutes 48 seconds", "ip.route.last.updated.sec": 406548, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 5 days 4 hours 24 minutes 41 seconds", "ip.route.last.updated.sec": 447881, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 2, "ip.route.last.updated": " 5 days 4 hours 24 minutes 40 seconds", "ip.route.last.updated.sec": 447880, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 2, "ip.route.last.updated": " 5 days 4 hours 24 minutes 40 seconds", "ip.route.last.updated.sec": 447880, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "0.0.0.0", "ip.route.interface.index": 0, "ip.route.last.updated": " 5 days 4 hours 24 minutes 42 seconds", "ip.route.last.updated.sec": 447882, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "0.0.0.0", "ip.route.type": "Indirect", "ip.routing.type": "Static and Default"}, {"ip.route": "***********", "ip.route.interface.index": 5, "ip.route.last.updated": " 5 days 4 hours 24 minutes 43 seconds", "ip.route.last.updated.sec": 447883, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 2, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "10", "ip.route.next.hop": "***********", "ip.route.protocol": "IS-IS", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 4 days 16 hours 55 minutes 48 seconds", "ip.route.last.updated.sec": 406548, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 5 days 4 hours 24 minutes 41 seconds", "ip.route.last.updated.sec": 447881, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"ip.route": [{"ip.route": "***********", "ip.route.interface.index": 0, "ip.route.last.updated": " 5 days 4 hours 43 minutes 15 seconds", "ip.route.last.updated.sec": 448995, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 24, "ip.route.last.updated": " 5 days 4 hours 43 minutes 0 second", "ip.route.last.updated.sec": 448980, "ip.route.metric": "20", "ip.route.next.hop": "***********", "ip.route.protocol": "IS-IS", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 0, "ip.route.last.updated": " 5 days 4 hours 43 minutes 15 seconds", "ip.route.last.updated.sec": 448995, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "0.0.0.0", "ip.route.interface.index": 117, "ip.route.last.updated": " 5 days 4 hours 43 minutes 15 seconds", "ip.route.last.updated.sec": 448995, "ip.route.metric": "-1", "ip.route.next.hop": "***********", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "0.0.0.0", "ip.route.type": "Indirect", "ip.routing.type": "Static and Default"}, {"ip.route": "***********", "ip.route.interface.index": 16, "ip.route.last.updated": " 5 days 4 hours 43 minutes 15 seconds", "ip.route.last.updated.sec": 448995, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct", "ip.routing.type": "Static"}, {"ip.route": "**********", "ip.route.interface.index": 117, "ip.route.last.updated": " 5 days 4 hours 43 minutes 15 seconds", "ip.route.last.updated.sec": 448995, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct", "ip.routing.type": "Static"}, {"ip.route": "***********", "ip.route.interface.index": 24, "ip.route.last.updated": " 5 days 4 hours 43 minutes 15 seconds", "ip.route.last.updated.sec": 448995, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct", "ip.routing.type": "Static"}, {"ip.route": "***********", "ip.route.interface.index": 24, "ip.route.last.updated": " 5 days 4 hours 43 minutes 0 second", "ip.route.last.updated.sec": 448980, "ip.route.metric": "30", "ip.route.next.hop": "***********", "ip.route.protocol": "IS-IS", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 24, "ip.route.last.updated": " 5 days 4 hours 43 minutes 0 second", "ip.route.last.updated.sec": 448980, "ip.route.metric": "20", "ip.route.next.hop": "***********", "ip.route.protocol": "IS-IS", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"ip.route": [{"ip.route": "0.0.0.0", "ip.route.interface.index": 0, "ip.route.last.updated": " 21 days 4 hours 22 minutes 46 seconds", "ip.route.last.updated.sec": 1830166, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "0.0.0.0", "ip.route.type": "Indirect", "ip.routing.type": "Static and Default"}, {"ip.route": "**********", "ip.route.interface.index": 3, "ip.route.last.updated": " 0 day 2 hours 14 minutes 0 second", "ip.route.last.updated.sec": 8040, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 21 days 4 hours 22 minutes 46 seconds", "ip.route.last.updated.sec": 1830166, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 21 days 4 hours 22 minutes 46 seconds", "ip.route.last.updated.sec": 1830166, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 2, "ip.route.last.updated": " 21 days 4 hours 22 minutes 46 seconds", "ip.route.last.updated.sec": 1830166, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 0, "ip.route.last.updated": " 21 days 4 hours 21 minutes 51 seconds", "ip.route.last.updated.sec": 1830111, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "BGP", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 5, "ip.route.last.updated": " 21 days 4 hours 22 minutes 49 seconds", "ip.route.last.updated.sec": 1830169, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 0 day 2 hours 14 minutes 0 second", "ip.route.last.updated.sec": 8040, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 2, "ip.route.last.updated": " 21 days 4 hours 22 minutes 46 seconds", "ip.route.last.updated.sec": 1830166, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "************", "ip.route.interface.index": 0, "ip.route.last.updated": " 6 days 1 hours 32 minutes 19 seconds", "ip.route.last.updated.sec": 523939, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "BGP", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"ip.route": [{"ip.route": "0.0.0.0", "ip.route.interface.index": 0, "ip.route.last.updated": " 21 days 4 hours 22 minutes 46 seconds", "ip.route.last.updated.sec": 1830166, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "0.0.0.0", "ip.route.type": "Indirect", "ip.routing.type": "Static and Default"}, {"ip.route": "**********", "ip.route.interface.index": 3, "ip.route.last.updated": " 0 day 2 hours 14 minutes 0 second", "ip.route.last.updated.sec": 8040, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 21 days 4 hours 22 minutes 46 seconds", "ip.route.last.updated.sec": 1830166, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 21 days 4 hours 22 minutes 46 seconds", "ip.route.last.updated.sec": 1830166, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 2, "ip.route.last.updated": " 21 days 4 hours 22 minutes 46 seconds", "ip.route.last.updated.sec": 1830166, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 0, "ip.route.last.updated": " 21 days 4 hours 21 minutes 51 seconds", "ip.route.last.updated.sec": 1830111, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "BGP", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 5, "ip.route.last.updated": " 21 days 4 hours 22 minutes 49 seconds", "ip.route.last.updated.sec": 1830169, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 0 day 2 hours 14 minutes 0 second", "ip.route.last.updated.sec": 8040, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 2, "ip.route.last.updated": " 21 days 4 hours 22 minutes 46 seconds", "ip.route.last.updated.sec": 1830166, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "************", "ip.route.interface.index": 0, "ip.route.last.updated": " 6 days 1 hours 32 minutes 19 seconds", "ip.route.last.updated.sec": 523939, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "BGP", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Juniper Networks", "port": 161, "rediscover.job": "Network Metric", "result": {"ip.route": [{"ip.route": "***********", "ip.route.interface.index": 16, "ip.route.last.updated": " 22 days 3 hours 4 minutes 28 seconds", "ip.route.last.updated.sec": 1911868, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct", "ip.routing.type": "Static"}, {"ip.route": "**********", "ip.route.interface.index": 117, "ip.route.last.updated": " 22 days 3 hours 4 minutes 28 seconds", "ip.route.last.updated.sec": 1911868, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct", "ip.routing.type": "Static"}, {"ip.route": "***********", "ip.route.interface.index": 24, "ip.route.last.updated": " 20 days 22 hours 38 minutes 29 seconds", "ip.route.last.updated.sec": 1809509, "ip.route.metric": "2", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 24, "ip.route.last.updated": " 20 days 22 hours 38 minutes 24 seconds", "ip.route.last.updated.sec": 1809504, "ip.route.metric": "3", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 0, "ip.route.last.updated": " 22 days 3 hours 4 minutes 28 seconds", "ip.route.last.updated.sec": 1911868, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "*********", "ip.route.interface.index": 0, "ip.route.last.updated": " 22 days 3 hours 4 minutes 31 seconds", "ip.route.last.updated.sec": 1911871, "ip.route.metric": "1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Other"}, {"ip.route": "***********", "ip.route.interface.index": 24, "ip.route.last.updated": " 20 days 22 hours 38 minutes 29 seconds", "ip.route.last.updated.sec": 1809509, "ip.route.metric": "2", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 24, "ip.route.last.updated": " 20 days 22 hours 38 minutes 24 seconds", "ip.route.last.updated.sec": 1809504, "ip.route.metric": "3", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 0, "ip.route.last.updated": " 22 days 3 hours 4 minutes 28 seconds", "ip.route.last.updated.sec": 1911868, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 24, "ip.route.last.updated": " 22 days 3 hours 4 minutes 28 seconds", "ip.route.last.updated.sec": 1911868, "ip.route.metric": "-1", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct", "ip.routing.type": "Static"}, {"ip.route": "0.0.0.0", "ip.route.interface.index": 117, "ip.route.last.updated": " 22 days 3 hours 4 minutes 27 seconds", "ip.route.last.updated.sec": 1911867, "ip.route.metric": "-1", "ip.route.next.hop": "***********", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "0.0.0.0", "ip.route.type": "Indirect", "ip.routing.type": "Static and Default"}, {"ip.route": "***********", "ip.route.interface.index": 24, "ip.route.last.updated": " 20 days 22 hours 38 minutes 24 seconds", "ip.route.last.updated.sec": 1809504, "ip.route.metric": "4", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"ip.route": [{"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "2", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 20 days 22 hours 38 minutes 11 seconds", "ip.route.last.updated.sec": 1809491, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 20 days 22 hours 38 minutes 11 seconds", "ip.route.last.updated.sec": 1809491, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "1", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "**********", "ip.route.interface.index": 2, "ip.route.last.updated": " 0 day 0 hour 53 minutes 25 seconds", "ip.route.last.updated.sec": 3205, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "************", "ip.route.interface.index": 4, "ip.route.last.updated": " 0 day 0 hour 2 minutes 55 seconds", "ip.route.last.updated.sec": 175, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "0.0.0.0", "ip.route.interface.index": 0, "ip.route.last.updated": " 20 days 22 hours 38 minutes 14 seconds", "ip.route.last.updated.sec": 1809494, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "0.0.0.0", "ip.route.type": "Indirect", "ip.routing.type": "Static and Default"}, {"ip.route": "**********", "ip.route.interface.index": 0, "ip.route.last.updated": " 20 days 22 hours 38 minutes 14 seconds", "ip.route.last.updated.sec": 1809494, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect", "ip.routing.type": "Static"}, {"ip.route": "***********", "ip.route.interface.index": 2, "ip.route.last.updated": " 0 day 0 hour 53 minutes 25 seconds", "ip.route.last.updated.sec": 3205, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 6, "ip.route.last.updated": " 20 days 22 hours 38 minutes 16 seconds", "ip.route.last.updated.sec": 1809496, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "2", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "3", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 20 days 22 hours 38 minutes 13 seconds", "ip.route.last.updated.sec": 1809493, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 20 days 22 hours 38 minutes 13 seconds", "ip.route.last.updated.sec": 1809493, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "************", "ip.route.interface.index": 4, "ip.route.last.updated": " 0 day 0 hour 2 minutes 55 seconds", "ip.route.last.updated.sec": 175, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"ip.route": [{"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 22 days 3 hours 11 minutes 45 seconds", "ip.route.last.updated.sec": 1912305, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 22 days 3 hours 11 minutes 45 seconds", "ip.route.last.updated.sec": 1912305, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "2", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect"}, {"ip.route": "************", "ip.route.interface.index": 4, "ip.route.last.updated": " 0 day 0 hour 0 minute  22 seconds", "ip.route.last.updated.sec": 22, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 6, "ip.route.last.updated": " 22 days 3 hours 11 minutes 47 seconds", "ip.route.last.updated.sec": 1912307, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "**********", "ip.route.interface.index": 2, "ip.route.last.updated": " 0 day 2 hours 44 minutes 25 seconds", "ip.route.last.updated.sec": 9865, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 2, "ip.route.last.updated": " 0 day 2 hours 44 minutes 25 seconds", "ip.route.last.updated.sec": 9865, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 22 days 3 hours 11 minutes 43 seconds", "ip.route.last.updated.sec": 1912303, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "************", "ip.route.interface.index": 4, "ip.route.last.updated": " 0 day 0 hour 0 minute  22 seconds", "ip.route.last.updated.sec": 22, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 22 days 3 hours 11 minutes 43 seconds", "ip.route.last.updated.sec": 1912303, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "0.0.0.0", "ip.route.interface.index": 0, "ip.route.last.updated": " 22 days 3 hours 11 minutes 46 seconds", "ip.route.last.updated.sec": 1912306, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "0.0.0.0", "ip.route.type": "Indirect", "ip.routing.type": "Static and Default"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "2", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "2", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 1, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "2", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"ip.route": [{"ip.route": "0.0.0.0", "ip.route.interface.index": 0, "ip.route.last.updated": " 22 days 3 hours 13 minutes 32 seconds", "ip.route.last.updated.sec": 1912412, "ip.route.metric": "0", "ip.route.next.hop": "***********", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "0.0.0.0", "ip.route.type": "Indirect", "ip.routing.type": "Static and Default"}, {"ip.route": "**********", "ip.route.interface.index": 6, "ip.route.last.updated": " 22 days 3 hours 13 minutes 33 seconds", "ip.route.last.updated.sec": 1912413, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "2", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 5, "ip.route.last.updated": " 22 days 3 hours 13 minutes 34 seconds", "ip.route.last.updated.sec": 1912414, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "**********", "ip.route.interface.index": 2, "ip.route.last.updated": " 0 day 0 hour 59 minutes 32 seconds", "ip.route.last.updated.sec": 3572, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 22 days 3 hours 13 minutes 30 seconds", "ip.route.last.updated.sec": 1912410, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "*************", "ip.route.type": "Direct"}, {"ip.route": "**********", "ip.route.interface.index": 6, "ip.route.last.updated": " 22 days 3 hours 13 minutes 34 seconds", "ip.route.last.updated.sec": 1912414, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "2", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "3", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "3", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 22 days 3 hours 13 minutes 30 seconds", "ip.route.last.updated.sec": 1912410, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}, {"ip.route": "**********", "ip.route.interface.index": 0, "ip.route.last.updated": " 22 days 3 hours 13 minutes 32 seconds", "ip.route.last.updated.sec": 1912412, "ip.route.metric": "0", "ip.route.next.hop": "*************", "ip.route.protocol": "NetMgmt", "ip.route.subnet.mask": "*************", "ip.route.type": "Indirect", "ip.routing.type": "Static"}, {"ip.route": "***********", "ip.route.interface.index": 3, "ip.route.last.updated": " 0 day 0 hour 0 minute  0 second", "ip.route.last.updated.sec": 0, "ip.route.metric": "3", "ip.route.next.hop": "***********", "ip.route.protocol": "OSPF", "ip.route.subnet.mask": "***************", "ip.route.type": "Indirect"}, {"ip.route": "***********", "ip.route.interface.index": 2, "ip.route.last.updated": " 0 day 0 hour 59 minutes 32 seconds", "ip.route.last.updated.sec": 3572, "ip.route.metric": "0", "ip.route.next.hop": "0.0.0.0", "ip.route.protocol": "Local", "ip.route.subnet.mask": "***************", "ip.route.type": "Direct"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}}