{"**********": {"errors": [], "object.ip": "**********", "oid.group.converters": {".*******.*******.********.1.2": {"1": "up", "2": "down"}}, "oid.group.oids": {".*******.*******.********.1.2": "vlan.state", ".*******.*******.********.1.4": "vlan.name"}, "oid.group.parent.oid": "interface.name", "oid.group.type": "tabular", "port": 161, "result": {"interface.name": [{"vlan.name": "default", "vlan.state": "up"}, {"vlan.name": "1st_Flr", "vlan.state": "up"}, {"vlan.name": "3rd_Flr", "vlan.state": "up"}, {"vlan.name": "cctv", "vlan.state": "up"}, {"vlan.name": "fddi-default", "vlan.state": "up"}, {"vlan.name": "fddinet-default", "vlan.state": "up"}, {"vlan.name": "trnet-default", "vlan.state": "up"}, {"vlan.name": "VLAN0040", "vlan.state": "up"}, {"vlan.name": "VLAN0050", "vlan.state": "up"}, {"vlan.name": "Wi-Fi", "vlan.state": "up"}, {"vlan.name": "QA_8.0", "vlan.state": "up"}, {"vlan.name": "QA_Testing", "vlan.state": "up"}, {"vlan.name": "token-ring-default", "vlan.state": "up"}]}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844584149", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587765], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844584150", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:07.135 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800623994, "event.timestamp": 1654845645, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587809, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587765, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "<PERSON><PERSON><PERSON> In<PERSON>", "metric.object": 58829800587766, "metric.plugin": "snmptabularmetric", "metric.polling.min.time": 150, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:50:37.77 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "bgp2.bgp2.com", "object.id": 3, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "bgp2.bgp2.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "oid.group.converters": {".*******.*******.********": {"1": "Not Specified", "2": "Up", "3": "Down", "4": "Standby"}}, "oid.group.device.type": "SNMP Device", "oid.group.id": "82c35b78-e1d3-4929-b37c-769669b0f587", "oid.group.name": "<PERSON><PERSON><PERSON> In<PERSON>", "oid.group.oids": {".*******.*******.********": "chassis.slot", ".*******.*******.********": "chassis.slot.status"}, "oid.group.parent.oid": "chassis.slot", "oid.group.polling.interval.sec": 600, "oid.group.polling.timeout.sec": 60, "oid.group.type": "tabular", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 503, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"chassis.slot": [{"chassis.slot": "7206VXR", "chassis.slot.status": "Up"}, {"chassis.slot": "I/O FastEthernet (TX-ISL)", "chassis.slot.status": "Up"}, {"chassis.slot": "Dual Port FastEthernet (RJ45)", "chassis.slot.status": "Up"}]}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844590141", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587771], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844590142", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:13.468 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624048, "event.timestamp": 1654845765, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587819, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587771, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Voltage Sensor", "metric.object": 58829800587772, "metric.plugin": "snmptabularmetric", "metric.polling.min.time": 150, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:52:38.346 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "ospf2.ospf2.com", "object.id": 5, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf2.ospf2.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "oid.group.converters": {".*******.*******.********.7": {"1": "Normal", "2": "Warning", "3": "Critical", "4": "Shutdown", "5": "Not Present", "6": "Not Functioning"}}, "oid.group.device.type": "SNMP Device", "oid.group.id": "3c2fa111-f768-4d84-af4b-6a66a00902fa", "oid.group.name": "Voltage Sensor", "oid.group.oids": {".*******.*******.********.2": "voltage.sensor", ".*******.*******.********.3": "voltage.sensor.reading.mill.volts", ".*******.*******.********.7": "voltage.sensor.state"}, "oid.group.parent.oid": "voltage.sensor", "oid.group.polling.interval.sec": 600, "oid.group.polling.timeout.sec": 60, "oid.group.type": "tabular", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 504, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"voltage.sensor": [{"voltage.sensor": "+3.45 V", "voltage.sensor.reading.mill.volts": 3437, "voltage.sensor.state": "Normal"}, {"voltage.sensor": "+5.15 V", "voltage.sensor.reading.mill.volts": 5131, "voltage.sensor.state": "Normal"}, {"voltage.sensor": "+12.15 V", "voltage.sensor.reading.mill.volts": 12105, "voltage.sensor.state": "Normal"}, {"voltage.sensor": "-11.95 V", "voltage.sensor.reading.mill.volts": -11905, "voltage.sensor.state": "Normal"}]}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844593464", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587774], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844593469", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:16.814 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624077, "event.timestamp": 1654845825, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587828, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587774, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Temperature Sensor", "metric.object": 58829800587775, "metric.plugin": "snmptabularmetric", "metric.polling.min.time": 150, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:53:38.985 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "ospf3.ospf3.com", "object.id": 6, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf3.ospf3.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "oid.group.converters": {".*******.*******.********.6": {"1": "Normal", "2": "Warning", "3": "Critical", "4": "Shutdown", "5": "Not Present", "6": "Not Functioning"}}, "oid.group.device.type": "SNMP Device", "oid.group.id": "3c2fa111-f767-4d84-af4b-6a66a00902fa", "oid.group.name": "Temperature Sensor", "oid.group.oids": {".*******.*******.********.2": "temperature.sensor", ".*******.*******.********.3": "temperature.sensor.reading.celsius", ".*******.*******.********.6": "temperature.sensor.state"}, "oid.group.parent.oid": "temperature.sensor", "oid.group.polling.interval.sec": 600, "oid.group.polling.timeout.sec": 60, "oid.group.type": "tabular", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 506, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"temperature.sensor": [{"temperature.sensor": "I/O Cont Inlet", "temperature.sensor.reading.celsius": 22, "temperature.sensor.state": "Normal"}, {"temperature.sensor": "I/O Cont Outlet", "temperature.sensor.reading.celsius": 22, "temperature.sensor.state": "Normal"}, {"temperature.sensor": "NPE Inlet", "temperature.sensor.reading.celsius": 22, "temperature.sensor.state": "Normal"}, {"temperature.sensor": "NPE Outlet", "temperature.sensor.reading.celsius": 22, "temperature.sensor.state": "Normal"}]}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844599761", "credential.profile.protocol": "SNMP V3", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587780], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844599762", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:23.133 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624136, "event.timestamp": 1654845885, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587845, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800587780, "metric.credential.profile.protocol": "SNMP V3", "metric.discovery.method": "REMOTE", "metric.name": "Voltage Sensor", "metric.object": 58829800587781, "metric.plugin": "snmptabularmetric", "metric.polling.min.time": 150, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:54:40.317 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "ospf1.ospf1.com", "object.id": 8, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf1.ospf1.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "oid.group.converters": {".*******.*******.********.7": {"1": "Normal", "2": "Warning", "3": "Critical", "4": "Shutdown", "5": "Not Present", "6": "Not Functioning"}}, "oid.group.device.type": "SNMP Device", "oid.group.id": "3c2fa111-f768-4d84-af4b-6a66a00902fa", "oid.group.name": "Voltage Sensor", "oid.group.oids": {".*******.*******.********.2": "voltage.sensor", ".*******.*******.********.3": "voltage.sensor.reading.mill.volts", ".*******.*******.********.7": "voltage.sensor.state"}, "oid.group.parent.oid": "voltage.sensor", "oid.group.polling.interval.sec": 600, "oid.group.polling.timeout.sec": 60, "oid.group.type": "tabular", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 504, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"voltage.sensor": [{"voltage.sensor": "+3.45 V", "voltage.sensor.reading.mill.volts": 3437, "voltage.sensor.state": "Normal"}, {"voltage.sensor": "+5.15 V", "voltage.sensor.reading.mill.volts": 5131, "voltage.sensor.state": "Normal"}, {"voltage.sensor": "+12.15 V", "voltage.sensor.reading.mill.volts": 12105, "voltage.sensor.state": "Normal"}, {"voltage.sensor": "-11.95 V", "voltage.sensor.reading.mill.volts": -11905, "voltage.sensor.state": "Normal"}]}, "snmp.authentication.password": "ospf1md5", "snmp.authentication.protocol": "MD5", "snmp.security.level": "Authentication No Privacy", "snmp.security.user.name": "ospf1md5", "snmp.version": "v3", "status": "succeed", "timeout": 60}, "************": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844607145", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587786], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844607146", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:31.505 pm 10/06/2022", "discovery.target": "************", "discovery.target.name": "************", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624254, "event.timestamp": 1654845945, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587979, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800587786, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "<PERSON>", "metric.object": 58829800587787, "metric.plugin": "snmptabularmetric", "metric.polling.min.time": 150, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:55:41.954 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000004, 10000000000013], "object.host": "cisco2960.motadata.local", "object.id": 10, "object.ip": "************", "object.make.model": "Cisco Catalyst 2960 Series", "object.name": "cisco2960.motadata.local", "object.snmp.device.catalog": 58829800538399, "object.state": "ENABLE", "object.system.oid": ".*******.*******.697", "object.target": "************", "object.type": "Switch", "object.vendor": "Cisco Systems", "oid.group.converters": {".*******.*******.********.3": {"1": "Normal", "2": "Warning", "3": "Critical", "4": "Shutdown", "5": "Not Present", "6": "Not Functioning"}}, "oid.group.device.type": "SNMP Device", "oid.group.id": "c06aca8b-2db4-4941-a7fb-f45db1d30c46", "oid.group.name": "<PERSON>", "oid.group.oids": {".*******.*******.********.2": "fan.sensor", ".*******.*******.********.3": "fan.sensor.state"}, "oid.group.parent.oid": "fan.sensor", "oid.group.polling.interval.sec": 600, "oid.group.polling.timeout.sec": 60, "oid.group.type": "tabular", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 509, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"fan.sensor": [{"fan.sensor": "Switch#1, Fan#1", "fan.sensor.state": "Normal"}]}, "snmp.community": "public", "snmp.version": "v1", "status": "succeed", "timeout": 60}, "*************": {"_type": "1", "credential.profile.name": "SNMP-Test-1654846121810", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800588090], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654846121812", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:58:45.715 pm 10/06/2022", "discovery.target": "*************", "discovery.target.name": "*************", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624605, "event.timestamp": 1654846135, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800588148, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800588090, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Hardware Sensor", "metric.object": 58829800588091, "metric.plugin": "snmptabularmetric", "metric.polling.min.time": 150, "metric.polling.time": 86400, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:58:47.740 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000048, 10000000000013], "object.host": "hp4202vl-48g-MIMIC8806", "object.id": 13, "object.ip": "*************", "object.make.model": "ProCurve Switch 4202vl-48G", "object.name": "hp4202vl-48g-MIMIC8806", "object.snmp.device.catalog": 58829800527850, "object.state": "ENABLE", "object.system.oid": ".*******.********.*********", "object.target": "*************", "object.type": "Switch", "object.vendor": "Hewlett Packard Enterprise", "oid.group.device.type": "SNMP Device", "oid.group.id": "143fdf6f-c3c4-4b7a-beb1-5af383841867", "oid.group.name": "Hardware Sensor", "oid.group.oids": {".*******.********.*********.6.1.1": "hp.hardware.sensor.index", ".*******.********.*********.6.1.4": "hp.hardware.sensor.state", ".*******.********.*********.6.1.7": "hp.hardware.sensor.description"}, "oid.group.parent.oid": "hp.hardware.sensor.index", "oid.group.polling.interval.sec": 86400, "oid.group.polling.timeout.sec": 60, "oid.group.type": "tabular", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 515, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"hp.hardware.sensor.index": [{"hp.hardware.sensor.description": "<PERSON>", "hp.hardware.sensor.index": 1, "hp.hardware.sensor.state": 4}, {"hp.hardware.sensor.description": "Power Supply 2 Sensor", "hp.hardware.sensor.index": 2, "hp.hardware.sensor.state": 5}, {"hp.hardware.sensor.description": "Power Supply 1 Sensor", "hp.hardware.sensor.index": 3, "hp.hardware.sensor.state": 4}]}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "*************": {"_type": "1", "credential.profile.name": "SNMP-Test-1654846130604", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800588151], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654846130605", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:58:54.513 pm 10/06/2022", "discovery.target": "*************", "discovery.target.name": "*************", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624643, "event.timestamp": 1654846145, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800588208, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800588151, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Hardware Sensor", "metric.object": 58829800588152, "metric.plugin": "snmptabularmetric", "metric.polling.min.time": 150, "metric.polling.time": 86400, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:58:56.534 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000048, 10000000000013], "object.host": "hp4202vl-48g-MIMIC9136", "object.id": 14, "object.ip": "*************", "object.make.model": "ProCurve Switch 4202vl-48G", "object.name": "hp4202vl-48g-MIMIC9136", "object.snmp.device.catalog": 58829800527850, "object.state": "ENABLE", "object.system.oid": ".*******.********.*********", "object.target": "*************", "object.type": "Switch", "object.vendor": "Hewlett Packard Enterprise", "oid.group.device.type": "SNMP Device", "oid.group.id": "143fdf6f-c3c4-4b7a-beb1-5af383841867", "oid.group.name": "Hardware Sensor", "oid.group.oids": {".*******.********.*********.6.1.1": "hp.hardware.sensor.index", ".*******.********.*********.6.1.4": "hp.hardware.sensor.state", ".*******.********.*********.6.1.7": "hp.hardware.sensor.description"}, "oid.group.parent.oid": "hp.hardware.sensor.index", "oid.group.polling.interval.sec": 86400, "oid.group.polling.timeout.sec": 60, "oid.group.type": "tabular", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 515, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"hp.hardware.sensor.index": [{"hp.hardware.sensor.description": "<PERSON>", "hp.hardware.sensor.index": 1, "hp.hardware.sensor.state": 4}, {"hp.hardware.sensor.description": "Power Supply 2 Sensor", "hp.hardware.sensor.index": 2, "hp.hardware.sensor.state": 5}, {"hp.hardware.sensor.description": "Power Supply 1 Sensor", "hp.hardware.sensor.index": 3, "hp.hardware.sensor.state": 4}]}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "*************": {"_type": "1", "credential.profile.name": "SNMP-Test-1654846139370", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800588211], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654846139371", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:59:03.840 pm 10/06/2022", "discovery.target": "*************", "discovery.target.name": "*************", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624708, "event.timestamp": 1654846155, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800588295, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800588211, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Hardware Sensor", "metric.object": 58829800588212, "metric.plugin": "snmptabularmetric", "metric.polling.min.time": 150, "metric.polling.time": 86400, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:59:05.868 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000048, 10000000000013], "object.host": "J4850A-MIMIC9087", "object.id": 15, "object.ip": "*************", "object.make.model": "HP ProCurve Switch 5304XL", "object.name": "J4850A-MIMIC9087", "object.snmp.device.catalog": 58829800561806, "object.state": "ENABLE", "object.system.oid": ".*******.********.*********", "object.target": "*************", "object.type": "Switch", "object.vendor": "Hewlett Packard Enterprise", "oid.group.device.type": "SNMP Device", "oid.group.id": "143fdf6f-c3c4-4b7a-beb1-5af383841867", "oid.group.name": "Hardware Sensor", "oid.group.oids": {".*******.********.*********.6.1.1": "hp.hardware.sensor.index", ".*******.********.*********.6.1.4": "hp.hardware.sensor.state", ".*******.********.*********.6.1.7": "hp.hardware.sensor.description"}, "oid.group.parent.oid": "hp.hardware.sensor.index", "oid.group.polling.interval.sec": 86400, "oid.group.polling.timeout.sec": 60, "oid.group.type": "tabular", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 515, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"hp.hardware.sensor.index": [{"hp.hardware.sensor.description": "<PERSON>", "hp.hardware.sensor.index": 1, "hp.hardware.sensor.state": 4}, {"hp.hardware.sensor.description": "Power Supply 2 Sensor", "hp.hardware.sensor.index": 2, "hp.hardware.sensor.state": 5}, {"hp.hardware.sensor.description": "Power Supply 1 Sensor", "hp.hardware.sensor.index": 3, "hp.hardware.sensor.state": 4}]}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844584149", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587765], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844584150", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:07.135 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800623994, "event.timestamp": 1654845645, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587809, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587765, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "<PERSON><PERSON><PERSON> In<PERSON>", "metric.object": 58829800587766, "metric.plugin": "snmptabularmetric", "metric.polling.min.time": 150, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:50:37.77 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "bgp2.bgp2.com", "object.id": 3, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "bgp2.bgp2.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "oid.group.converters": {".*******.*******.********": {"1": "Not Specified", "2": "Up", "3": "Down", "4": "Standby"}}, "oid.group.device.type": "SNMP Device", "oid.group.id": "82c35b78-e1d3-4929-b37c-769669b0f587", "oid.group.name": "<PERSON><PERSON><PERSON> In<PERSON>", "oid.group.oids": {".*******.*******.********": "chassis.slot", ".*******.*******.********": "chassis.slot.status"}, "oid.group.parent.oid": "chassis.slot", "oid.group.polling.interval.sec": 600, "oid.group.polling.timeout.sec": 60, "oid.group.type": "tabular", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 503, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"chassis.slot": [{"chassis.slot": "7206VXR", "chassis.slot.status": "Up"}, {"chassis.slot": "I/O FastEthernet (TX-ISL)", "chassis.slot.status": "Up"}, {"chassis.slot": "Dual Port FastEthernet (RJ45)", "chassis.slot.status": "Up"}]}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844584149", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587765], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844584150", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:07.135 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800623994, "event.timestamp": 1654845645, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587809, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587765, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "<PERSON><PERSON><PERSON> In<PERSON>", "metric.object": 58829800587766, "metric.plugin": "snmptabularmetric", "metric.polling.min.time": 150, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:50:37.77 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "bgp2.bgp2.com", "object.id": 3, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "bgp2.bgp2.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "oid.group.converters": {".*******.*******.********": {"1": "Not Specified", "2": "Up", "3": "Down", "4": "Standby"}}, "oid.group.device.type": "SNMP Device", "oid.group.id": "82c35b78-e1d3-4929-b37c-769669b0f587", "oid.group.name": "<PERSON><PERSON><PERSON> In<PERSON>", "oid.group.oids": {".*******.*******.********": "chassis.slot", ".*******.*******.********": "chassis.slot.status"}, "oid.group.parent.oid": "chassis.slot", "oid.group.polling.interval.sec": 600, "oid.group.polling.timeout.sec": 60, "oid.group.type": "tabular", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 503, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"chassis.slot": [{"chassis.slot": "7206VXR", "chassis.slot.status": "Up"}, {"chassis.slot": "I/O FastEthernet (TX-ISL)", "chassis.slot.status": "Up"}, {"chassis.slot": "Dual Port FastEthernet (RJ45)", "chassis.slot.status": "Up"}]}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844590141", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587771], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844590142", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:13.468 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624048, "event.timestamp": 1654845765, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587819, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587771, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Voltage Sensor", "metric.object": 58829800587772, "metric.plugin": "snmptabularmetric", "metric.polling.min.time": 150, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:52:38.346 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "ospf2.ospf2.com", "object.id": 5, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf2.ospf2.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "oid.group.converters": {".*******.*******.********.7": {"1": "Normal", "2": "Warning", "3": "Critical", "4": "Shutdown", "5": "Not Present", "6": "Not Functioning"}}, "oid.group.device.type": "SNMP Device", "oid.group.id": "3c2fa111-f768-4d84-af4b-6a66a00902fa", "oid.group.name": "Voltage Sensor", "oid.group.oids": {".*******.*******.********.2": "voltage.sensor", ".*******.*******.********.3": "voltage.sensor.reading.mill.volts", ".*******.*******.********.7": "voltage.sensor.state"}, "oid.group.parent.oid": "voltage.sensor", "oid.group.polling.interval.sec": 600, "oid.group.polling.timeout.sec": 60, "oid.group.type": "tabular", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 504, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"voltage.sensor": [{"voltage.sensor": "+3.45 V", "voltage.sensor.reading.mill.volts": 3437, "voltage.sensor.state": "Normal"}, {"voltage.sensor": "+5.15 V", "voltage.sensor.reading.mill.volts": 5131, "voltage.sensor.state": "Normal"}, {"voltage.sensor": "+12.15 V", "voltage.sensor.reading.mill.volts": 12105, "voltage.sensor.state": "Normal"}, {"voltage.sensor": "-11.95 V", "voltage.sensor.reading.mill.volts": -11905, "voltage.sensor.state": "Normal"}]}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844593464", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587774], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844593469", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:16.814 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624077, "event.timestamp": 1654845825, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587828, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587774, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Temperature Sensor", "metric.object": 58829800587775, "metric.plugin": "snmptabularmetric", "metric.polling.min.time": 150, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:53:38.985 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "ospf3.ospf3.com", "object.id": 6, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf3.ospf3.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "oid.group.converters": {".*******.*******.********.6": {"1": "Normal", "2": "Warning", "3": "Critical", "4": "Shutdown", "5": "Not Present", "6": "Not Functioning"}}, "oid.group.device.type": "SNMP Device", "oid.group.id": "3c2fa111-f767-4d84-af4b-6a66a00902fa", "oid.group.name": "Temperature Sensor", "oid.group.oids": {".*******.*******.********.2": "temperature.sensor", ".*******.*******.********.3": "temperature.sensor.reading.celsius", ".*******.*******.********.6": "temperature.sensor.state"}, "oid.group.parent.oid": "temperature.sensor", "oid.group.polling.interval.sec": 600, "oid.group.polling.timeout.sec": 60, "oid.group.type": "tabular", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 506, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"temperature.sensor": [{"temperature.sensor": "I/O Cont Inlet", "temperature.sensor.reading.celsius": 22, "temperature.sensor.state": "Normal"}, {"temperature.sensor": "I/O Cont Outlet", "temperature.sensor.reading.celsius": 22, "temperature.sensor.state": "Normal"}, {"temperature.sensor": "NPE Inlet", "temperature.sensor.reading.celsius": 22, "temperature.sensor.state": "Normal"}, {"temperature.sensor": "NPE Outlet", "temperature.sensor.reading.celsius": 22, "temperature.sensor.state": "Normal"}]}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844599761", "credential.profile.protocol": "SNMP V3", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587780], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844599762", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:23.133 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624136, "event.timestamp": 1654845885, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587845, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800587780, "metric.credential.profile.protocol": "SNMP V3", "metric.discovery.method": "REMOTE", "metric.name": "Voltage Sensor", "metric.object": 58829800587781, "metric.plugin": "snmptabularmetric", "metric.polling.min.time": 150, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:54:40.317 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "ospf1.ospf1.com", "object.id": 8, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf1.ospf1.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "oid.group.converters": {".*******.*******.********.7": {"1": "Normal", "2": "Warning", "3": "Critical", "4": "Shutdown", "5": "Not Present", "6": "Not Functioning"}}, "oid.group.device.type": "SNMP Device", "oid.group.id": "3c2fa111-f768-4d84-af4b-6a66a00902fa", "oid.group.name": "Voltage Sensor", "oid.group.oids": {".*******.*******.********.2": "voltage.sensor", ".*******.*******.********.3": "voltage.sensor.reading.mill.volts", ".*******.*******.********.7": "voltage.sensor.state"}, "oid.group.parent.oid": "voltage.sensor", "oid.group.polling.interval.sec": 600, "oid.group.polling.timeout.sec": 60, "oid.group.type": "tabular", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 504, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"chassis.slot": [{"chassis.slot": "7206VXR", "chassis.slot.status": "Up"}, {"chassis.slot": "I/O FastEthernet (TX-ISL)", "chassis.slot.status": "Up"}, {"chassis.slot": "Ethernet", "chassis.slot.status": "Up"}], "power.supply.sensor": [{"power.supply.sensor": "AC Power Supply", "power.supply.sensor.status": "Normal"}, {"power.supply.sensor": "AC Power Supply", "power.supply.sensor.status": "Normal"}], "voltage.sensor": [{"voltage.sensor": "+3.45 V", "voltage.sensor.reading.mill.volts": 3437, "voltage.sensor.status": "Normal"}, {"voltage.sensor": "+5.15 V", "voltage.sensor.reading.mill.volts": 5131, "voltage.sensor.status": "Normal"}, {"voltage.sensor": "+12.15 V", "voltage.sensor.reading.mill.volts": 12105, "voltage.sensor.status": "Normal"}, {"voltage.sensor": "-11.95 V", "voltage.sensor.reading.mill.volts": -11905, "voltage.sensor.status": "Normal"}], "temperature.sensor": [{"temperature.sensor": "I/O Cont Inlet", "temperature.sensor.reading.celsius": 22, "temperature.sensor.status": "Normal"}, {"temperature.sensor": "I/O Cont Outlet", "temperature.sensor.reading.celsius": 22, "temperature.sensor.status": "Normal"}, {"temperature.sensor": "NPE Inlet", "temperature.sensor.reading.celsius": 22, "temperature.sensor.status": "Normal"}, {"temperature.sensor": "NPE Outlet", "temperature.sensor.reading.celsius": 22, "temperature.sensor.status": "Normal"}]}, "snmp.authentication.password": "ospf1md5", "snmp.authentication.protocol": "MD5", "snmp.security.level": "Authentication No Privacy", "snmp.security.user.name": "ospf1md5", "snmp.version": "v3", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844584149", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587765], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844584150", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:07.135 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800623994, "event.timestamp": 1654845645, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587809, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587765, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "<PERSON><PERSON><PERSON> In<PERSON>", "metric.object": 58829800587766, "metric.plugin": "snmptabularmetric", "metric.polling.min.time": 150, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:50:37.77 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "bgp2.bgp2.com", "object.id": 3, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "bgp2.bgp2.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "oid.group.converters": {".*******.*******.********": {"1": "Not Specified", "2": "Up", "3": "Down", "4": "Standby"}}, "oid.group.device.type": "SNMP Device", "oid.group.id": "82c35b78-e1d3-4929-b37c-769669b0f587", "oid.group.name": "<PERSON><PERSON><PERSON> In<PERSON>", "oid.group.oids": {".*******.*******.********": "chassis.slot", ".*******.*******.********": "chassis.slot.status"}, "oid.group.parent.oid": "chassis.slot", "oid.group.polling.interval.sec": 600, "oid.group.polling.timeout.sec": 60, "oid.group.type": "tabular", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 503, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"chassis.slot": [{"chassis.slot": "7206VXR", "chassis.slot.status": "Up"}, {"chassis.slot": "I/O FastEthernet (TX-ISL)", "chassis.slot.status": "Up"}, {"chassis.slot": "Dual Port FastEthernet (RJ45)", "chassis.slot.status": "Up"}]}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}}