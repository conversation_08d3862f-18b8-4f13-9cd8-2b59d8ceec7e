[{"oid.group.id": "8847662b-6c51-482c-98c3-fa271981ff52", "oid.group.device.type": "SNMP Device", "oid.group.type": "scalar", "oid.group.name": "CPU & Memory Statistics", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 300, "oid.group.oids": {"(.*******.4.1.2636.********.*******.0 + .*******.4.1.2636.********.*******.0)": "system.cpu.percent", "(.*******.4.1.2636.********.********.0 + .*******.4.1.2636.********.********.0)": "system.memory.used.percent"}}, {"oid.group.id": "114a727a-0878-4f82-aedc-c61049908d0a", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "<PERSON>", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.parent.oid": ".*******.4.1.2636.********.5.4", "oid.group.oids": {".*******.4.1.2636.********.5.4": "fan.sensor", ".*******.4.1.2636.********.6.4": "fan.sensor.status"}, "oid.group.converters": {".*******.4.1.2636.********.6.4": {"1": "Unknown", "2": "Running", "3": "Ready", "4": "Reset", "5": "Running Full Speed", "6": "Down", "7": "Standby"}}}, {"oid.group.id": "6fc1e189-d0c9-491d-bdc5-43e88c92af2d", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Power Supply Sensor", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.parent.oid": ".*******.4.1.2636.********.5.2", "oid.group.oids": {".*******.4.1.2636.********.5.2": "power.supply.sensor", ".*******.4.1.2636.********.6.2": "power.supply.sensor.status"}, "oid.group.converters": {".*******.4.1.2636.********.6.2": {"1": "Unknown", "2": "Running", "3": "Ready", "4": "Reset", "5": "Running Full Speed", "6": "Down", "7": "Standby"}}}, {"oid.group.id": "abea2679-1951-49e3-89eb-e3f76ae59db0", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.name": "Flexible PIC Concentrators (FPC)", "oid.group.parent.oid": ".*******.4.1.2636.********.5.7", "oid.group.oids": {".*******.4.1.2636.********.5.7": "juniper.fpc", ".*******.4.1.2636.********.6.7": "juniper.fpc.state", ".*******.4.1.2636.********.7.7": "juniper.fpc.temperature.reading.celsius"}, "oid.group.converters": {".*******.4.1.2636.********.6.7": {"1": "Unknown", "2": "Running", "3": "Ready", "4": "Reset", "5": "Running Full Speed", "6": "Down", "7": "Standby"}}}, {"oid.group.id": "d4308693-7fe9-4964-a50c-a48222df1b54", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.name": "Field Replaceable Units (FRU)", "oid.group.parent.oid": ".*******.4.1.2636.********.5", "oid.group.oids": {".*******.4.1.2636.********.5": "juniper.fru", ".*******.4.1.2636.********.6": "juniper.fru.type", ".*******.4.1.2636.********.8": "juniper.fru.state", ".*******.4.1.2636.********.9": "juniper.fru.temperature.celsius", "(0.01*.*******.4.1.2636.********.13)": "juniper.fru.uptime.seconds", ".*******.4.1.2636.********.14": "juniper.fru.chassis", ".*******.4.1.2636.********.15": "juniper.fru.chassis.description"}, "oid.group.converters": {".*******.4.1.2636.********.6": {"1": "Other", "2": "Clock Generator", "3": "Flexible Pic Concentrator", "4": "Switching Forwarding Module", "5": "Control Board", "6": "Routing Engine", "7": "Power Entry Module", "8": "Front Panel Module", "9": "Switch Interface Board", "10": "Processor Mezzanine Board", "11": "Port Interface Card", "12": "Craft Interface Panel", "13": "Fan", "14": "Line Card Chassis", "15": "Forwarding Engine Board", "16": "Protected System Domain"}, ".*******.4.1.2636.********.8": {"1": "Unknown", "2": "Empty", "3": "Present", "4": "Ready", "5": "Announce Online", "6": "Online", "7": "Announce Offline", "8": "Offline", "9": "Diagnostic", "10": "Standby"}}}, {"oid.group.id": "cb5ae67c-bd3a-4edf-80ca-1245d67aa8fe", "oid.group.device.type": "SNMP Device", "oid.group.type": "scalar", "oid.group.name": "Routing Engine Statistics", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 300, "oid.group.oids": {".*******.4.1.2636.********.*******.0": "juniper.routing.engine.1.cpu.percent", ".*******.4.1.2636.********.*******.0": "juniper.routing.engine.2.cpu.percent", ".*******.4.1.2636.********.********.0": "juniper.routing.engine.1.memory.used.percent", ".*******.4.1.2636.********.********.0": "juniper.routing.engine.2.memory.used.percent", ".*******.4.1.2636.********.*******.0": "juniper.routing.engine.1.temperature.reading.celsius", ".*******.4.1.2636.********.*******.0": "juniper.routing.engine.2.temperature.reading.celsius"}}, {"oid.group.id": "d370ec7b-a184-41e1-b84d-fc2139a3b9b9", "oid.group.device.type": "SNMP Device", "oid.group.typ": "tabular", "oid.group.name": "Virtual Chassis Info", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.parent.oid": ".*******.4.1.2636.********.*******", "oid.group.oids": {".*******.4.1.2636.********.*******": "juniper.virtual.chassis", ".*******.4.1.2636.********.*******": "juniper.virtual.chassis.role", ".*******.4.1.2636.********.*******": "juniper.virtual.chassis.mac.address", ".*******.4.1.2636.********.*******": "juniper.virtual.chassis.version", ".*******.4.1.2636.********.*******": "juniper.virtual.chassis.priority", ".*******.4.1.2636.********.*******": "juniper.virtual.chassis.started.time.seconds", ".*******.4.1.2636.********.*******": "juniper.virtual.chassis.model", ".*******.4.1.2636.********.*******": "juniper.virtual.chassis.location"}}, {"oid.group.id": "490da18b-3ee2-4d02-8cf4-f8ba6b8761cb", "oid.group.device.type": "SNMP Device", "oid.group.type": "scalar", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 10800, "oid.group.name": "System Info", "oid.group.oids": {".*******.4.1.2636.*******": "system.model", ".*******.4.1.2636.*******": "system.serial.number"}}]