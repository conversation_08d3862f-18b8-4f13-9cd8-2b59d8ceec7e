/*
 * Copyright (c) Motadata 2025. All rights reserved.
 */

package main

import (
	"fmt"
	"github.com/kardianos/service"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

const (
	PathSeparator = string(os.PathSeparator)

	LogDirectory = "logs"

	ServiceName = "Motadata Upgrader : 8.0.5"

	ServiceDescription = "this service is used to upgrade motadata manager"

	UpgradeFile = "upgrade.me"

	WindowsSeparator = `\`

	LinuxManagerUpgraderScript = "manager-upgrader.sh"

	WindowsManagerUpgraderScript = "manager-upgrader.bat"
)

var (
	CurrentDir string

	shutdownEvents chan bool
)

type program struct{}

func (program program) Start(s service.Service) error {

	if err := os.Chdir(filepath.Dir(os.Args[0])); err != nil {
		return err
	}

	CurrentDir, _ = os.Getwd()

	dir, err := filepath.Abs(filepath.Dir(CurrentDir + PathSeparator + LogDirectory + PathSeparator))

	if err != nil {
		return err
	}

	if _, err := os.Stat(dir); os.IsNotExist(err) {

		if err = os.MkdirAll(dir, 0666); err != nil {
			return err
		}

	}

	logPath := filepath.Join(dir, "upgrader.log")

	file, err := os.OpenFile(logPath, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)

	if err != nil {
		return err
	}

	log.SetOutput(file)

	log.Println(s.String() + " started")

	shutdownEvents = make(chan bool)

	go run()

	return nil
}

func run() {

	for {

		select {

		case <-time.After(10 * time.Second):

			var err error

			if _, err = os.Stat(CurrentDir + PathSeparator + UpgradeFile); !os.IsNotExist(err) {

				if bytes, _ := os.ReadFile(CurrentDir + PathSeparator + UpgradeFile); len(string(bytes)) > 0 && len(strings.Split(string(bytes), ":")) == 1 {

					var script string

					if script = "bash " + LinuxManagerUpgraderScript; PathSeparator == WindowsSeparator {

						script = WindowsManagerUpgraderScript

					}

					_, err = executeCommand(script, true, false, 1000)

					if err != nil {

						log.Printf("error executing script %v\n", err.Error())

					}
				}
			}

		case shutdown := <-shutdownEvents:

			if shutdown {

				return
			}
		}
	}
}

func (program program) Stop(s service.Service) error {

	shutdownEvents <- true

	log.Println(s.String() + " stopped")

	return nil
}

func main() {

	serviceConfig := &service.Config{
		Name:        ServiceName,
		DisplayName: ServiceName,
		Description: ServiceDescription,
	}

	p := &program{}

	s, err := service.New(p, serviceConfig)

	if err != nil {

		log.Println("Cannot create the service: " + err.Error())
	}

	err = s.Run()

	if err != nil {

		log.Println("Cannot start the service: " + err.Error())
	}

}

func executeCommand(cmd string, useShell, output bool, timeout int) (string, error) {

	var shell, flag string

	var err error

	var bytes []byte

	var command *exec.Cmd

	response := make(chan []byte)

	errors := make(chan error)

	if command = exec.Command(cmd); useShell {

		if shell, flag = "/bin/sh", "-c"; PathSeparator == WindowsSeparator {

			shell, flag = "cmd.exe", "/c"

		}

		command = exec.Command(shell, flag, cmd)
	}

	log.Printf("Executing command : %v\n", command)

	// used to kill the process in case of timeout
	go func() {

		if output {

			bytes, err := command.CombinedOutput()

			if err != nil {

				errors <- err

			} else {

				response <- bytes

			}

		} else {

			errors <- command.Run()

		}

	}()

	select {

	case <-time.After(time.Duration(timeout) * time.Second):

		err = fmt.Errorf("Timeout occurred in the command : %v ", cmd)

		log.Printf("Killing process having command %v due to timeout\n", cmd)

		_ = command.Process.Kill()

	case bytes = <-response:

	case err = <-errors:

	}

	return strings.Trim(strings.TrimSpace(string(bytes)), "\n"), err
}
